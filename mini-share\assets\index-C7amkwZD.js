import{r as n,y as l,Z as u,G as d,H as m,j as e,c as p,o as h,f as _,X as g}from"./index-DDqcBwar.js";const f=["src"],y={__name:"index",props:{src:{type:String,required:!0}},setup(s){const r=s,t=n(document.documentElement.clientHeight-94.5+"px;"),o=n(!0),c=l(()=>r.src);return u(()=>{setTimeout(()=>{o.value=!1},300),window.onresize=function(){t.value=document.documentElement.clientHeight-94.5+"px;"}}),(i,v)=>{const a=d("loading");return m((h(),p("div",{style:g("height:"+e(t))},[_("iframe",{src:e(c),frameborder:"no",style:{width:"100%",height:"100%"},scrolling:"auto"},null,8,f)],4)),[[a,e(o)]])}}};export{y as _};
