<!-- 
 * @description: k库存管理
 * @fileName: index.vue 
 * @author: 李文滔 
 * @date: 2025-04-02 21:18:57 
 * @path:  
 * @version: V1.0.0 
!-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="锁类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择锁类型"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="item in locking_type"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="设备编号" prop="lockNo">
        <el-input
          v-model="queryParams.lockNo"
          placeholder="请输入设备编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车场" prop="parkName">
        <el-input
          v-model="queryParams.parkName"
          placeholder="请输入车场"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="订单" prop="useStatus">
        <el-select
          v-model="queryParams.useStatus"
          placeholder="请选择订单状态"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="item in orderStatus"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="锁状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择锁状态"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="item in status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="ifOnline">
        <el-select
          v-model="queryParams.ifOnline"
          placeholder="请选择状态"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="item in onlineStatus"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8"> </el-row>

    <el-row :gutter="10" class="mb8">
      <el-button
        type="primary"
        plain
        icon="Plus"
        @click="handleAdd"
        v-hasPermi="['device:lock:add']"
        >新增</el-button
      >
    </el-row>

    <el-table
      v-loading="loading"
      :data="tagList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column label="锁类型" align="center" prop="type">
        <template #default="scope">
          <el-tag v-if="scope.row.type == 1" type="success">个人</el-tag>
          <el-tag v-else-if="scope.row.type == 2" type="info"
            >物业</el-tag
          >
        </template>
      </el-table-column>

      <el-table-column
        label="设备编号"
        align="center"
        prop="lockNo"
        width="200px"
      />
      <el-table-column
        label="车场"
        align="center"
        prop="parkName"
        width="200px"
      />
      <el-table-column
        label="地址"
        align="center"
        prop="parkAddress"
        width="200px"
      />
      <el-table-column
        label="区域"
        align="center"
        prop="location"
        width="200px"
      />
      <el-table-column label="车位号" align="center" prop="code" />
      <el-table-column
        label="手机号"
        align="center"
        prop="phone"
        width="200px"
      />

      <el-table-column
        label="订单"
        align="center"
        prop="useStatus"
        width="100px"
      >
        <template #default="scope">
          <dict-tag
            :options="orderStatus"
            :value="scope.row.useStatus"
          ></dict-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="电量"
        align="center"
        prop="electricQuantity"
        width="100px"
      />
      <el-table-column label="设备版本" align="center" prop="lockVersion" />
      <el-table-column
        label="最后通讯时间"
        align="center"
        prop="lastRespTime"
        width="200px"
      />
      <el-table-column label="锁状态" align="center" prop="status">
        <template #default="scope">
          <el-tag v-if="scope.row.status == 1" type="success">升锁</el-tag>
          <el-tag v-else-if="scope.row.status == 0" type="info"
            >降锁</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="ifOnline">
        <template #default="scope">
          <el-tag v-if="scope.row.ifOnline == 1" type="success">在线</el-tag>
          <el-tag v-else-if="scope.row.ifOnline == 0" type="info">离线</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Tickets"
            @click="handleLink(scope.row)"
            v-hasPermi="['device:order:lot']"
            >订单</el-button
          >
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['device:lock:update']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Tickets"
            @click="handleControl(scope.row)"
            v-hasPermi="['device:lock:operate']"
            >控制</el-button
          >
          <el-button
            link
            type="primary"
            icon="Tickets"
            v-hasPermi="['device:lock:release']"
            @click="handleLift(scope.row)"
            >解绑</el-button
          >

          <el-button
            link
            type="primary"
            icon="Tickets"
            v-hasPermi="['device:lock:qrcode']"
            @click="handleQrcode(scope.row)"
            >二维码</el-button
          >
          <el-button
            link
            type="primary"
            v-hasPermi="['device:locking:share']"
            icon="Tickets"
            @click="handleShare(scope.row)"
            >共享</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改库存对话框 -->
    <el-dialog :title="title" v-model="open" width="880px" append-to-body>
      <el-form ref="lockRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="锁类型" prop="type">
              <el-select v-model="form.type">
                <el-option
                  v-for="(dict, i) in locking_type"
                  :key="i"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备编号" prop="lockNo">
              <el-select
                v-model.string="form.inventoryId"
                clearable
                reserve-keyword
                remote
                filterable
                :remote-method="getListInventory"
                @change="inventoryChange"
              >
                <el-option
                  v-for="(dict, i) in inventoryData"
                  :key="i"
                  :label="dict.lockNo"
                  :value="dict.inventoryId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入手机号"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="车场" prop="parkId">
              <el-select
                v-model="form.parkId"
                clearable
                reserve-keyword
                remote
                filterable
                @change="parkChange"
              >
                <el-option
                  v-for="(dict, i) in parkList"
                  :key="i"
                  :label="dict.parkName"
                  :value="dict.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地址" prop="parkAddress">
              <el-input
                v-model="form.parkAddress"
                placeholder="车场地址"
                :disabled="true"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="区域" prop="location">
              <el-input
                v-model="form.location"
                placeholder="请输入区域"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车位号" prop="code">
              <el-input
                v-model="form.code"
                placeholder="请输入车位号"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 设备二维码 -->
    <el-dialog title="二维码" v-model="openUrl" width="600px">
      <el-image
        style="width: 100%; height: 100%"
        :src="imageUrl"
        fit="contain"
        v-if="imageUrl"
      ></el-image>
      <div class="right-grid">
        <el-button type="primary" @click="downloadQrcode">下载二维码</el-button>
      </div>
    </el-dialog>
    <!-- 控制 -->
    <el-dialog title="设备控制" v-model="controlOpen" width="600px">
      <el-form ref="controlRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备编号" prop="lockNo">
              <el-input
                v-model="form.lockNo"
                placeholder="请输入设备编号"
                :disabled="true"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :value="0">关锁</el-radio>
                <el-radio :value="1">开锁</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitControlForm">确 定</el-button>
          <el-button @click="controlOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 共享方案 -->
    <el-dialog title="共享方案" v-model="shareOpen" width="600px">
      <el-form ref="shareRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="重复共享" prop="cycleType">
              <el-select v-model="form.cycleType">
                <el-option
                  v-for="(dict, i) in rent_type"
                  :key="i"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="shareStartTime">
              <el-time-picker
                v-model="form.shareStartTime"
                value-format="HH:mm:ss"
                format="HH:mm"
                type="datetime"
                placeholder="选择共享开始时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="shareEndTime">
              <el-time-picker
                v-model="form.shareEndTime"
                value-format="HH:mm:ss"
                format="HH:mm"
                type="datetime"
                placeholder="选择共享结束时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="单价" prop="price">
              <el-input
                v-model.number="form.price"
                placeholder="请输入单价"
                clearable
                type="number"
              >
                <template #append>元/每分钟</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="封顶价格" prop="capAmount">
              <el-input
                v-model.number="form.capAmount"
                placeholder="请输入封顶价格"
                clearable
                type="number"
              >
                <template #append>元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="经营类型" prop="businessType">
              <el-radio-group v-model="form.businessType">
                <el-radio :value="1">个体经营</el-radio>
                <el-radio :value="2">参与物业优惠</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitShareForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Notice">
import {
  listLocking,
  addLocking,
  updateLocking,
  operateLock,
  getLocking,
  unbindLock,
  addOrUpdateShare,
  getShareInfo,
  listDevice,
  getParkList,
} from "@/api/deveice/locking";
// import { listInventory } from "@/api/deveice/inventory";
// import { listPark } from "@/api/system/park";
import request from "@/utils/request";
import { tansParams, blobValidate } from "@/utils/ruoyi";
import { ElMessage, ElMessageBox } from "element-plus";
import { saveAs } from "file-saver";
import { useRouter } from "vue-router";
const { proxy } = getCurrentInstance();
const router = useRouter();
const tagList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const inventoryData = ref([]);
const parkList = ref([]); //车场列表
const openUrl = ref(false); //二维码弹窗
const imageUrl = ref(""); //二维码链接
const controlOpen = ref(false); //控制弹窗
const shareOpen = ref(false); //共享弹窗

const data = reactive({
  form: {},
  shareForm: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    type: undefined,
    lockNo: undefined,
    parkName: undefined,
    phone: undefined,
    useStatus: undefined,
    status: undefined,
    ifOnline: undefined,
  },
  rules: {
    lockNo: [
      { required: true, message: "设备编号不能为空", trigger: "blur" },
    ],
    parkId: [{ required: true, message: "车场不能为空", trigger: "blur" }],
    location: [{ required: true, message: "区域不能为空", trigger: "blur" }],
    code: [
      { required: true, message: "车位号不能为空", trigger: "blur" },
    ],

    phone: [{ required: true, message: "手机号不能为空", trigger: "blur" }],
    price: [
      { required: true, message: "单价不能为空", trigger: "blur" },
      {
        // 只能是大于0的数字
        pattern: /^[1-9]\d*$/,
        message: "单价需为大于零的整数",
        trigger: "blur",
      },
    ],
    capAmount: [
      { required: true, message: "封顶价格不能为空", trigger: "blur" },
      {
        pattern: /^[1-9]\d*$/,
        message: "封顶价需为大于零的整数",
        trigger: "blur",
      },
    ],
    cycleType: [
      { required: true, message: "重复共享不能为空", trigger: "change" },
    ],
    shareStartTime: [
      { required: true, message: "开始时间不能为空", trigger: "blur" },
    ],
    shareEndTime: [
      { required: true, message: "结束时间不能为空", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (value && form.value.shareStartTime > value) {
            callback(new Error("结束时间必须大于开始时间"));
          } else {
            callback();
          }
        },
      },
    ],
    businessType: [
      { required: true, message: "经营类型不能为空", trigger: "change" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  listLocking(queryParams.value).then((response) => {
    tagList.value = response.rows;
    total.value = parseInt(response.total);
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  controlOpen.value = false;
  shareOpen.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
    // status: "0",
  };
  proxy.resetForm("lockRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加车位锁";
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset();
  open.value = true;
  title.value = "修改车位锁";
  getLocking({
    lockId: row.lockId,
  }).then((response) => {
    form.value = response.data;
    form.value.location = response.data.location;
    form.value.code = response.data.code;
    form.value.parkId = Number(response.data.parkId);
    form.value.inventoryId = response.data.inventoryId;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["lockRef"].validate((valid) => {
    if (valid) {
      if (form.value.lotId != undefined) {
        updateLocking(form.value).then((response) => {
          proxy.$modal.msgSuccess("操作成功");
          open.value = false;
          getList();
        });
      } else {
        addLocking(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

//查询设备生成的二维码
function handleQrcode(row) {
  request
    .get(
      "/device/lock/qrcode",
      {
        params: {
          lockNo: row.lockNo,
        },
        responseType: "blob",
      },
      { isToken: true }
    )
    .then((res) => {
      const isBlob = blobValidate(res);
      if (isBlob) {
        let blob = new Blob([res], { type: "image/png" });
        imageUrl.value = URL.createObjectURL(blob);
        openUrl.value = true;
      }
    })
    .catch(() => {});
}
// 下载二维码
function downloadQrcode() {
  if (imageUrl.value) {
    saveAs(imageUrl.value, "二维码.png");
  }
}
// 控制
function handleControl(row) {
  controlOpen.value = true;
  form.value = row;
}
// 控制提交
function submitControlForm() {
  proxy.$refs["controlRef"].validate((valid) => {
    if (valid) {
      operateLock(form.value).then((response) => {
        proxy.$modal.msgSuccess("修改成功");
        controlOpen.value = false;
        getList();
      });
    }
  });
}
// 解绑
function handleLift(row) {
  ElMessageBox.confirm(
    "您正在解除与设备的绑定关系，绑定后设备将无用户信息，进入库存。",
    "提示",
    {
      type: "warning",
    }
  )
    .then(() => {
      unbindLock(row.lockId).then((response) => {
        proxy.$modal.msgSuccess("解绑成功");
        getList();
      });
    })
    .catch(() => {});
}
// 共享
function handleShare(row) {
  getShareInfo(row.lotId).then((response) => {
    form.value['lotId'] = String(row.lotId);
    if (response.data.id) {
      form.value = response.data;
    } 
    shareOpen.value = true;
  });
}
function submitShareForm() {
  proxy.$refs["shareRef"].validate((valid) => {
    if (valid) {
      addOrUpdateShare(form.value).then((response) => {
        proxy.$modal.msgSuccess("增加成功");
        shareOpen.value = false;
        getList();
      });
    }
  });
}
// 查询设备编号
const getListInventory = (lockNo = "") => {
  listDevice({
    lockNo,
  }).then((res) => {
    inventoryData.value = res.rows;
  });
};
const inventoryChange = (e) => {
  let item = inventoryData.value.find((item) => {
    return item.inventoryId == e;
  });
  // 添加车位锁的时候,手机号不需要回填
  // form.value.phone = item.phone;
  form.value.lockNo = item.lockNo;
};
// 查询所有的车场列表
function queryParkList() {
  getParkList().then((response) => {
    parkList.value = response.data;
  });
}
// 车场的切换
function parkChange(e) {
  let item = parkList.value.find((item) => {
    return e == item.id;
  });
  form.value.parkAddress = item.parkAddress;
}
// 跳转
function handleLink(row) {
  router.push({
    name: "LockOrder",
    query: {
      lotId: row.lotId,
    },
  });
}
getList();

getListInventory();
queryParkList();
// 静态数据
const locking_type = [
  {
    label: "个人",
    value: 1,
  },
  {
    label: "物业",
    value: 2,
  },
];
// 出租类型
const rent_type = [
  {
    label: "自定义",
    value: 0,
  },
  {
    label: "每日",
    value: 1,
  },
  {
    label: "工作日",
    value: 2,
  },
  {
    label: "休息日",
    value: 3,
  },
];
// 订单状态
const orderStatus = [
  {
    label: "无",
    value: '0',
  },
  {
    label: "有",
    value: '1',
  },
];
// 锁状态
const status = [
  {
    label: "升锁",
    value: 1,
  },
  {
    label: "降锁",
    value: 0,
  },
];
// 在线状态
const onlineStatus = [
  {
    label: "在线",
    value: 1,
  },
  {
    label: "离线",
    value: 0,
  },
];
</script>
<style lang="scss" scoped>
.right-grid {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}
</style>
