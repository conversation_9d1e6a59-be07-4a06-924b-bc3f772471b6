import{T as h,d as Z,w as ee,r as g,e as i,c as P,o as x,h as e,i as l,j as u,v as t,A as d,s as D,f as B,t as le,D as L,J as ae}from"./index-DDqcBwar.js";import{b as te}from"./statements-Nia77kYU.js";function ge(p){return h({url:"/device/order/getOrders",method:"get",params:p})}function be(p){return h({url:"/device/order/getOrdersByLotId",method:"get",params:p})}function ue(p){return h({url:"/device/order/getOrders/"+p,method:"get"})}function de(p){return h({url:"/device/order/getDiscount",method:"get",params:p})}function ne(p){return h({url:"/device/order/getManualRefund",method:"get",params:p})}function oe(p){return h({url:"/device/order/getOrderDetailByBill/"+p,method:"get"})}function fe(p){return h({url:"/device/order/getViolation",method:"get",params:p})}const se={key:1},re={class:"dialog-footer"},_e={style:{"margin-bottom":"20px"}},pe={class:"dialog-footer"},ie={__name:"oderDetails",props:{modelValue:{type:Boolean,default:!1},title:{type:String,default:"订单详情"},item:{type:Object,default:()=>({})},type:{type:String,default:"1"}},emits:["update:modelValue"],setup(p,{emit:j}){const O=p,U=j,{proxy:S}=Z();ee(()=>O.modelValue,s=>{s&&M(O.item)});const _=g({}),R=g({}),c=g({}),E=g({}),y=g(!1),f=g({}),b=g(!1),m=g({}),q={refundFee:[{required:!0,message:"退款金额不能为空",trigger:"blur"},{validator:(s,o,I)=>{o>f.value.actualFee?I(new Error("退款金额不能大于应收金额")):I()}}],password:[{required:!0,message:"交易密码不能为空",trigger:"blur"}]},M=s=>{console.log(s),ue(s.orderId).then(o=>{f.value=o.data,y.value=!0,$({parkId:o.data.parkId,phone:o.data.phone,lotShareRuleId:o.data.lotShareRuleId}),z({orderId:s.orderId}),J(o.data.lotShareRuleId),G({orderId:s.orderId,lotShareRuleId:o.data.lotShareRuleId})})};function $(s){de(s).then(o=>{_.value=o.data})}function z(s){ne(s).then(o=>{R.value=o.data})}function J(s){oe(s).then(o=>{c.value=o.data})}function G(s){fe(s).then(o=>{E.value=o.data})}const H=[{label:"普通计费",value:"1"},{label:"特殊计费",value:"2"}],K=[{label:"待交押金",value:"-1"},{label:"已交押金待入场",value:"0"},{label:"已入场",value:"3"},{label:"已完成",value:"1"},{label:"已取消(主动)",value:"2"},{label:"已取消(自动取消)",value:"4"},{label:"待补缴",value:"5"}];function C(){y.value=!1,b.value=!1,U("update:modelValue",!1)}const Q=()=>{b.value=!0},W=()=>{S.$refs.tagRef.validate(s=>{s&&(m.value.orderId=f.value.orderId,m.value.actualFee=f.value.actualFee,te(m.value).then(o=>{S.$modal.msgSuccess("退款成功"),b.value=!1}))})};function X(){b.value=!1,m.value={}}return(s,o)=>{const I=i("dict-tag"),r=i("el-form-item"),a=i("el-col"),n=i("el-row"),w=i("el-card"),V=i("el-collapse-item"),k=i("el-collapse"),F=i("el-form"),T=i("el-button"),A=i("el-dialog"),Y=i("el-tag"),N=i("el-input");return x(),P(ae,null,[e(A,{title:"订单详情",modelValue:u(y),"onUpdate:modelValue":o[0]||(o[0]=v=>L(y)?y.value=v:null),width:"880px","append-to-body":"","close-on-click-modal":!1,onClose:C},{footer:l(()=>[B("div",re,[p.type==1?(x(),D(T,{key:0,onClick:Q,type:"primary"},{default:l(()=>[t("退 款")]),_:1})):le("",!0),e(T,{onClick:C},{default:l(()=>[t("取 消")]),_:1})])]),default:l(()=>[e(F,{ref:"tagRef",model:u(f),"label-width":"120px"},{default:l(()=>[e(n,null,{default:l(()=>[e(a,{span:24},{default:l(()=>[e(r,{label:"状态"},{default:l(()=>[e(I,{options:K,value:u(f).status},null,8,["value"])]),_:1})]),_:1}),e(a,{span:12},{default:l(()=>[e(r,{label:"订单号"},{default:l(()=>[t(d(u(f).orderNo),1)]),_:1})]),_:1}),e(a,{span:12},{default:l(()=>[e(r,{label:"订单类型"},{default:l(()=>[e(I,{options:H,value:u(f).orderType},null,8,["value"])]),_:1})]),_:1}),e(a,{span:12},{default:l(()=>[e(r,{label:"用户手机号"},{default:l(()=>[t(d(u(f).phone),1)]),_:1})]),_:1}),e(a,{span:12},{default:l(()=>[e(r,{label:"车牌号"},{default:l(()=>[t(d(u(f).plateNo),1)]),_:1})]),_:1}),e(a,{span:12},{default:l(()=>[e(r,{label:"车场"},{default:l(()=>[t(d(u(f).parkName),1)]),_:1})]),_:1}),e(a,{span:24},{default:l(()=>[e(r,{label:"车位信息"},{default:l(()=>[e(k,{style:{width:"300px"}},{default:l(()=>[e(V,{title:"查看详情",name:"1"},{default:l(()=>[e(w,{style:{width:"300px"}},{default:l(()=>[e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 设备编号: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(c).lockNumbers),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 区域: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(c).location),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 车位号: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(c).code),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(a,{span:12},{default:l(()=>[e(r,{label:"下单时间"},{default:l(()=>[t(d(s.parseTime(u(f).orderTime)),1)]),_:1})]),_:1}),e(a,{span:12},{default:l(()=>[e(r,{label:"结束时间"},{default:l(()=>[t(d(s.parseTime(u(f).finishTime)),1)]),_:1})]),_:1}),e(a,{span:24},{default:l(()=>[e(r,{label:"收费标准"},{default:l(()=>[e(k,{style:{width:"300px"}},{default:l(()=>[e(V,{title:"查看详情",name:"1"},{default:l(()=>[e(w,{style:{width:"300px"}},{default:l(()=>[e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 锁单价: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(c).unitCharge)+" /1分钟 ",1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 封顶价格: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(c).capAmount)+"元",1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 共享时间: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(s.parseTime(u(c).shareStartTime))+" ~ "+d(s.parseTime(u(c).shareEndTime)),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 车场单价: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(c).price),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(a,{span:12},{default:l(()=>[e(r,{label:"正常消费"},{default:l(()=>[t(d(u(f).parkAmount),1)]),_:1})]),_:1}),e(a,{span:24},{default:l(()=>[e(r,{label:"优惠折扣"},{default:l(()=>[e(k,{style:{width:"300px"}},{default:l(()=>[e(V,{title:"查看详情",name:"1"},{default:l(()=>[e(w,{style:{width:"300px"}},{default:l(()=>[e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 折扣: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).discount)+"折 ",1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 正常价格: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(f).parkAmount),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 免费时长: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).freeTime)+"小时 ",1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 锁单价: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).price)+"元/分钟*60 ",1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 免费时间: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).startTime),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 订单时间: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).price)+"元/分钟*60 ",1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(a,{span:24},{default:l(()=>[e(r,{label:"额外费用"},{default:l(()=>[e(k,{style:{width:"300px"}},{default:l(()=>[e(V,{title:u(f).timeoutAmount+"元",name:"1"},{default:l(()=>[e(w,{style:{width:"300px"}},{default:l(()=>[e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 入场滞留费: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).discount),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 进场时间: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).location),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 开锁时间: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).freeTime)+"小时 ",1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 滞留时间:")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).price),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 费用计算:")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).freeTime),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 出场滞留费:")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).code),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 关锁时间:")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).code),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 出场时间:")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).code),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 滞留时间 :")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).code),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 费用计算: ")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).code),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 超时停放:")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).code),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 共享时间:")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).code),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 关锁时间:")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).code),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 超时停放:")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).code),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:6},{default:l(()=>[t(" 费用计算:")]),_:1}),e(a,{span:18},{default:l(()=>[t(d(u(_).code),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1})]),_:1})]),_:1}),e(a,{span:12},{default:l(()=>[e(r,{label:"应收金额"},{default:l(()=>[t(d(u(f).payableAmount||0)+"元 ",1)]),_:1})]),_:1}),e(a,{span:12},{default:l(()=>[e(r,{label:"实收金额"},{default:l(()=>[t(d(u(f).actualFee||0)+"元 ",1)]),_:1})]),_:1}),e(a,{span:12},{default:l(()=>[e(r,{label:"需退费金额"},{default:l(()=>[t(d(u(f).refundPendingAmount||0)+"元 ",1)]),_:1})]),_:1}),e(a,{span:12},{default:l(()=>[e(r,{label:"待补缴金额"},{default:l(()=>[t(d(u(f).unpaidAmount)+"元 ",1)]),_:1})]),_:1}),e(a,{span:24},{default:l(()=>[e(r,{label:"人工退费"},{default:l(()=>[u(f).manualRefundAmount?(x(),D(k,{key:0,style:{width:"300px"}},{default:l(()=>[e(V,{title:u(f).manualRefundAmount+"元",name:"1"},{default:l(()=>[e(w,{style:{width:"300px"}},{default:l(()=>[e(n,null,{default:l(()=>[e(a,{span:7},{default:l(()=>[t(" 操作人员： ")]),_:1}),e(a,{span:17},{default:l(()=>[t(d(u(R).operateBy),1)]),_:1})]),_:1}),e(n,null,{default:l(()=>[e(a,{span:7},{default:l(()=>[t(" 操作时间： ")]),_:1}),e(a,{span:17},{default:l(()=>[t(d(u(R).updateTime),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["title"])]),_:1})):(x(),P("div",se,"无"))]),_:1})]),_:1}),e(a,{span:24},{default:l(()=>[e(r,{label:"分成扣除"},{default:l(()=>[t(" 暂无 ")]),_:1})]),_:1}),e(a,{span:12},{default:l(()=>[e(r,{label:"实际收入"},{default:l(()=>[t(d(u(f).actualIncome)+" 元 ",1)]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),e(A,{title:"退款",modelValue:u(b),"onUpdate:modelValue":o[3]||(o[3]=v=>L(b)?b.value=v:null),width:"580px","append-to-body":""},{footer:l(()=>[B("div",pe,[e(T,{type:"primary",onClick:W},{default:l(()=>[t("确 定")]),_:1}),e(T,{onClick:X},{default:l(()=>[t("取 消")]),_:1})])]),default:l(()=>[e(F,{ref:"tagRef",model:u(m),rules:q,"label-width":"80px"},{default:l(()=>[B("div",_e,[e(Y,{type:"danger"},{default:l(()=>[t(" 确定是否进行退款，退款后无法恢复(可退金额不可大于消费金额/保证金)。")]),_:1})]),e(n,null,{default:l(()=>[e(a,{span:24},{default:l(()=>[e(r,{label:"退款金额",prop:"refundFee"},{default:l(()=>[e(N,{modelValue:u(m).refundFee,"onUpdate:modelValue":o[1]||(o[1]=v=>u(m).refundFee=v),placeholder:"请输入退款金额",type:"number"},null,8,["modelValue"])]),_:1})]),_:1}),e(a,{span:24},{default:l(()=>[e(r,{label:"交易密码",prop:"password"},{default:l(()=>[e(N,{modelValue:u(m).password,"onUpdate:modelValue":o[2]||(o[2]=v=>u(m).password=v),placeholder:"请输入交易密码",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])],64)}}},ve=Object.freeze(Object.defineProperty({__proto__:null,default:ie},Symbol.toStringTag,{value:"Module"}));export{ie as _,ge as a,be as l,ve as o};
