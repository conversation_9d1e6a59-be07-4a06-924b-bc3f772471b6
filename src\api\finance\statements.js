import request from '@/utils/request'
// 查询财务报表列表
export function listStatements(query) {
  return request({
    url: '/finance/financeList',
    method: 'get',
    params: query
  })
}
// 获取统计数据
export function getStatements(query) {
  return request({
    url: '/finance/total' ,
    method: 'get',
    params: query
  })
}
// 查询提现列表
export function listWithdraw(query) {
  return request({
    url: '/finance/withdrawalsList',
    method: 'get',
    params: query
  })
}
// 打款
export function putMoney(query) {
  return request({
    url: '/finance/updatePaymentStatus',
    method: 'put',
    data: query
  })
}
// 查询提现记录
export function getWithdraw(query) {
  return request({
    url: '/finance/getWithdrawals' ,
    method: 'get',
    params: query
  })
}
// 查看当前用户的余额
export function getBalance(query) {
  return request({
    url: '/finance/checkAccount',
    method: 'get',
    params: query
  })
}
// 提现
export function withdraw(query) {
  return request({
    url: '/finance/withdraw/apply',
    method: 'post',
    data: query
  })
}
// 获取银行卡列表
export function getBankCardList(query) {
  return request({
    url: '/finance/bankList',
    method: 'get',
    params: query
  })
}
//退款
export function applyRefund(query) {
  return request({
    url: '/finance/refund',
    method: 'post',
    data: query
  })
}