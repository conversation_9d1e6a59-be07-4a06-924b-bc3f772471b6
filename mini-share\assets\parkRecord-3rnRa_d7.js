import{B as w,d as S,r as e,C as x,F as k,e as u,G as q,c as z,H as d,j as t,s as D,i as I,h as o,I as P,o as m}from"./index-DDqcBwar.js";import{k as j}from"./park-DZi9DkwV.js";const L={class:"app-container"},R=w({name:"Notice"}),A=Object.assign(R,{setup(U){const{proxy:_}=S(),{sys_notice_status:E,sys_notice_type:F}=_.useDict("sys_notice_status","sys_notice_type"),p=e([]);e(!1);const l=e(!0);e(!0);const f=e([]),v=e(!0),y=e(!0),i=e(0);e("");const b=x({form:{},queryParams:{pageNum:1,pageSize:10,tagName:void 0},rules:{tagName:[{required:!0,message:"标签类型不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序不能为空",trigger:"blur"}]}}),{queryParams:s,form:G,rules:H}=k(b);function g(){l.value=!0,j(s.value).then(a=>{p.value=a.rows,i.value=a.total,l.value=!1})}function h(a){f.value=a.map(n=>n.noticeId),v.value=a.length!=1,y.value=!a.length}return g(),(a,n)=>{const r=u("el-table-column"),B=u("el-table"),C=u("pagination"),N=q("loading");return m(),z("div",L,[d((m(),D(B,{data:t(p),onSelectionChange:h},{default:I(()=>[o(r,{label:"序号",align:"center",type:"index",width:"100"}),o(r,{label:"车牌号码",align:"center",prop:"tagName","show-overflow-tooltip":!0}),o(r,{label:"入场时间",align:"center",prop:"orderBy"}),o(r,{label:"开闸设备",align:"center",prop:"orderBy"}),o(r,{label:"出场时间",align:"center",prop:"orderBy"})]),_:1},8,["data"])),[[N,t(l)]]),d(o(C,{total:t(i),page:t(s).pageNum,"onUpdate:page":n[0]||(n[0]=c=>t(s).pageNum=c),limit:t(s).pageSize,"onUpdate:limit":n[1]||(n[1]=c=>t(s).pageSize=c),onPagination:g},null,8,["total","page","limit"]),[[P,t(i)>0]])])}}});export{A as default};
