import{B as ze,a as Ke,a3 as Le,d as Fe,r as s,C as Ae,F as Ee,w as je,e as r,G as H,c as C,o as i,h as e,i as a,j as o,f as X,H as y,k as Y,J as R,K as P,s as m,v as w,I as Z,D as ee,t as x,A as le}from"./index-DDqcBwar.js";import{h as Je,l as Me,i as te,j as Qe,k as We,r as Ge,a as He,m as Xe,n as Ye}from"./user-C3QdTmTM.js";import{g as Ze}from"./tag-DOE35K0D.js";import{M as el,g as ae}from"./splitpanes-DR_7Z2MZ.js";const ll={class:"app-container"},tl={class:"head-container"},al={class:"dialog-footer"},nl=ze({name:"User"}),pl=Object.assign(nl,{setup(ol){const ne=Ke(),oe=Le(),{proxy:d}=Fe(),{sys_normal_disable:K,sys_user_sex:ul}=d.useDict("sys_normal_disable","sys_user_sex"),L=s([]),h=s(!1),D=s(!0),U=s(!0),q=s([]),ue=s(!0),re=s(!0),T=s(0),B=s(""),F=s([]),se=s(""),A=s(void 0),E=s(void 0),de=s(void 0),j=s([]),O=s([]),ie=s([]),J=s([{key:0,label:"用户编号",visible:!0},{key:1,label:"用户名称",visible:!0},{key:2,label:"用户昵称",visible:!0},{key:3,label:"部门",visible:!0},{key:4,label:"手机号码",visible:!0},{key:5,label:"状态",visible:!0},{key:6,label:"创建时间",visible:!0}]),pe=Ae({form:{},queryParams:{pageNum:1,pageSize:10,userName:void 0,phonenumber:void 0,status:void 0,deptId:void 0},rules:{userName:[{required:!0,message:"用户名称不能为空",trigger:"blur"},{min:2,max:20,message:"用户名称长度必须介于 2 和 20 之间",trigger:"blur"}],nickName:[{required:!0,message:"用户昵称不能为空",trigger:"blur"}],password:[{required:!0,message:"用户密码不能为空",trigger:"blur"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:`不能包含非法字符：< > " ' \\ |`,trigger:"blur"}],phonenumber:[{required:!0,message:"请输入正确的手机号码",trigger:"blur"}],deptId:[{required:!0,message:"请选择部门",trigger:"change"}]}}),{queryParams:p,form:u,rules:me}=Ee(pe),ce=(n,l)=>n?l.label.indexOf(n)!==-1:!0;je(se,n=>{d.$refs.deptTreeRef.filter(n)});function k(){D.value=!0,Me(d.addDateRange(p.value,F.value)).then(n=>{D.value=!1,L.value=n.rows,T.value=n.total})}function fe(){Je().then(n=>{A.value=n.data,E.value=M(JSON.parse(JSON.stringify(n.data)))})}function M(n){return n.filter(l=>l.disabled?!1:(l.children&&l.children.length&&(l.children=M(l.children)),!0))}function _e(n){p.value.deptId=n.id,I()}function I(){p.value.pageNum=1,k()}function ge(){F.value=[],d.resetForm("queryRef"),p.value.deptId=void 0,d.$refs.deptTreeRef.setCurrentKey(null),I()}function he(n){const l=n.userId||q.value;d.$modal.confirm('是否确认删除用户编号为"'+l+'"的数据项？').then(function(){return We(l)}).then(()=>{k(),d.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ve(n){let l=n.status==="0"?"启用":"停用";d.$modal.confirm('确认要"'+l+'""'+n.userName+'"用户吗?').then(function(){return Qe(n.userId,n.status)}).then(()=>{d.$modal.msgSuccess(l+"成功")}).catch(function(){n.status=n.status==="0"?"1":"0"})}function be(n){const l=n.userId;ne.push("/system/user-auth/role/"+l)}function ye(n){d.$prompt('请输入"'+n.userName+'"的新密码',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,inputPattern:/^.{5,20}$/,inputErrorMessage:"用户密码长度必须介于 5 和 20 之间",inputValidator:l=>{if(/<|>|"|'|\||\\/.test(l))return`不能包含非法字符：< > " ' \\ |`}}).then(({value:l})=>{Ge(n.userId,l).then(v=>{d.$modal.msgSuccess("修改成功，新密码是："+l)})}).catch(()=>{})}function ke(n){q.value=n.map(l=>l.userId),ue.value=n.length!=1,re.value=!n.length}function z(){u.value={userId:void 0,deptId:void 0,userName:void 0,nickName:void 0,password:"123456",phonenumber:void 0,email:void 0,sex:void 0,status:"0",remark:void 0,postIds:[],roleIds:[]},d.resetForm("userRef")}function we(){h.value=!1,z()}function Ne(){z(),te().then(n=>{j.value=n.posts,O.value=n.roles,h.value=!0,B.value="添加用户",u.value.password=de.value})}function Ie(n){z();const l=n.userId||q.value;te(l).then(v=>{u.value=v.data,j.value=v.posts,O.value=v.roles,u.value.postIds=v.postIds,u.value.roleIds=v.roleIds,h.value=!0,B.value="修改用户",u.password=""})}function Ve(){d.$refs.userRef.validate(n=>{n&&(u.value.userId!=null?Xe(u.value).then(l=>{d.$modal.msgSuccess("修改成功"),h.value=!1,k()}):Ye(u.value).then(l=>{d.$modal.msgSuccess("新增成功"),h.value=!1,k()}))})}function Ce(){Ze().then(n=>{ie.value=n.data})}function xe(){u.value.phonenumber&&He({phone:u.value.phonenumber}).then(n=>{var l;n.code==200&&((l=n.rows)==null?void 0:l.length)>0?(u.value.nickName=n.rows[0].wechatNickName,u.value.userName=n.rows[0].phone):d.$modal.msgError("未查询到用户信息")})}return fe(),k(),Ce(),(n,l)=>{const v=r("el-tree"),b=r("el-col"),S=r("el-input"),c=r("el-form-item"),Q=r("el-option"),W=r("el-select"),f=r("el-button"),G=r("el-form"),Ue=r("right-toolbar"),N=r("el-row"),_=r("el-table-column"),Se=r("el-tag"),$e=r("el-switch"),$=r("el-tooltip"),Re=r("el-table"),Pe=r("pagination"),De=r("el-tree-select"),qe=r("el-radio"),Te=r("el-radio-group"),Be=r("el-dialog"),V=H("hasPermi"),Oe=H("loading");return i(),C("div",ll,[e(N,{gutter:20},{default:a(()=>[e(o(el),{horizontal:o(oe).device==="mobile",class:"default-theme"},{default:a(()=>[e(o(ae),{size:"16"},{default:a(()=>[e(b,null,{default:a(()=>[X("div",tl,[e(v,{data:o(A),props:{label:"label",children:"children"},"expand-on-click-node":!1,"filter-node-method":ce,ref:"deptTreeRef","node-key":"id","highlight-current":"","default-expand-all":"",onNodeClick:_e},null,8,["data"])])]),_:1})]),_:1}),e(o(ae),{size:"84"},{default:a(()=>[e(b,null,{default:a(()=>[y(e(G,{model:o(p),ref:"queryRef",inline:!0,"label-width":"68px"},{default:a(()=>[e(c,{label:"用户名称",prop:"nickName"},{default:a(()=>[e(S,{modelValue:o(p).nickName,"onUpdate:modelValue":l[0]||(l[0]=t=>o(p).nickName=t),placeholder:"请输入用户名称",clearable:"",style:{width:"240px"},onKeyup:Y(I,["enter"])},null,8,["modelValue"])]),_:1}),e(c,{label:"手机号码",prop:"phonenumber"},{default:a(()=>[e(S,{modelValue:o(p).phonenumber,"onUpdate:modelValue":l[1]||(l[1]=t=>o(p).phonenumber=t),placeholder:"请输入手机号码",clearable:"",style:{width:"240px"},onKeyup:Y(I,["enter"])},null,8,["modelValue"])]),_:1}),e(c,{label:"状态",prop:"status"},{default:a(()=>[e(W,{modelValue:o(p).status,"onUpdate:modelValue":l[2]||(l[2]=t=>o(p).status=t),placeholder:"用户状态",clearable:"",style:{width:"240px"}},{default:a(()=>[(i(!0),C(R,null,P(o(K),t=>(i(),m(Q,{key:t.value,label:t.label,value:t.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(c,null,{default:a(()=>[e(f,{type:"primary",icon:"Search",onClick:I},{default:a(()=>[w("搜索")]),_:1}),e(f,{icon:"Refresh",onClick:ge},{default:a(()=>[w("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[Z,o(U)]]),e(N,{gutter:10,class:"mb8"},{default:a(()=>[e(b,{span:1.5},{default:a(()=>[y((i(),m(f,{type:"primary",plain:"",icon:"Plus",onClick:Ne},{default:a(()=>[w("新增")]),_:1})),[[V,["system:user:add"]]])]),_:1}),e(Ue,{showSearch:o(U),"onUpdate:showSearch":l[3]||(l[3]=t=>ee(U)?U.value=t:null),onQueryTable:k,columns:o(J)},null,8,["showSearch","columns"])]),_:1}),y((i(),m(Re,{data:o(L),onSelectionChange:ke},{default:a(()=>[e(_,{type:"selection",width:"50",align:"center"}),o(J)[0].visible?(i(),m(_,{label:"用户编号",align:"center",key:"userId",prop:"userId"})):x("",!0),e(_,{label:"用户名称",align:"center",key:"nickName",prop:"nickName","show-overflow-tooltip":!0}),e(_,{label:"标签",align:"center",key:"tagNames",prop:"tagNames","show-overflow-tooltip":!0}),e(_,{label:"所属车场",align:"center",key:"parkNames",prop:"parkNames",width:"300px","show-overflow-tooltip":!0}),e(_,{label:"角色",align:"center"},{default:a(t=>[(i(!0),C(R,null,P(t.row.roles,g=>(i(),m(Se,{key:g,class:"tag",size:"small"},{default:a(()=>[w(le(g.roleName),1)]),_:2},1024))),128))]),_:1}),e(_,{label:"手机号码",align:"center",key:"phonenumber",prop:"phonenumber",width:"120"}),e(_,{label:"状态",align:"center",key:"status"},{default:a(t=>[e($e,{modelValue:t.row.status,"onUpdate:modelValue":g=>t.row.status=g,"active-value":"0","inactive-value":"1",onChange:g=>ve(t.row)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),e(_,{label:"操作",align:"center",width:"150","class-name":"small-padding fixed-width"},{default:a(t=>[t.row.userId!==1?(i(),m($,{key:0,content:"修改",placement:"top"},{default:a(()=>[y(e(f,{link:"",type:"primary",icon:"Edit",onClick:g=>Ie(t.row)},null,8,["onClick"]),[[V,["system:user:edit"]]])]),_:2},1024)):x("",!0),t.row.userId!==1?(i(),m($,{key:1,content:"删除",placement:"top"},{default:a(()=>[y(e(f,{link:"",type:"primary",icon:"Delete",onClick:g=>he(t.row)},null,8,["onClick"]),[[V,["system:user:remove"]]])]),_:2},1024)):x("",!0),t.row.userId!==1?(i(),m($,{key:2,content:"重置密码",placement:"top"},{default:a(()=>[y(e(f,{link:"",type:"primary",icon:"Key",onClick:g=>ye(t.row)},null,8,["onClick"]),[[V,["system:user:resetPwd"]]])]),_:2},1024)):x("",!0),t.row.userId!==1?(i(),m($,{key:3,content:"分配角色",placement:"top"},{default:a(()=>[y(e(f,{link:"",type:"primary",icon:"CircleCheck",onClick:g=>be(t.row)},null,8,["onClick"]),[[V,["system:user:edit"]]])]),_:2},1024)):x("",!0)]),_:1})]),_:1},8,["data"])),[[Oe,o(D)]]),y(e(Pe,{total:o(T),page:o(p).pageNum,"onUpdate:page":l[4]||(l[4]=t=>o(p).pageNum=t),limit:o(p).pageSize,"onUpdate:limit":l[5]||(l[5]=t=>o(p).pageSize=t),onPagination:k},null,8,["total","page","limit"]),[[Z,o(T)>0]])]),_:1})]),_:1})]),_:1},8,["horizontal"])]),_:1}),e(Be,{title:o(B),modelValue:o(h),"onUpdate:modelValue":l[11]||(l[11]=t=>ee(h)?h.value=t:null),width:"600px","append-to-body":""},{footer:a(()=>[X("div",al,[e(f,{type:"primary",onClick:Ve},{default:a(()=>[w("确 定")]),_:1}),e(f,{onClick:we},{default:a(()=>[w("取 消")]),_:1})])]),default:a(()=>[e(G,{model:o(u),rules:o(me),ref:"userRef","label-width":"80px"},{default:a(()=>[e(N,null,{default:a(()=>[e(b,{span:24},{default:a(()=>[e(c,{label:"手机号码",prop:"phonenumber"},{default:a(()=>[e(S,{modelValue:o(u).phonenumber,"onUpdate:modelValue":l[6]||(l[6]=t=>o(u).phonenumber=t),placeholder:"请输入手机号码",maxlength:"11",onBlur:xe},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(N,null,{default:a(()=>[e(b,{span:12},{default:a(()=>[e(c,{label:"用户名称",prop:"nickName"},{default:a(()=>[e(S,{modelValue:o(u).nickName,"onUpdate:modelValue":l[7]||(l[7]=t=>o(u).nickName=t),placeholder:"请输入用户名称",maxlength:"30"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(N,null,{default:a(()=>[e(b,{span:12},{default:a(()=>[e(c,{label:"归属部门",prop:"deptId"},{default:a(()=>[e(De,{modelValue:o(u).deptId,"onUpdate:modelValue":l[8]||(l[8]=t=>o(u).deptId=t),data:o(E),props:{value:"id",label:"label",children:"children"},"value-key":"id",placeholder:"请选择归属部门","check-strictly":""},null,8,["modelValue","data"])]),_:1})]),_:1})]),_:1}),e(b,{span:12},{default:a(()=>[e(c,{label:"角色"},{default:a(()=>[e(W,{modelValue:o(u).roleIds,"onUpdate:modelValue":l[9]||(l[9]=t=>o(u).roleIds=t),multiple:"",placeholder:"请选择"},{default:a(()=>[(i(!0),C(R,null,P(o(O),t=>(i(),m(Q,{key:t.roleId,label:t.roleName,value:t.roleId,disabled:t.status==1},null,8,["label","value","disabled"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(N,null,{default:a(()=>[e(b,{span:12},{default:a(()=>[e(c,{label:"状态"},{default:a(()=>[e(Te,{modelValue:o(u).status,"onUpdate:modelValue":l[10]||(l[10]=t=>o(u).status=t)},{default:a(()=>[(i(!0),C(R,null,P(o(K),t=>(i(),m(qe,{key:t.value,value:t.value},{default:a(()=>[w(le(t.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{pl as default};
