<template>
  <div class="login">
    <div class="login-content">
      <div class="form">
        <div class="login-logo"></div>
        <el-form
          ref="loginRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
        >
          <div class="short-tips">欢迎回来</div>
          <h3 class="title">共享车位管理平台</h3>
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              type="text"
              auto-complete="off"
              placeholder="账号"
            >
              <template #prefix
                ><svg-icon icon-class="icon_zhanghao" class="login-icon"
              /></template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              auto-complete="off"
              placeholder="密码"
              @keyup.enter="handleLogin"
            >
              <template #prefix
                ><svg-icon icon-class="icon_mima" class="login-icon"
              /></template>
            </el-input>
          </el-form-item>

          <el-form-item style="width: 100%">
            <el-button
              :loading="loading"
              type="primary"
              style="width: 100%"
              @click.prevent="handleLogin"
            >
              <span v-if="!loading">登 录</span>
              <span v-else>登 录 中...</span>
            </el-button>
          </el-form-item>
        </el-form>
        <div class="form-copy">Copyright © 2019 悦马科技技术部出品</div>
      </div>
      <div class="form-info"></div>
    </div>
  </div>
</template>

<script setup>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from "@/store/modules/user";

const userStore = useUserStore();
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

const loginForm = ref({
  username: "admin",
  password: "",
  rememberMe: false,
  code: "",
  uuid: "",
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }],
};

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

watch(
  route,
  (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect;
  },
  { immediate: true }
);

function handleLogin() {
  proxy.$refs.loginRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 });
        Cookies.set("password", encrypt(loginForm.value.password), {
          expires: 30,
        });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      // 调用action的登录方法
      userStore
        .login(loginForm.value)
        .then(() => {
          const query = route.query;
          const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
            if (cur !== "redirect") {
              acc[cur] = query[cur];
            }
            return acc;
          }, {});
          router.push({ path: redirect.value || "/", query: otherQueryParams });
        })
        .catch(() => {
          loading.value = false;
          // 重新获取验证码
          if (captchaEnabled.value) {
            getCode();
          }
        });
    }
  });
}

function getCode() {
  getCodeImg().then((res) => {
    captchaEnabled.value =
      res.captchaEnabled === undefined ? true : res.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password:
      password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
  };
}

getCode();
getCookie();
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/ym-bg.webp");
  background-size: cover;
  .login-content {
    width: 1200px;
    height: 680px;
    display: flex;

    .form {
      background: #ffffff;
      width: 620px;
      padding: 32px 40px;
      display: flex;
      flex-direction: column;
      .login-logo {
        width: 122px;
        height: 30px;
        background: url("@/assets/images/login-logo.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
      }
      .form-copy {
        text-align: center;
        font-weight: 500;
        font-size: 12px;
        color: #cccccc;
      }
    }
    .form-info {
      flex: 1;
      background: url("@/assets/images/form-info.webp");
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
}
.short-tips {
  font-weight: 400;
  font-size: 18px;
  color: #cccccc;
}
.title {
  font-weight: 500;
  font-size: 34px;
  color: #333333;
  margin: 18px 0px 36px 0px;
}

.login-form {
  padding: 88px 40px 5px 40px;
  flex: 1;

  .login-icon {
    font-size: 18px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 40px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 40px;
  padding-left: 12px;
}
:deep(.el-form-item) {
  margin-bottom: 16px;
}
:deep(.el-form-item__content) {
  height: 60px;
  // background: #f7f9fb;
  border-radius: 4px 4px 4px 4px;
  .el-input__wrapper {
    border: none;
    height: 60px;
    background: #f7f9fb;
    outline: none;
    box-shadow: none;
  }
}
:deep(.el-button) {
  height: 60px;
  background: #13c2c2;
  margin-top: 16px;
  font-size: 20px;
}
</style>
