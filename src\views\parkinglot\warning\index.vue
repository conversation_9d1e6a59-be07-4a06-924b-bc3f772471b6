<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="预警类型" prop="alertType">
        <el-select
          v-model="queryParams.alertType"
          placeholder="请选择预警类型"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="item in alert_type"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="预警状况" prop="alertState">
        <el-select
          v-model="queryParams.alertState"
          placeholder="请选择预警状况"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="item in alert_state"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="车场" prop="parkName">
        <el-input
          v-model="queryParams.parkName"
          placeholder="请输入车场名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="logList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column label="预警类型" align="center" prop="alertType">
        <template #default="scope">
          <dict-tag :value="scope.row.alertType" :options="alert_type" />
        </template>
      </el-table-column>
      <el-table-column label="预警状况" align="center" prop="alertState">
        <template #default="scope">
          <dict-tag :value="scope.row.alertState" :options="alert_state" />
        </template>
      </el-table-column>

      <el-table-column label="车场" align="center" prop="parkName" />
      <el-table-column label="地址" align="center" prop="parkAddress" />
      <el-table-column label="区域" align="center" prop="location" />
      <el-table-column label="车位号" align="center" prop="code" />
      <el-table-column label="预警时间" align="center" prop="alertTime">
        <template #default="scope">
          {{ parseTime(scope.row.alertTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Notice">
import { listWarning } from "@/api/parkinglot/warning";

const { proxy } = getCurrentInstance();
const { sys_notice_status, sys_notice_type } = proxy.useDict(
  "sys_notice_status",
  "sys_notice_type"
);

const logList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    tagName: undefined,
  },
  rules: {
    tagName: [{ required: true, message: "标签类型不能为空", trigger: "blur" }],
    orderBy: [{ required: true, message: "排序不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  listWarning(queryParams.value).then((response) => {
    logList.value = response.rows;
    total.value = parseInt(response.total);
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
    status: "0",
  };
  proxy.resetForm("tagRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加标签";
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset();
  const tagId = row.id || ids.value;
  getTag(tagId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改标签";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["tagRef"].validate((valid) => {
    if (valid) {
      if (form.value.parkId != undefined) {
        updateTag(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTag(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const tagIds = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除标签编号为"' + tagIds + '"的数据项？')
    .then(function () {
      return delTag(tagIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

getList();
// 静态数据
//预警状况value = "操作类型", allowableValues = "1, 2, 3"
const alert_type = [
  { label: "停车位不足", value: "1" },
  { label: "异常入侵", value: "2" },
];

const alert_state = [
  { label: "车位锁离线", value: "1" },
  { label: "V1离线", value: "2" },
  { label: "电量过低", value: "3" },
  { label: "订单欠费", value: "4" },
];
</script>
