import{T as G,B as H,d as J,r as u,C as O,F as W,e as r,G as M,c as f,o as d,H as v,I as q,j as t,h as l,i as n,k as y,J as B,K,v as b,s as X,A as Y}from"./index-DDqcBwar.js";function Z(h){return G({url:"/park/alert/list",method:"get",params:h})}const $={class:"app-container"},ee=H({name:"Notice"}),re=Object.assign(ee,{setup(h){const{proxy:k}=J(),{sys_notice_status:le,sys_notice_type:te}=k.useDict("sys_notice_status","sys_notice_type"),S=u([]);u(!1);const m=u(!0),U=u(!0),D=u([]),R=u(!0),z=u(!0),_=u(0);u("");const F=O({form:{},queryParams:{pageNum:1,pageSize:10,tagName:void 0},rules:{tagName:[{required:!0,message:"标签类型不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序不能为空",trigger:"blur"}]}}),{queryParams:a,form:ae,rules:ne}=W(F);function g(){m.value=!0,Z(a.value).then(s=>{S.value=s.rows,_.value=s.total,m.value=!1})}function p(){a.value.pageNum=1,g()}function I(){k.resetForm("queryRef"),p()}function L(s){D.value=s.map(o=>o.noticeId),R.value=s.length!=1,z.value=!s.length}g();const w=[{label:"停车位不足",value:"1"},{label:"异常入侵",value:"2"}],N=[{label:"车位锁离线",value:"1"},{label:"V1离线",value:"2"},{label:"电量过低",value:"3"},{label:"订单欠费",value:"4"}];return(s,o)=>{const V=r("el-option"),T=r("el-select"),c=r("el-form-item"),P=r("el-input"),C=r("el-button"),j=r("el-form"),i=r("el-table-column"),x=r("dict-tag"),A=r("el-table"),Q=r("pagination"),E=M("loading");return d(),f("div",$,[v(l(j,{model:t(a),ref:"queryRef",inline:!0},{default:n(()=>[l(c,{label:"预警类型",prop:"alertType"},{default:n(()=>[l(T,{modelValue:t(a).alertType,"onUpdate:modelValue":o[0]||(o[0]=e=>t(a).alertType=e),placeholder:"请选择预警类型",style:{width:"200px"},onKeyup:y(p,["enter"])},{default:n(()=>[(d(),f(B,null,K(w,e=>l(V,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),l(c,{label:"预警状况",prop:"alertState"},{default:n(()=>[l(T,{modelValue:t(a).alertState,"onUpdate:modelValue":o[1]||(o[1]=e=>t(a).alertState=e),placeholder:"请选择预警状况",style:{width:"200px"},onKeyup:y(p,["enter"])},{default:n(()=>[(d(),f(B,null,K(N,e=>l(V,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),l(c,{label:"车场",prop:"parkName"},{default:n(()=>[l(P,{modelValue:t(a).parkName,"onUpdate:modelValue":o[2]||(o[2]=e=>t(a).parkName=e),placeholder:"请输入车场名称",clearable:"",style:{width:"200px"},onKeyup:y(p,["enter"])},null,8,["modelValue"])]),_:1}),l(c,null,{default:n(()=>[l(C,{type:"primary",icon:"Search",onClick:p},{default:n(()=>[b("搜索")]),_:1}),l(C,{icon:"Refresh",onClick:I},{default:n(()=>[b("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[q,t(U)]]),v((d(),X(A,{data:t(S),onSelectionChange:L},{default:n(()=>[l(i,{label:"序号",align:"center",type:"index",width:"100"}),l(i,{label:"预警类型",align:"center",prop:"alertType"},{default:n(e=>[l(x,{value:e.row.alertType,options:w},null,8,["value"])]),_:1}),l(i,{label:"预警状况",align:"center",prop:"alertState"},{default:n(e=>[l(x,{value:e.row.alertState,options:N},null,8,["value"])]),_:1}),l(i,{label:"车场",align:"center",prop:"parkName"}),l(i,{label:"地址",align:"center",prop:"parkAddress"}),l(i,{label:"区域",align:"center",prop:"location"}),l(i,{label:"车位号",align:"center",prop:"code"}),l(i,{label:"预警时间",align:"center",prop:"alertTime"},{default:n(e=>[b(Y(s.parseTime(e.row.alertTime,"{y}-{m}-{d} {h}:{i}:{s}")),1)]),_:1})]),_:1},8,["data"])),[[E,t(m)]]),v(l(Q,{total:t(_),page:t(a).pageNum,"onUpdate:page":o[3]||(o[3]=e=>t(a).pageNum=e),limit:t(a).pageSize,"onUpdate:limit":o[4]||(o[4]=e=>t(a).pageSize=e),onPagination:g},null,8,["total","page","limit"]),[[q,t(_)>0]])])}}});export{re as default};
