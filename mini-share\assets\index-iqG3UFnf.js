import{T,B as j,d as F,r as d,e as s,G as V,c as O,o as b,h as e,H as y,i as l,k as C,j as t,v as k,s as L,f as B,A as D,I as Q,D as S}from"./index-DDqcBwar.js";function z(c){return T({url:"/monitor/online/list",method:"get",params:c})}function A(c){return T({url:"/monitor/online/"+c,method:"delete"})}const E={class:"app-container"},G=j({name:"Online"}),M=Object.assign(G,{setup(c){const{proxy:f}=F(),N=d([]),_=d(!0),g=d(0),r=d(1),u=d(10),p=d({ipaddr:void 0,userName:void 0});function w(){_.value=!0,z(p.value).then(i=>{N.value=i.rows,g.value=i.total,_.value=!1})}function m(){r.value=1,w()}function I(){f.resetForm("queryRef"),m()}function R(i){f.$modal.confirm('是否确认强退名称为"'+i.userName+'"的用户?').then(function(){return A(i.tokenId)}).then(()=>{w(),f.$modal.msgSuccess("删除成功")}).catch(()=>{})}return w(),(i,a)=>{const x=s("el-input"),h=s("el-form-item"),v=s("el-button"),U=s("el-form"),n=s("el-table-column"),$=s("el-table"),q=s("pagination"),K=V("hasPermi"),P=V("loading");return b(),O("div",E,[e(U,{model:t(p),ref:"queryRef",inline:!0},{default:l(()=>[e(h,{label:"登录地址",prop:"ipaddr"},{default:l(()=>[e(x,{modelValue:t(p).ipaddr,"onUpdate:modelValue":a[0]||(a[0]=o=>t(p).ipaddr=o),placeholder:"请输入登录地址",clearable:"",style:{width:"200px"},onKeyup:C(m,["enter"])},null,8,["modelValue"])]),_:1}),e(h,{label:"用户名称",prop:"userName"},{default:l(()=>[e(x,{modelValue:t(p).userName,"onUpdate:modelValue":a[1]||(a[1]=o=>t(p).userName=o),placeholder:"请输入用户名称",clearable:"",style:{width:"200px"},onKeyup:C(m,["enter"])},null,8,["modelValue"])]),_:1}),e(h,null,{default:l(()=>[e(v,{type:"primary",icon:"Search",onClick:m},{default:l(()=>[k("搜索")]),_:1}),e(v,{icon:"Refresh",onClick:I},{default:l(()=>[k("重置")]),_:1})]),_:1})]),_:1},8,["model"]),y((b(),L($,{data:t(N).slice((t(r)-1)*t(u),t(r)*t(u)),style:{width:"100%"}},{default:l(()=>[e(n,{label:"序号",width:"50",type:"index",align:"center"},{default:l(o=>[B("span",null,D((t(r)-1)*t(u)+o.$index+1),1)]),_:1}),e(n,{label:"会话编号",align:"center",prop:"tokenId","show-overflow-tooltip":!0}),e(n,{label:"登录名称",align:"center",prop:"userName","show-overflow-tooltip":!0}),e(n,{label:"所属部门",align:"center",prop:"deptName","show-overflow-tooltip":!0}),e(n,{label:"主机",align:"center",prop:"ipaddr","show-overflow-tooltip":!0}),e(n,{label:"登录地点",align:"center",prop:"loginLocation","show-overflow-tooltip":!0}),e(n,{label:"操作系统",align:"center",prop:"os","show-overflow-tooltip":!0}),e(n,{label:"浏览器",align:"center",prop:"browser","show-overflow-tooltip":!0}),e(n,{label:"登录时间",align:"center",prop:"loginTime",width:"180"},{default:l(o=>[B("span",null,D(i.parseTime(o.row.loginTime)),1)]),_:1}),e(n,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(o=>[y((b(),L(v,{link:"",type:"primary",icon:"Delete",onClick:H=>R(o.row)},{default:l(()=>[k("强退")]),_:2},1032,["onClick"])),[[K,["monitor:online:forceLogout"]]])]),_:1})]),_:1},8,["data"])),[[P,t(_)]]),y(e(q,{total:t(g),page:t(r),"onUpdate:page":a[2]||(a[2]=o=>S(r)?r.value=o:null),limit:t(u),"onUpdate:limit":a[3]||(a[3]=o=>S(u)?u.value=o:null)},null,8,["total","page","limit"]),[[Q,t(g)>0]])])}}});export{M as default};
