import request from '@/utils/request'
// 查询预警列表
export function listWarning(query) {
  return request({
    url: '/system/alert/list',
    method: 'get',
    params: query
  })
}
//通过 tagId 查询可预警的人员
export function getWarningByTagId(query) {
  return request({
    url: '/system/alert/get',
    method: 'get',
    params: query
  })
}
// 查询标签列表
export function getTagList() {
  return request({
    url: '/system/tag/getList',
    method: 'get'
  })
}
// 查询预警详细
export function getWarning(warningId) {
  return request({
    url: '/system/alert/getBy/' + warningId,
    method: 'get'
  })
}
// 新增预警
export function addWarning(data) {
  return request({
    url: '/system/alert/add',
    method: 'post',
    data: data
  })
}
// 修改预警
export function updateWarning(data) {
  return request({
    url: '/system/alert/update',
    method: 'put',
    data: data
  })
}
// 删除预警
export function delWarning(warningId) {
  return request({
    url: '/system/alert/' + warningId,
    method: 'delete'
  })
}