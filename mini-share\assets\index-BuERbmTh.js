import{d as S,u as B,a as z,g as L,l as Q}from"./inventory-BKeIVDEr.js";import{_ as U,e as a,G as b,c as I,o as m,H as u,h as e,I as k,i as t,k as g,v as d,s as c,f as C}from"./index-DDqcBwar.js";const K={name:"Inventory",data(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,inventoryList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,lockNo:null,phone:null,ifBind:null},form:{},rules:{}}},created(){this.getList()},methods:{getList(){this.loading=!0,Q(this.queryParams).then(i=>{this.inventoryList=i.rows,this.total=i.total,this.loading=!1})},cancel(){this.open=!1,this.reset()},reset(){this.form={id:null,lockNo:null,phone:null,ifBind:null,createdTime:null,updatedTime:null},this.resetForm("form")},handleQuery(){this.queryParams.pageNum=1,this.getList()},resetQuery(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange(i){this.ids=i.map(n=>n.id),this.single=i.length!==1,this.multiple=!i.length},handleAdd(){this.reset(),this.open=!0,this.title="添加锁库存"},handleUpdate(i){this.reset(),i.id||this.ids,L().then(n=>{this.form=n.data,this.open=!0,this.title="修改锁库存"})},submitForm(){this.$refs.form.validate(i=>{i&&(this.form.id!=null?B(this.form).then(n=>{this.$modal.msgSuccess("修改成功"),this.open=!1,this.getList()}):z(this.form).then(n=>{this.$modal.msgSuccess("新增成功"),this.open=!1,this.getList()}))})},handleDelete(i){const n=i.id||this.ids;this.$modal.confirm('是否确认删除锁库存编号为"'+n+'"的数据项？').then(function(){return S(n)}).then(()=>{this.getList(),this.$modal.msgSuccess("删除成功")}).catch(()=>{})},handleExport(){this.download("system/inventory/export",{...this.queryParams},`inventory_${new Date().getTime()}.xlsx`)}}},D={class:"app-container"},F={"slot-scope":"scope"},T={slot:"footer",class:"dialog-footer"};function E(i,n,A,G,l,o){const h=a("el-input"),p=a("el-form-item"),r=a("el-button"),v=a("el-form"),_=a("el-col"),w=a("right-toolbar"),V=a("el-row"),f=a("el-table-column"),P=a("el-table"),x=a("pagination"),q=a("el-dialog"),y=b("hasPermi"),N=b("loading");return m(),I("div",D,[u(e(v,{model:l.queryParams,ref:"queryForm",size:"small",inline:!0,"label-width":"68px"},{default:t(()=>[e(p,{label:"锁编号",prop:"lockNo"},{default:t(()=>[e(h,{modelValue:l.queryParams.lockNo,"onUpdate:modelValue":n[0]||(n[0]=s=>l.queryParams.lockNo=s),placeholder:"请输入锁编号",clearable:"",onKeyup:g(o.handleQuery,["enter","native"])},null,8,["modelValue","onKeyup"])]),_:1}),e(p,{label:"锁绑定手机号",prop:"phone"},{default:t(()=>[e(h,{modelValue:l.queryParams.phone,"onUpdate:modelValue":n[1]||(n[1]=s=>l.queryParams.phone=s),placeholder:"请输入锁绑定手机号",clearable:"",onKeyup:g(o.handleQuery,["enter","native"])},null,8,["modelValue","onKeyup"])]),_:1}),e(p,{label:"状态",prop:"ifBind"},{default:t(()=>[e(h,{modelValue:l.queryParams.ifBind,"onUpdate:modelValue":n[2]||(n[2]=s=>l.queryParams.ifBind=s),placeholder:"请输入状态",clearable:"",onKeyup:g(o.handleQuery,["enter","native"])},null,8,["modelValue","onKeyup"])]),_:1}),e(p,null,{default:t(()=>[e(r,{type:"primary",icon:"el-icon-search",size:"mini",onClick:o.handleQuery},{default:t(()=>[d("搜索")]),_:1},8,["onClick"]),e(r,{icon:"el-icon-refresh",size:"mini",onClick:o.resetQuery},{default:t(()=>[d("重置")]),_:1},8,["onClick"])]),_:1})]),_:1},8,["model"]),[[k,l.showSearch]]),e(V,{gutter:10,class:"mb8"},{default:t(()=>[e(_,{span:1.5},{default:t(()=>[u((m(),c(r,{type:"primary",plain:"",icon:"el-icon-plus",size:"mini",onClick:o.handleAdd},{default:t(()=>[d("新增")]),_:1},8,["onClick"])),[[y,["system:inventory:add"]]])]),_:1}),e(_,{span:1.5},{default:t(()=>[u((m(),c(r,{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:l.single,onClick:o.handleUpdate},{default:t(()=>[d("修改")]),_:1},8,["disabled","onClick"])),[[y,["system:inventory:edit"]]])]),_:1}),e(_,{span:1.5},{default:t(()=>[u((m(),c(r,{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:l.multiple,onClick:o.handleDelete},{default:t(()=>[d("删除")]),_:1},8,["disabled","onClick"])),[[y,["system:inventory:remove"]]])]),_:1}),e(_,{span:1.5},{default:t(()=>[u((m(),c(r,{type:"warning",plain:"",icon:"el-icon-download",size:"mini",onClick:o.handleExport},{default:t(()=>[d("导出")]),_:1},8,["onClick"])),[[y,["system:inventory:export"]]])]),_:1}),e(w,{showSearch:l.showSearch,onQueryTable:o.getList},null,8,["showSearch","onQueryTable"])]),_:1}),u((m(),c(P,{data:l.inventoryList,onSelectionChange:o.handleSelectionChange},{default:t(()=>[e(f,{type:"selection",width:"55",align:"center"}),e(f,{label:"主键id",align:"center",prop:"id"}),e(f,{label:"锁编号",align:"center",prop:"lockNo"}),e(f,{label:"锁绑定手机号",align:"center",prop:"phone"}),e(f,{label:"状态",align:"center",prop:"ifBind"}),e(f,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:t(()=>[C("template",F,[u((m(),c(r,{size:"mini",type:"text",icon:"el-icon-edit",onClick:n[3]||(n[3]=s=>o.handleUpdate(i.scope.row))},{default:t(()=>[d("修改")]),_:1})),[[y,["system:inventory:edit"]]]),u((m(),c(r,{size:"mini",type:"text",icon:"el-icon-delete",onClick:n[4]||(n[4]=s=>o.handleDelete(i.scope.row))},{default:t(()=>[d("删除")]),_:1})),[[y,["system:inventory:remove"]]])])]),_:1})]),_:1},8,["data","onSelectionChange"])),[[N,l.loading]]),u(e(x,{total:l.total,page:l.queryParams.pageNum,limit:l.queryParams.pageSize,onPagination:o.getList},null,8,["total","page","limit","onPagination"]),[[k,l.total>0]]),e(q,{title:l.title,visible:l.open,width:"500px","append-to-body":""},{default:t(()=>[e(v,{ref:"form",model:l.form,rules:l.rules,"label-width":"80px"},{default:t(()=>[e(p,{label:"锁编号",prop:"lockNo"},{default:t(()=>[e(h,{modelValue:l.form.lockNo,"onUpdate:modelValue":n[5]||(n[5]=s=>l.form.lockNo=s),placeholder:"请输入锁编号"},null,8,["modelValue"])]),_:1}),e(p,{label:"锁绑定手机号",prop:"phone"},{default:t(()=>[e(h,{modelValue:l.form.phone,"onUpdate:modelValue":n[6]||(n[6]=s=>l.form.phone=s),placeholder:"请输入锁绑定手机号"},null,8,["modelValue"])]),_:1}),e(p,{label:"状态",prop:"ifBind"},{default:t(()=>[e(h,{modelValue:l.form.ifBind,"onUpdate:modelValue":n[7]||(n[7]=s=>l.form.ifBind=s),placeholder:"请输入状态"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),C("div",T,[e(r,{type:"primary",onClick:o.submitForm},{default:t(()=>[d("确 定")]),_:1},8,["onClick"]),e(r,{onClick:o.cancel},{default:t(()=>[d("取 消")]),_:1},8,["onClick"])])]),_:1},8,["title","visible"])])}const J=U(K,[["render",E]]);export{J as default};
