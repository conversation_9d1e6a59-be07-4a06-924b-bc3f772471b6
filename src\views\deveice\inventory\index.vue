<!-- 
 * @description: k库存管理
 * @fileName: index.vue 
 * @author: 李文滔 
 * @date: 2025-04-02 21:18:57 
 * @path:  
 * @version: V1.0.0 
!-->
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="设备编号" prop="lockNo">
        <el-input
          v-model="queryParams.lockNo"
          placeholder="请输入标签名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入标签名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="ifBind">
        <el-select
          v-model="queryParams.ifBind"
          placeholder="请选择设备状态"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="item in status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="3">
        <el-card>
          <el-statistic
            title="库存总数"
            :value="inventoryData.totalInventories"
          />
        </el-card>
      </el-col>
      <el-col :span="3">
        <el-card>
          <el-statistic title="已绑定" :value="inventoryData.bindingCount" />
        </el-card>
      </el-col>
      <el-col :span="3">
        <el-card>
          <el-statistic title="未绑定" :value="inventoryData.unbindingCount" />
        </el-card>
      </el-col>
      <el-col :span="15" class="right-grid">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
           v-hasPermi="['device:inventory:add']"
          >新增</el-button
        >
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="tagList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column
        label="设备编号"
        align="center"
        prop="lockNo"
        :show-overflow-tooltip="true"
      />

      <el-table-column label="手机号" align="center" prop="phone" />
      <el-table-column label="绑定时间" align="center" prop="bindingTime" />
      <el-table-column label="状态" align="center" prop="ifBind">
        <template #default="scope">
          <el-tag v-if="scope.row.ifBind == 1" type="success">已绑定</el-tag>
          <el-tag v-else-if="scope.row.ifBind == 0" type="danger"
            >未绑定</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            v-hasPermi="['device:inventory:update']"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
             v-hasPermi="['device:inventory:delete']"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
          <el-button
            link
            type="primary"
              v-hasPermi="['device:inventory:code']"
            icon="Tickets"
            @click="handleQrcode(scope.row)"
            >二维码</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改库存对话框 -->
    <el-dialog :title="title" v-model="open" width="580px" append-to-body>
      <el-form ref="tagRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="设备编号" prop="lockNo">
              <el-input
                v-model="form.lockNo"
                placeholder="请输入设备编号"
                :disabled="form.inventoryId != undefined"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="手机号" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入手机号"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Notice">
import {
  listInventory,
  addInventory,
  updateInventory,
  delInventory,
  getInventory,
} from "@/api/deveice/inventory";

const { proxy } = getCurrentInstance();

const tagList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const inventoryData = ref({
  bindingCount: 0,
  unbindingCount: 0,
  totalInventories: 0,
});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    lockNo: undefined,
    phone: undefined,
    ifBind: undefined,
  },
  rules: {
    lockNo: [
      { required: true, message: "设备编号不能为空", trigger: "blur" },
    ],
    phone: [{ required: true, message: "手机号不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  listInventory(queryParams.value).then((response) => {
    tagList.value = response.rows;
    total.value = parseInt(response.total);
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
    status: "0",
  };
  proxy.resetForm("tagRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加库存";
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset();
  open.value = true;
  title.value = "修改库存";
  form.value = row;
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["tagRef"].validate((valid) => {
    if (valid) {
      if (form.value.inventoryId != undefined) {
        updateInventory(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
          getStatistics();
        });
      } else {
        addInventory(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
          getStatistics();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const tagIds = row.inventoryId;
  proxy.$modal
    .confirm('是否确认删除库存编号为"' + tagIds + '"的数据项？')
    .then(function () {
      return delInventory(tagIds);
    })
    .then(() => {
      getList();
      getStatistics();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
//查询设备生成的二维码
function handleQrcode(row) {
  proxy.$modal.msgSuccess("二维码生成成功");
  open.value = false;
  getList();
}
// 获取统计数据
function getStatistics() {
  getInventory().then((response) => {
    inventoryData.value = response.data;
  });
}
getList();
getStatistics();
// 静态数据
const status = [
  { label: "已绑定", value: 1 },
  { label: "未绑定", value: 0 },
];
</script>
<style lang="scss" scoped>
.right-grid {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}
</style>
