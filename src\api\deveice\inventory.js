import request from '@/utils/request'
// 查询库存管理列表
export function listInventory(query) {
  return request({
    url: '/device/inventory/getInventoryList',
    method: 'get',
    params: query
  })
}
// 新增库存管理
export function addInventory(data) {
  return request({
    url: '/device/inventory',
    method: 'post',
    data: data
  })
}
// 修改库存管理
export function updateInventory(data) {
  return request({
    url: '/device/inventory',
    method: 'put',
    data: data
  })
}
// 删除库存管理
export function delInventory(id) {
  return request({
    url: '/device/inventory/' + id,
    method: 'delete'
  })
}
// 查询库存管理相关信息
export function getInventory() {
  return request({
    url: '/device/inventory',
    method: 'get'
  })
}