<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="订单号" prop="orderNo">
        <el-input
          v-model="queryParams.orderNo"
          placeholder="请输入订单号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车场" prop="parkName">
        <el-input
          v-model="queryParams.parkName"
          placeholder="请输入车场名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="下单时间" prop="dataRangeTime">
        <el-date-picker
          v-model="queryParams.dataRangeTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="To"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择订单状态"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="item in order_status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="tagList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column
        label="订单号"
        align="center"
        prop="orderNo"
        width="300px"
      />

      <el-table-column label="订单类型" align="center" prop="orderType">
        <template #default="scope">
          <dict-tag
            :options="order_type"
            :value="scope.row.orderType"
          ></dict-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="手机号"
        align="center"
        prop="phone"
        width="200px"
      />
      <el-table-column
        label="车牌号"
        align="center"
        prop="plateNo"
        width="100px"
      />
      <el-table-column
        label="车场"
        align="center"
        prop="parkName"
        width="200px"
      />
      <el-table-column label="地址" align="center" prop="parkAddress" />
      <el-table-column label="区域" align="center" prop="location" />
      <el-table-column label="车位号" align="center" prop="code" />
      <el-table-column
        label="下单时间"
        align="center"
        prop="orderTime"
        width="200px"
      >
        <template #default="scope">
          {{ parseTime(scope.row.orderTime) }}
        </template>
      </el-table-column>
      <el-table-column
        label="结束时间"
        align="center"
        prop="finishTime"
        width="200px"
      >
        <template #default="scope">
          {{ parseTime(scope.row.finishTime) }}
        </template>
      </el-table-column>

      <el-table-column
        label="应收金额"
        align="center"
        prop="payableAmount"
        width="120"
      >
        <template #default="scope"> {{ scope.row.payableAmount || 0 }}元 </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="120">
        <template #default="scope">
          <dict-tag
            :options="order_status"
            :value="scope.row.status + ''"
          ></dict-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="150"
        fixed="right"
      >
        <template #default="scope">
          <el-button
          
            link
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['device:order:query']"
            >查看详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <oderDetails v-model="open" :item="orderItem" type="2"></oderDetails>
  </div>
</template>

<script setup name="Notice">
import { listOrder, listLockOrder } from "@/api/deveice/order";
import { useRoute } from "vue-router";
import oderDetails from "./components/oderDetails.vue";

const { proxy } = getCurrentInstance();

const route = useRoute();
const tagList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const orderItem = ref({});

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNo: undefined,
    phone: undefined,
    parkName: undefined,
    plateNo:undefined,
    status: undefined,
    dataRangeTime: undefined,
  },
  rules: {},
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  const urlPath = route.query.lotId ? listLockOrder : listOrder;
  if (queryParams.value.dataRangeTime?.length > 0) {
    queryParams.value.orderBeginTime = queryParams.value.dataRangeTime[0];
    queryParams.value.orderEndTime = queryParams.value.dataRangeTime[1];
  } else {
    queryParams.value.orderBeginTime = undefined;
    queryParams.value.orderEndTime = undefined;
  }
  urlPath({
    ...queryParams.value,
    lotId: route.query.lotId,
  }).then((response) => {
    tagList.value = response.rows;
    total.value = parseInt(response.total);
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
    status: "0",
  };
  proxy.resetForm("tagRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;

  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.dataRangeTime = undefined;
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset();
  orderItem.value = row;
  open.value = true;
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["tagRef"].validate((valid) => {
    if (valid) {
      if (form.value.parkId != undefined) {
        updateTag(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addTag(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const tagIds = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除标签编号为"' + tagIds + '"的数据项？')
    .then(function () {
      return delTag(tagIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

getList();
// 静态数据
const order_type = [
  { label: "普通计费", value: "1" },
  { label: "特殊计费", value: "2" },
];

const order_status = [
  { label: "待交押金", value: "-1" },
  { label: "已交押金待入场", value: "0" },
  { label: "已入场", value: "3" },
  { label: "已完成", value: "1" },
  { label: "已取消(主动)", value: "2" },
  { label: "已取消(自动取消)", value: "4" },
  { label: "待补缴", value: "5" },
];
</script>
