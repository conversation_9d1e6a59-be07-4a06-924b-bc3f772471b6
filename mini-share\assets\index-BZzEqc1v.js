import{T as K,B as P,d as j,r as i,C as M,F as Q,e as o,G as A,c as h,o as _,H as g,I as w,j as l,h as a,i as r,k as G,J as H,K as J,v,s as O,A as W,W as X}from"./index-DDqcBwar.js";function Z(b){return K({url:"/system/customer/getFeedbackList",method:"get",params:b})}const $={class:"app-container"},ee=P({name:"Notice"}),ne=Object.assign(ee,{setup(b){const{proxy:R}=j(),k=i([]);i(!1);const c=i(!0),S=i(!0),C=i([]),N=i(!0),V=i(!0),d=i(0);i("");const x=M({form:{},queryParams:{pageNum:1,pageSize:10,feedbackType:void 0,dataRangeTime:void 0}}),{queryParams:e,form:ae,rules:te}=Q(x);function p(){c.value=!0,e.value.dataRangeTime?(e.value.feedbackStartTime=e.value.dataRangeTime[0],e.value.feedbackEndTime=e.value.dataRangeTime[1]):(e.value.feedbackStartTime=void 0,e.value.feedbackEndTime=void 0),Z(e.value).then(u=>{k.value=u.rows,d.value=u.total,c.value=!1})}function m(){e.value.pageNum=1,p()}function B(){R.resetForm("queryRef"),m()}function D(u){C.value=u.map(n=>n.noticeId),N.value=u.length!=1,V.value=!u.length}p();const y=[{label:"扣款异常",value:"1"},{label:"页面加载失败",value:"2"},{label:"保证金未返回",value:"3"},{label:"升降锁异常",value:"4"},{label:"其他",value:"5"}];return(u,n)=>{const F=o("el-option"),q=o("el-select"),f=o("el-form-item"),L=o("el-date-picker"),T=o("el-button"),U=o("el-form"),s=o("el-table-column"),Y=o("dict-tag"),z=o("el-table"),E=o("pagination"),I=A("loading");return _(),h("div",$,[g(a(U,{model:l(e),ref:"queryRef",inline:!0},{default:r(()=>[a(f,{label:"类型",prop:"feedbackType"},{default:r(()=>[a(q,{modelValue:l(e).feedbackType,"onUpdate:modelValue":n[0]||(n[0]=t=>l(e).feedbackType=t),placeholder:"请选择类型",style:{width:"200px"},onKeyup:G(m,["enter"])},{default:r(()=>[(_(),h(H,null,J(y,t=>a(F,{key:t.value,label:t.label,value:t.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),a(f,{label:"时间",prop:"dataRangeTime"},{default:r(()=>[a(L,{modelValue:l(e).dataRangeTime,"onUpdate:modelValue":n[1]||(n[1]=t=>l(e).dataRangeTime=t),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"To","start-placeholder":"开始时间","end-placeholder":"结束时间"},null,8,["modelValue"])]),_:1}),a(f,null,{default:r(()=>[a(T,{type:"primary",icon:"Search",onClick:m},{default:r(()=>[v("搜索")]),_:1}),a(T,{icon:"Refresh",onClick:B},{default:r(()=>[v("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[w,l(S)]]),g((_(),O(z,{data:l(k),onSelectionChange:D},{default:r(()=>[a(s,{label:"序号",align:"center",type:"index",width:"100"}),a(s,{label:"时间",align:"center",prop:"feedbackTime"},{default:r(t=>[v(W(l(X)(t.row.feedbackTime)),1)]),_:1}),a(s,{label:"账号",align:"center",prop:"weChatNickName"}),a(s,{label:"类型",align:"center",prop:"feedbackType"},{default:r(t=>[a(Y,{value:t.row.feedbackType,options:y},null,8,["value"])]),_:1}),a(s,{label:"描述",align:"center",prop:"remark"}),a(s,{label:"联系方式",align:"center",prop:"phone"})]),_:1},8,["data"])),[[I,l(c)]]),g(a(E,{total:l(d),page:l(e).pageNum,"onUpdate:page":n[2]||(n[2]=t=>l(e).pageNum=t),limit:l(e).pageSize,"onUpdate:limit":n[3]||(n[3]=t=>l(e).pageSize=t),onPagination:p},null,8,["total","page","limit"]),[[w,l(d)>0]])])}}});export{ne as default};
