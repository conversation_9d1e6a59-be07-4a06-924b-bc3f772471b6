import{_ as de,B as se,d as re,r as s,C as ce,F as pe,e as i,G as T,c as z,o as p,H as v,h as e,I as L,j as t,i as l,k as R,J as fe,K as me,v as r,s as g,t as _e,f as ve,D as ge}from"./index-DDqcBwar.js";import{l as ye,g as be,d as he,u as ke,a as Ce}from"./inventory-BKeIVDEr.js";const we={class:"app-container"},Ve={class:"dialog-footer"},Ne=se({name:"Notice"}),Ie=Object.assign(Ne,{setup(xe){const{proxy:f}=re(),q=s([]),c=s(!1),I=s(!0),Q=s(!0),j=s([]),E=s(!0),A=s(!0),x=s(0),S=s(""),C=s({bindingCount:0,unbindingCount:0,totalInventories:0}),G=ce({form:{},queryParams:{pageNum:1,pageSize:10,lockNo:void 0,phone:void 0,ifBind:void 0},rules:{lockNo:[{required:!0,message:"设备编号不能为空",trigger:"blur"}],phone:[{required:!0,message:"手机号不能为空",trigger:"blur"}]}}),{queryParams:u,form:d,rules:H}=pe(G);function _(){I.value=!0,ye(u.value).then(a=>{q.value=a.rows,x.value=a.total,I.value=!1})}function J(){c.value=!1,B()}function B(){d.value={noticeId:void 0,noticeTitle:void 0,noticeType:void 0,noticeContent:void 0,status:"0"},f.resetForm("tagRef")}function k(){u.value.pageNum=1,_()}function O(){f.resetForm("queryRef"),k()}function M(a){j.value=a.map(o=>o.noticeId),E.value=a.length!=1,A.value=!a.length}function W(){B(),c.value=!0,S.value="添加库存"}function X(a){B(),c.value=!0,S.value="修改库存",d.value=a}function Y(){f.$refs.tagRef.validate(a=>{a&&(d.value.inventoryId!=null?ke(d.value).then(o=>{f.$modal.msgSuccess("修改成功"),c.value=!1,_(),w()}):Ce(d.value).then(o=>{f.$modal.msgSuccess("新增成功"),c.value=!1,_(),w()}))})}function Z(a){const o=a.inventoryId;f.$modal.confirm('是否确认删除库存编号为"'+o+'"的数据项？').then(function(){return he(o)}).then(()=>{_(),w(),f.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ee(a){f.$modal.msgSuccess("二维码生成成功"),c.value=!1,_()}function w(){be().then(a=>{C.value=a.data})}_(),w();const le=[{label:"已绑定",value:1},{label:"未绑定",value:0}];return(a,o)=>{const V=i("el-input"),y=i("el-form-item"),te=i("el-option"),ne=i("el-select"),m=i("el-button"),D=i("el-form"),U=i("el-statistic"),$=i("el-card"),b=i("el-col"),P=i("el-row"),h=i("el-table-column"),F=i("el-tag"),oe=i("el-table"),ae=i("pagination"),ie=i("el-dialog"),N=T("hasPermi"),ue=T("loading");return p(),z("div",we,[v(e(D,{model:t(u),ref:"queryRef",inline:!0},{default:l(()=>[e(y,{label:"设备编号",prop:"lockNo"},{default:l(()=>[e(V,{modelValue:t(u).lockNo,"onUpdate:modelValue":o[0]||(o[0]=n=>t(u).lockNo=n),placeholder:"请输入标签名称",clearable:"",style:{width:"200px"},onKeyup:R(k,["enter"])},null,8,["modelValue"])]),_:1}),e(y,{label:"手机号",prop:"phone"},{default:l(()=>[e(V,{modelValue:t(u).phone,"onUpdate:modelValue":o[1]||(o[1]=n=>t(u).phone=n),placeholder:"请输入标签名称",clearable:"",style:{width:"200px"},onKeyup:R(k,["enter"])},null,8,["modelValue"])]),_:1}),e(y,{label:"状态",prop:"ifBind"},{default:l(()=>[e(ne,{modelValue:t(u).ifBind,"onUpdate:modelValue":o[2]||(o[2]=n=>t(u).ifBind=n),placeholder:"请选择设备状态",style:{width:"200px"},onKeyup:R(k,["enter"])},{default:l(()=>[(p(),z(fe,null,me(le,n=>e(te,{key:n.value,label:n.label,value:n.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(y,null,{default:l(()=>[e(m,{type:"primary",icon:"Search",onClick:k},{default:l(()=>[r("搜索")]),_:1}),e(m,{icon:"Refresh",onClick:O},{default:l(()=>[r("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[L,t(Q)]]),e(P,{gutter:10,class:"mb8"},{default:l(()=>[e(b,{span:3},{default:l(()=>[e($,null,{default:l(()=>[e(U,{title:"库存总数",value:t(C).totalInventories},null,8,["value"])]),_:1})]),_:1}),e(b,{span:3},{default:l(()=>[e($,null,{default:l(()=>[e(U,{title:"已绑定",value:t(C).bindingCount},null,8,["value"])]),_:1})]),_:1}),e(b,{span:3},{default:l(()=>[e($,null,{default:l(()=>[e(U,{title:"未绑定",value:t(C).unbindingCount},null,8,["value"])]),_:1})]),_:1}),e(b,{span:15,class:"right-grid"},{default:l(()=>[v((p(),g(m,{type:"primary",plain:"",icon:"Plus",onClick:W},{default:l(()=>[r("新增")]),_:1})),[[N,["device:inventory:add"]]])]),_:1})]),_:1}),v((p(),g(oe,{data:t(q),onSelectionChange:M},{default:l(()=>[e(h,{label:"序号",align:"center",type:"index",width:"100"}),e(h,{label:"设备编号",align:"center",prop:"lockNo","show-overflow-tooltip":!0}),e(h,{label:"手机号",align:"center",prop:"phone"}),e(h,{label:"绑定时间",align:"center",prop:"bindingTime"}),e(h,{label:"状态",align:"center",prop:"ifBind"},{default:l(n=>[n.row.ifBind==1?(p(),g(F,{key:0,type:"success"},{default:l(()=>[r("已绑定")]),_:1})):n.row.ifBind==0?(p(),g(F,{key:1,type:"danger"},{default:l(()=>[r("未绑定")]),_:1})):_e("",!0)]),_:1}),e(h,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(n=>[v((p(),g(m,{link:"",type:"primary",icon:"Edit",onClick:K=>X(n.row)},{default:l(()=>[r("修改")]),_:2},1032,["onClick"])),[[N,["device:inventory:update"]]]),v((p(),g(m,{link:"",type:"primary",icon:"Delete",onClick:K=>Z(n.row)},{default:l(()=>[r("删除")]),_:2},1032,["onClick"])),[[N,["device:inventory:delete"]]]),v((p(),g(m,{link:"",type:"primary",icon:"Tickets",onClick:K=>ee(n.row)},{default:l(()=>[r("二维码")]),_:2},1032,["onClick"])),[[N,["device:inventory:code"]]])]),_:1})]),_:1},8,["data"])),[[ue,t(I)]]),v(e(ae,{total:t(x),page:t(u).pageNum,"onUpdate:page":o[3]||(o[3]=n=>t(u).pageNum=n),limit:t(u).pageSize,"onUpdate:limit":o[4]||(o[4]=n=>t(u).pageSize=n),onPagination:_},null,8,["total","page","limit"]),[[L,t(x)>0]]),e(ie,{title:t(S),modelValue:t(c),"onUpdate:modelValue":o[7]||(o[7]=n=>ge(c)?c.value=n:null),width:"580px","append-to-body":""},{footer:l(()=>[ve("div",Ve,[e(m,{type:"primary",onClick:Y},{default:l(()=>[r("确 定")]),_:1}),e(m,{onClick:J},{default:l(()=>[r("取 消")]),_:1})])]),default:l(()=>[e(D,{ref:"tagRef",model:t(d),rules:t(H),"label-width":"80px"},{default:l(()=>[e(P,null,{default:l(()=>[e(b,{span:24},{default:l(()=>[e(y,{label:"设备编号",prop:"lockNo"},{default:l(()=>[e(V,{modelValue:t(d).lockNo,"onUpdate:modelValue":o[5]||(o[5]=n=>t(d).lockNo=n),placeholder:"请输入设备编号",disabled:t(d).inventoryId!=null},null,8,["modelValue","disabled"])]),_:1})]),_:1}),e(b,{span:24},{default:l(()=>[e(y,{label:"手机号",prop:"phone"},{default:l(()=>[e(V,{modelValue:t(d).phone,"onUpdate:modelValue":o[6]||(o[6]=n=>t(d).phone=n),placeholder:"请输入手机号",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}}),Ue=de(Ie,[["__scopeId","data-v-222acec9"]]);export{Ue as default};
