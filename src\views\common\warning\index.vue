<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="标签名称" prop="tagName">
        <el-input
          v-model="queryParams.tagName"
          placeholder="请输入标签名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:alert:add']"
          >新增</el-button
        >
      </el-col>

      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="warnList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column
        label="标签名称"
        align="center"
        prop="tagName"
        :show-overflow-tooltip="true"
      />

      <el-table-column label="用户昵称" align="center" prop="nicknames" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :value="scope.row.status" :options="warningStatus" />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:alert:update']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:alert:delete']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" v-model="open" width="580px" append-to-body>
      <el-form ref="tagRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="部门标签" prop="tagId">
              <el-select
                v-model="form.tagId"
                placeholder="请选择部门标签"
                clearable
                @change="tagChange"
              >
                <el-option
                  v-for="item in tagList"
                  :key="item.tagId"
                  :label="item.tagName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="人员" prop="userIds">
              <el-select
                v-model="form.userIds"
                placeholder="请选择人员"
                clearable
                multiple
              >
                <el-option
                  v-for="item in tagPeople"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId + ''"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option
                  v-for="item in warningStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="Number(item.value)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Notice">
import {
  listWarning,
  getWarningByTagId,
  getTagList,
  getWarning,
  addWarning,
  updateWarning,
} from "@/api/common/warning";

const { proxy } = getCurrentInstance();

const tagList = ref([]);
const warnList = ref([]);
const tagPeople = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    tagName: undefined,
  },
  rules: {
    tagId: [{ required: true, message: "标签类型不能为空", trigger: "change" }],
    userIds: [{ required: true, message: "人员不能为空", trigger: "change" }],
    status: [{ required: true, message: "状态不能为空", trigger: "change" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  listWarning(queryParams.value).then((response) => {
    warnList.value = response.rows;
    total.value = parseInt(response.total);
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    status: undefined,
    tagId: undefined,
    userIds: [],
  };
  proxy.resetForm("tagRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加预警人员";
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset();
  const tagId = row.id || ids.value;
  getWarning(tagId).then(async (response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改预警人员";
    form.value.userIds = [];
    await tagChange(response.data.tagId);
    for (var i in response.data.sysUsers) {
      form.value.userIds.push(i);
    }
    console.log(form.value.userIds);
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["tagRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != undefined) {
        updateWarning(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addWarning(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const tagIds = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删预警编号为"' + tagIds + '"的数据项？')
    .then(function () {
      return delWarning(tagIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
// 获取所有标签
function getAllTag() {
  getTagList().then((response) => {
    tagList.value = response.data;
  });
}
//  获取预警人员
async function tagChange(tagId) {
  return getWarningByTagId({
    tagId: tagId,
  }).then((response) => {
    tagPeople.value = response.data;
  });
}
getAllTag();
// 预警状态
const warningStatus = [
  { label: "正常", value: "0" },
  { label: "异常", value: "1" },
];
getList();
</script>
