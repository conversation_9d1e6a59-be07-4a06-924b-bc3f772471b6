import{B as P,d as R,r as s,C as U,F as z,e as a,G as j,c as D,o as v,H as d,I as y,j as e,h as t,i as l,k as F,v as b,s as I}from"./index-DDqcBwar.js";import{a as K}from"./user-C3QdTmTM.js";const L={class:"app-container"},Q=P({name:"Notice"}),W=Object.assign(Q,{setup(E){const{proxy:w}=R(),f=s([]);s(!1);const i=s(!0),N=s(!0),c=s(0),x=U({form:{},queryParams:{pageNum:1,pageSize:10}}),{queryParams:o,form:G,rules:H}=z(x);function p(){i.value=!0,K(o.value).then(m=>{f.value=m.rows,c.value=m.total,i.value=!1})}function u(){o.value.pageNum=1,p()}function k(){w.resetForm("queryRef"),u()}return p(),(m,n)=>{const C=a("el-input"),g=a("el-form-item"),h=a("el-button"),S=a("el-form"),_=a("el-table-column"),B=a("el-table"),V=a("pagination"),q=j("loading");return v(),D("div",L,[d(t(S,{model:e(o),ref:"queryRef",inline:!0},{default:l(()=>[t(g,{label:"手机号",prop:"phone"},{default:l(()=>[t(C,{modelValue:e(o).phone,"onUpdate:modelValue":n[0]||(n[0]=r=>e(o).phone=r),placeholder:"请输入标签名称",clearable:"",style:{width:"200px"},onKeyup:F(u,["enter"])},null,8,["modelValue"])]),_:1}),t(g,null,{default:l(()=>[t(h,{type:"primary",icon:"Search",onClick:u},{default:l(()=>[b("搜索")]),_:1}),t(h,{icon:"Refresh",onClick:k},{default:l(()=>[b("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[y,e(N)]]),d((v(),I(B,{data:e(f)},{default:l(()=>[t(_,{label:"序号",align:"center",type:"index",width:"100"}),t(_,{label:"名称",align:"center",prop:"wechatNickName","show-overflow-tooltip":!0}),t(_,{label:"手机号",align:"center",prop:"phone"})]),_:1},8,["data"])),[[q,e(i)]]),d(t(V,{total:e(c),page:e(o).pageNum,"onUpdate:page":n[1]||(n[1]=r=>e(o).pageNum=r),limit:e(o).pageSize,"onUpdate:limit":n[2]||(n[2]=r=>e(o).pageSize=r),onPagination:p},null,8,["total","page","limit"]),[[y,e(c)>0]])])}}});export{W as default};
