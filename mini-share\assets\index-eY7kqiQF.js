import{B as Pe,d as Ue,a as xe,r as _,C as qe,F as Se,e as o,G,c as P,o as i,h as e,i as l,j as n,f as H,H as k,k as B,J as F,K,v as s,I as W,s as b,D as X,t as $e}from"./index-DDqcBwar.js";import{l as Ae,m as Oe,n as Re,o as De,p as ze,q as Be}from"./park-DZi9DkwV.js";import{M as Fe,g as Y}from"./splitpanes-DR_7Z2MZ.js";const Ke={class:"app-container"},Le={class:"head-container"},Je={class:"dialog-footer"},Qe=Pe({name:"Park"}),He=Object.assign(Qe,{setup(je){const{proxy:y}=Ue(),T=xe(),L=_([]),h=_(!1),U=_(!0),I=_(!0),x=_([]),Z=_(!0),ee=_(!0),q=_(0),S=_(""),$=_(void 0),J=_(void 0),le=qe({form:{},queryParams:{pageNum:1,pageSize:10,parkName:null,parkAddress:null,parkPhone:null,status:null,longitude:null,latitude:null,menuNumber:null,parkType:null,remark:null},rules:{parkType:[{required:!0,message:"请选择车场类型",trigger:"change"}],parkName:[{required:!0,message:"请输入车场名称",trigger:"blur"}],longitude:[{required:!0,message:"请输入经度",trigger:"blur"}],latitude:[{required:!0,message:"请输入纬度",trigger:"blur"}],parkPhone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3456789]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],parkAddress:[{required:!0,message:"请输入车场地址",trigger:"blur"}],areaId:[{required:!0,message:"请选择地区",trigger:"change"}],freeInTime:[{required:!0,message:"请输入入场免费时间",trigger:"blur"},{pattern:/^[0-9]*[1-9][0-9]*$/,message:"请输入正整数",trigger:"blur"}],freeOutTime:[{required:!0,message:"请输入出场免费时间",trigger:"blur"},{pattern:/^[0-9]*[1-9][0-9]*$/,message:"请输入正整数",trigger:"blur"}],unitCharge:[{required:!0,message:"请输入单价",trigger:"blur"},{pattern:/^[0-9]+(.[0-9]{1,2})?$/,message:"请输入正确的金额",trigger:"blur"}],timeUnit:[{required:!0,message:"请选择单价单位",trigger:"change"}]}}),{queryParams:p,form:r,rules:ae}=Se(le);function V(){U.value=!0,Ae(p.value).then(u=>{L.value=u.rows,q.value=u.total,U.value=!1})}function te(){h.value=!1,A()}function A(){r.value={id:null,parkId:null,parkName:null,parkAddress:null,parkPhone:null,status:null,longitude:null,latitude:null,menuNumber:null,parkType:null,remark:null,createdTime:null,updatedTime:null,illegalMultiple:4},y.resetForm("parkRef")}function C(){p.value.pageNum=1,V()}function ne(){y.resetForm("queryRef"),C()}function re(u){x.value=u.map(t=>t.id),Z.value=u.length!=1,ee.value=!u.length}function ue(){A(),h.value=!0,S.value="添加车场"}function oe(u){A();const t=u.parkId||x.value;Re(t).then(O=>{r.value=O.data,h.value=!0,S.value="修改车场"})}function de(){y.$refs.parkRef.validate(u=>{u&&(r.value.parkId!=null?ze(r.value).then(t=>{y.$modal.msgSuccess("修改成功"),h.value=!1,V()}):Be(r.value).then(t=>{y.$modal.msgSuccess("新增成功"),h.value=!1,V()}))})}function pe(u){const t=u.id||x.value;y.$modal.confirm('是否确认删除车场编号为"'+t+'"的数据项？').then(function(){return De(t)}).then(()=>{V(),y.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ie(){Oe().then(u=>{$.value=y.handleTree(u.data,"areaId"),J.value=Q(JSON.parse(JSON.stringify($.value)))})}function Q(u){return u.filter(t=>t.disabled?!1:(t.children&&t.children.length&&(t.children=Q(t.children)),!0))}const se=(u,t)=>u?t.label.indexOf(u)!==-1:!0;function me(u){T.push({name:"StaffConfig",query:{parkId:u.parkId}})}function fe(u){T.push({name:"AdminStaffConfig",query:{parkId:u.parkId}})}function ce(u){T.push({name:"ParkRecord",query:{parkNo:u.parkNo}})}function ge(u){T.push({name:"DeviceRecord",query:{parkNo:u.parkNo}})}function _e(u){p.value.areaId=u.areaId,C()}V(),ie();const j=[{label:"开放式车场",value:0},{label:"封闭式车场",value:1},{label:"路边式车场",value:2}],ke=[{label:"开放式车场",value:"0"},{label:"封闭式车场",value:"1"},{label:"路边式车场",value:"2"}],be=[{label:"分钟",value:1},{label:"小时",value:2}];return(u,t)=>{const O=o("el-tree"),m=o("el-col"),c=o("el-input"),d=o("el-form-item"),R=o("el-option"),D=o("el-select"),f=o("el-button"),M=o("el-form"),ye=o("right-toolbar"),v=o("el-row"),g=o("el-table-column"),he=o("dict-tag"),E=o("el-tag"),z=o("el-dropdown-item"),ve=o("el-dropdown-menu"),Ve=o("el-dropdown"),we=o("el-table"),Ce=o("pagination"),Ne=o("el-tree-select"),Te=o("el-dialog"),w=G("hasPermi"),Ie=G("loading");return i(),P("div",Ke,[e(v,null,{default:l(()=>[e(n(Fe),{class:"default-theme"},{default:l(()=>[e(n(Y),{size:"16"},{default:l(()=>[e(m,null,{default:l(()=>[H("div",Le,[e(O,{data:n($),props:{label:"areaName",children:"children"},"expand-on-click-node":!1,"filter-node-method":se,ref:"deptTreeRef","node-key":"areaId","highlight-current":"","default-expand-all":"",onNodeClick:_e},null,8,["data"])])]),_:1})]),_:1}),e(n(Y),{size:"84"},{default:l(()=>[e(m,null,{default:l(()=>[k(e(M,{model:n(p),ref:"queryRef",inline:!0,"label-width":"80px"},{default:l(()=>[e(d,{label:"车场名称",prop:"parkName"},{default:l(()=>[e(c,{modelValue:n(p).parkName,"onUpdate:modelValue":t[0]||(t[0]=a=>n(p).parkName=a),placeholder:"请输入停车场名称",clearable:"",onKeyup:B(C,["enter"])},null,8,["modelValue"])]),_:1}),e(d,{label:"手机号",prop:"parkPhone"},{default:l(()=>[e(c,{modelValue:n(p).parkPhone,"onUpdate:modelValue":t[1]||(t[1]=a=>n(p).parkPhone=a),placeholder:"请输入手机号",clearable:"",onKeyup:B(C,["enter"])},null,8,["modelValue"])]),_:1}),e(d,{label:"类型",prop:"parkType"},{default:l(()=>[e(D,{modelValue:n(p).parkType,"onUpdate:modelValue":t[2]||(t[2]=a=>n(p).parkType=a),placeholder:"请选择车场类型",clearable:"",onKeyup:B(C,["enter"]),style:{width:"200px"}},{default:l(()=>[(i(),P(F,null,K(j,a=>e(R,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(d,null,{default:l(()=>[e(f,{type:"primary",icon:"Search",onClick:C},{default:l(()=>[s("搜索")]),_:1}),e(f,{icon:"Refresh",onClick:ne},{default:l(()=>[s("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[W,n(I)]]),e(v,{gutter:10,class:"mb8"},{default:l(()=>[e(m,{span:1.5},{default:l(()=>[k((i(),b(f,{type:"primary",plain:"",icon:"Plus",onClick:ue},{default:l(()=>[s("新增")]),_:1})),[[w,["system:park:add"]]])]),_:1}),e(ye,{showSearch:n(I),"onUpdate:showSearch":t[3]||(t[3]=a=>X(I)?I.value=a:null),onQueryTable:V},null,8,["showSearch"])]),_:1}),k((i(),b(we,{data:n(L),onSelectionChange:re},{default:l(()=>[e(g,{type:"selection",width:"55",align:"center"}),e(g,{label:"序号",align:"center",type:"index"}),e(g,{label:"车场名称",align:"center",prop:"parkName",width:"200px"}),e(g,{label:"车场地址",align:"center",prop:"parkAddress",width:"200px"}),e(g,{label:"手机号",align:"center",prop:"parkPhone",width:"200px"}),e(g,{label:"类型",align:"center",prop:"parkType",width:"100px"},{default:l(({row:a})=>[e(he,{value:a.parkType,options:ke},null,8,["value"])]),_:1}),e(g,{label:"入场免费时间",align:"center",prop:"freeInTime",width:"200px"}),e(g,{label:"出场免费时间",align:"center",prop:"freeOutTime",width:"200px"}),e(g,{label:"V1状态",align:"center",prop:"state"},{default:l(({row:a})=>[a.state==-1?(i(),b(E,{key:0,type:"success",size:"mini"},{default:l(()=>[s("不在线")]),_:1})):a.state==1?(i(),b(E,{key:1,type:"danger",size:"mini"},{default:l(()=>[s("在线")]),_:1})):$e("",!0)]),_:1}),e(g,{label:"单价",align:"center",prop:"unitCharge"}),e(g,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"300"},{default:l(a=>[k((i(),b(f,{link:"",type:"primary",onClick:N=>oe(a.row)},{default:l(()=>[s("编辑")]),_:2},1032,["onClick"])),[[w,["system:park:update"]]]),k((i(),b(f,{link:"",type:"primary",onClick:N=>me(a.row)},{default:l(()=>[s("人员管理")]),_:2},1032,["onClick"])),[[w,["system:park:user"]]]),k((i(),b(f,{link:"",type:"primary",onClick:N=>fe(a.row)},{default:l(()=>[s("管理员配置")]),_:2},1032,["onClick"])),[[w,["system:park:admin"]]]),e(Ve,null,{dropdown:l(()=>[e(ve,null,{default:l(()=>[e(z,null,{default:l(()=>[k((i(),b(f,{link:"",type:"primary",onClick:N=>ge(a.row)},{default:l(()=>[s("设备状态")]),_:2},1032,["onClick"])),[[w,["system:park:device:list"]]])]),_:2},1024),e(z,null,{default:l(()=>[k((i(),b(f,{link:"",type:"primary",onClick:N=>ce(a.row)},{default:l(()=>[s("进出记录")]),_:2},1032,["onClick"])),[[w,["system:park:record"]]])]),_:2},1024),e(z,null,{default:l(()=>[k((i(),b(f,{link:"",type:"primary",onClick:N=>pe(a.row)},{default:l(()=>[s("删除")]),_:2},1032,["onClick"])),[[w,["system:park:remove"]]])]),_:2},1024)]),_:2},1024)]),default:l(()=>[e(f,{link:"",type:"primary"},{default:l(()=>[s("更多")]),_:1})]),_:2},1024)]),_:1})]),_:1},8,["data"])),[[Ie,n(U)]]),k(e(Ce,{total:n(q),page:n(p).pageNum,"onUpdate:page":t[4]||(t[4]=a=>n(p).pageNum=a),limit:n(p).pageSize,"onUpdate:limit":t[5]||(t[5]=a=>n(p).pageSize=a),onPagination:V},null,8,["total","page","limit"]),[[W,n(q)>0]])]),_:1})]),_:1})]),_:1})]),_:1}),e(Te,{title:n(S),modelValue:n(h),"onUpdate:modelValue":t[18]||(t[18]=a=>X(h)?h.value=a:null),width:"800px","append-to-body":""},{footer:l(()=>[H("div",Je,[e(f,{type:"primary",onClick:de},{default:l(()=>[s("确 定")]),_:1}),e(f,{onClick:te},{default:l(()=>[s("取 消")]),_:1})])]),default:l(()=>[e(M,{ref:"parkRef",model:n(r),rules:n(ae),"label-width":"110px"},{default:l(()=>[e(d,{label:"车场类型",prop:"parkType"},{default:l(()=>[e(D,{modelValue:n(r).parkType,"onUpdate:modelValue":t[6]||(t[6]=a=>n(r).parkType=a),placeholder:"请选择车场类型"},{default:l(()=>[(i(),P(F,null,K(j,a=>e(R,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(v,null,{default:l(()=>[e(m,{span:24},{default:l(()=>[e(d,{label:"车场名称",prop:"parkName"},{default:l(()=>[e(c,{modelValue:n(r).parkName,"onUpdate:modelValue":t[7]||(t[7]=a=>n(r).parkName=a),placeholder:"请输入车场名称"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(d,{label:"车场地址",prop:"parkAddress"},{default:l(()=>[e(c,{modelValue:n(r).parkAddress,"onUpdate:modelValue":t[8]||(t[8]=a=>n(r).parkAddress=a),type:"textarea",rows:"5",placeholder:"请输入停车场详细地址"},null,8,["modelValue"])]),_:1}),e(v,null,{default:l(()=>[e(m,{span:12},{default:l(()=>[e(d,{label:"手机号",prop:"parkPhone"},{default:l(()=>[e(c,{modelValue:n(r).parkPhone,"onUpdate:modelValue":t[9]||(t[9]=a=>n(r).parkPhone=a),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{label:"地区",prop:"areaId"},{default:l(()=>[e(Ne,{modelValue:n(r).areaId,"onUpdate:modelValue":t[10]||(t[10]=a=>n(r).areaId=a),data:n(J),props:{value:"areaId",label:"areaName",children:"children"},"value-key":"areaId",placeholder:"请选择归属部门","check-strictly":""},null,8,["modelValue","data"])]),_:1})]),_:1})]),_:1}),e(v,null,{default:l(()=>[e(m,{span:12},{default:l(()=>[e(d,{label:"经度",prop:"longitude"},{default:l(()=>[e(c,{modelValue:n(r).longitude,"onUpdate:modelValue":t[11]||(t[11]=a=>n(r).longitude=a),placeholder:"请输入经度"},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{label:"纬度",prop:"latitude"},{default:l(()=>[e(c,{modelValue:n(r).latitude,"onUpdate:modelValue":t[12]||(t[12]=a=>n(r).latitude=a),placeholder:"请输入纬度"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(v,null,{default:l(()=>[e(m,{span:12},{default:l(()=>[e(d,{label:"入场免费时间",prop:"freeInTime"},{default:l(()=>[e(c,{modelValue:n(r).freeInTime,"onUpdate:modelValue":t[13]||(t[13]=a=>n(r).freeInTime=a),type:"number",placeholder:"请输入入场免费时间"},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{label:"出场免费时间",prop:"freeOutTime"},{default:l(()=>[e(c,{modelValue:n(r).freeOutTime,"onUpdate:modelValue":t[14]||(t[14]=a=>n(r).freeOutTime=a),type:"number",placeholder:"请输入出场免费时间"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(v,null,{default:l(()=>[e(m,{span:12},{default:l(()=>[e(d,{label:"单价",prop:"unitCharge"},{default:l(()=>[e(c,{modelValue:n(r).unitCharge,"onUpdate:modelValue":t[15]||(t[15]=a=>n(r).unitCharge=a),placeholder:"请输入单价"},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:12},{default:l(()=>[e(d,{label:"单价单位",prop:"timeUnit"},{default:l(()=>[e(D,{modelValue:n(r).timeUnit,"onUpdate:modelValue":t[16]||(t[16]=a=>n(r).timeUnit=a),placeholder:"请选择单价单位"},{default:l(()=>[(i(),P(F,null,K(be,a=>e(R,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),e(v,null,{default:l(()=>[e(m,{span:24},{default:l(()=>[e(d,{label:"车场备注",prop:"remark"},{default:l(()=>[e(c,{type:"textarea",rows:5,modelValue:n(r).remark,"onUpdate:modelValue":t[17]||(t[17]=a=>n(r).remark=a),placeholder:"请输入车场备注"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{He as default};
