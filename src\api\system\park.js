import request from '@/utils/request'

// 查询车场列表
export function listPark(query) {
  return request({
    url: '/system/park/getParks',
    method: 'get',
    params: query
  })
}

// 查询车场详细
export function getPark(id) {
  return request({
    url: '/system/park/getParkInfo/' + id,
    method: 'get'
  })
}

// 新增车场
export function addPark(data) {
  return request({
    url: '/system/park/addPark',
    method: 'post',
    data: data
  })
}

// 修改车场
export function updatePark(data) {
  return request({
    url: '/system/park/updatePark',
    method: 'put',
    data: data
  })
}

// 删除车场
export function delPark(id) {
  return request({
    url: '/system/park/' + id,
    method: 'delete'
  })
}
// 查询区域的车场
export function getParkByArea() {
  return request({
    url: '/system/area/list',
    method: 'get'
  })
}
// 查找用户
export function findUserByPhone(phone) {
  return request({
    url: '/system/user/findUserByPhone/' + phone,
    method: 'get'
  })
}
// 查询车场的人员
export function getParkStaff(parkId) {
  return request({
    url: '/system/park/findTag/' + parkId,
    method: 'get'
  })
}
// 查询车场的人员的绑定信息
export function getParkStaffBind(query) {
  return request({
    url: '/system/park/findUserTagByPhone',
    method: 'get',
    params: query
  })
}
// 绑定车场人员
export function bindStaff(data) {
  return request({
    url: '/system/park/addUserTagByPark',
    method: 'post',
    data: data
  })
}
// 修改车场人员
export function updateStaff(data) {
  return request({
    url: '/system/park/updateUserByPark',
    method: 'put',
    data: data
  })
}
// 删除车场人员
export function delStaff(query) {
  return request({
    url: '/system/park/deleteUserTag' ,
    method: 'delete',
    params: query
  })
}
// 查询车场的管理人员
export function getAdminStaff(parkId) {
  return request({
    url: '/system/park/findUserTag/' + parkId,
    method: 'get'
  })
}
// 查询车场的管理人员的绑定信息
export function getAdminStaffBind(query) {
  return request({
    url: '/system/park/findUserManageByPhone',
    method: 'get',
    params: query
  })
}
// 新增车场管理人员
export function addAdminStaff(data) {
  return request({
    url: '/system/park/addManage',
    method: 'post',
    data: data
  })
}
// 修改车场管理人员
export function updateAdminStaff(data) {
  return request({
    url: '/system/park/updateAdminUserByPark',
    method: 'put',
    data: data
  })
}
// 删除车场管理人员
export function delAdminStaff(query) {
  return request({
    url: '/system/park/deleteManage',
    method: 'delete',
    params: query
  })
}
// 查询进出记录
export function findCarRecord(query){
  return request({
    url: '/system/park/findCarRecord',
    method: 'get',
    params: query
  })
}
// 查询设备状态
export function findDevice(query){
  return request({
    url: '/system/park/findDevice/'+query,
    method: 'get',
    
  })
}
// 查询车场管理员列表
export function listParkAdmin(query) {
  return request({
    url: '/system/user/getUsers',
    method: 'get',
    params: query
  })
}
// 删除车场管理员
export function delParkAdmin(query) {
  return request({
    url: '/system/park/deleteManage' ,
    method: 'delete',
    params: query
  })
}