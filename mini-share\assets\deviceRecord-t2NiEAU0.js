import{B as w,d as k,u as x,r as e,C as B,F as j,e as l,G as q,c as z,H as v,j as t,s as g,i,h as r,o as c,v as h,I as D}from"./index-DDqcBwar.js";import{j as P}from"./park-DZi9DkwV.js";const I={class:"app-container"},L=w({name:"Notice"}),H=Object.assign(L,{setup(R){const{proxy:U}=k(),y=x(),m=e([]);e(!1);const u=e(!0);e(!0),e([]),e(!0),e(!0);const p=e(0);e("");const S=B({form:{},queryParams:{pageNum:1,pageSize:10,tagName:void 0},rules:{}}),{queryParams:o,form:V,rules:E}=j(S);function _(){u.value=!0,P(y.query.parkNo).then(n=>{m.value=n.data,p.value=n.data.length,u.value=!1})}return _(),(n,s)=>{const d=l("el-table-column"),f=l("el-tag"),b=l("el-table"),C=l("pagination"),N=q("loading");return c(),z("div",I,[v((c(),g(b,{data:t(m),onSelectionChange:n.handleSelectionChange},{default:i(()=>[r(d,{label:"序号",align:"center",type:"index",width:"100"}),r(d,{label:"设备名称",align:"center",prop:"address","show-overflow-tooltip":!0}),r(d,{label:"状态",align:"center",prop:"deviceStatus"},{default:i(a=>[a.row.deviceStatus?(c(),g(f,{key:0,type:"success"},{default:i(()=>[h("正常")]),_:1})):(c(),g(f,{key:1,type:"danger"},{default:i(()=>[h("异常")]),_:1}))]),_:1})]),_:1},8,["data","onSelectionChange"])),[[N,t(u)]]),v(r(C,{total:t(p),page:t(o).pageNum,"onUpdate:page":s[0]||(s[0]=a=>t(o).pageNum=a),limit:t(o).pageSize,"onUpdate:limit":s[1]||(s[1]=a=>t(o).pageSize=a),onPagination:_},null,8,["total","page","limit"]),[[D,t(p)>0]])])}}});export{H as default};
