import{B as Z,d as ee,r,C as te,F as ae,e as s,G as q,c as le,o as v,H as g,h as e,I as D,j as l,i as a,k as oe,v as f,s as w,D as I,f as ne}from"./index-DDqcBwar.js";import{l as se,a as ie,d as ue,u as re,b as de}from"./tag-DOE35K0D.js";const ce={class:"app-container"},me={class:"dialog-footer"},pe=Z({name:"Notice"}),he=Object.assign(pe,{setup(fe){const{proxy:d}=ee(),{sys_notice_status:ge,sys_notice_type:_e}=d.useDict("sys_notice_status","sys_notice_type"),R=r([]),c=r(!1),C=r(!0),y=r(!0),N=r([]),P=r(!0),F=r(!0),k=r(0),V=r(""),z=te({form:{},queryParams:{pageNum:1,pageSize:10,tagName:void 0},rules:{tagName:[{required:!0,message:"标签类型不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序不能为空",trigger:"blur"}]}}),{queryParams:u,form:i,rules:Q}=ae(z);function m(){C.value=!0,se(u.value).then(n=>{R.value=n.rows,k.value=n.total,C.value=!1})}function j(){c.value=!1,S()}function S(){i.value={noticeId:void 0,noticeTitle:void 0,noticeType:void 0,noticeContent:void 0,status:"0"},d.resetForm("tagRef")}function x(){u.value.pageNum=1,m()}function E(){d.resetForm("queryRef"),x()}function K(n){N.value=n.map(t=>t.noticeId),P.value=n.length!=1,F.value=!n.length}function L(){S(),c.value=!0,V.value="添加标签"}function A(n){S();const t=n.id||N.value;ie(t).then(_=>{i.value=_.data,c.value=!0,V.value="修改标签"})}function G(){d.$refs.tagRef.validate(n=>{n&&(i.value.id!=null?re(i.value).then(t=>{d.$modal.msgSuccess("修改成功"),c.value=!1,m()}):de(i.value).then(t=>{d.$modal.msgSuccess("新增成功"),c.value=!1,m()}))})}function H(n){const t=n.id||N.value;d.$modal.confirm('是否确认删除标签编号为"'+t+'"的数据项？').then(function(){return ue(t)}).then(()=>{m(),d.$modal.msgSuccess("删除成功")}).catch(()=>{})}return m(),(n,t)=>{const _=s("el-input"),h=s("el-form-item"),p=s("el-button"),U=s("el-form"),B=s("el-col"),O=s("right-toolbar"),$=s("el-row"),b=s("el-table-column"),J=s("el-table"),M=s("pagination"),W=s("el-dialog"),T=q("hasPermi"),X=q("loading");return v(),le("div",ce,[g(e(U,{model:l(u),ref:"queryRef",inline:!0},{default:a(()=>[e(h,{label:"标签名称",prop:"tagName"},{default:a(()=>[e(_,{modelValue:l(u).tagName,"onUpdate:modelValue":t[0]||(t[0]=o=>l(u).tagName=o),placeholder:"请输入标签名称",clearable:"",style:{width:"200px"},onKeyup:oe(x,["enter"])},null,8,["modelValue"])]),_:1}),e(h,null,{default:a(()=>[e(p,{type:"primary",icon:"Search",onClick:x},{default:a(()=>[f("搜索")]),_:1}),e(p,{icon:"Refresh",onClick:E},{default:a(()=>[f("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[D,l(y)]]),e($,{gutter:10,class:"mb8"},{default:a(()=>[e(B,{span:1.5},{default:a(()=>[g((v(),w(p,{type:"primary",plain:"",icon:"Plus",onClick:L},{default:a(()=>[f("新增")]),_:1})),[[T,["tag:tag:add"]]])]),_:1}),e(O,{showSearch:l(y),"onUpdate:showSearch":t[1]||(t[1]=o=>I(y)?y.value=o:null),onQueryTable:m},null,8,["showSearch"])]),_:1}),g((v(),w(J,{data:l(R),onSelectionChange:K},{default:a(()=>[e(b,{label:"序号",align:"center",type:"index",width:"100"}),e(b,{label:"标签名称",align:"center",prop:"tagName","show-overflow-tooltip":!0}),e(b,{label:"排序",align:"center",prop:"orderBy"}),e(b,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:a(o=>[g((v(),w(p,{link:"",type:"primary",icon:"Edit",onClick:Y=>A(o.row)},{default:a(()=>[f("修改")]),_:2},1032,["onClick"])),[[T,["tag:tag:edit"]]]),g((v(),w(p,{link:"",type:"primary",icon:"Delete",onClick:Y=>H(o.row)},{default:a(()=>[f("删除")]),_:2},1032,["onClick"])),[[T,["tag:tag:remove"]]])]),_:1})]),_:1},8,["data"])),[[X,l(C)]]),g(e(M,{total:l(k),page:l(u).pageNum,"onUpdate:page":t[2]||(t[2]=o=>l(u).pageNum=o),limit:l(u).pageSize,"onUpdate:limit":t[3]||(t[3]=o=>l(u).pageSize=o),onPagination:m},null,8,["total","page","limit"]),[[D,l(k)>0]]),e(W,{title:l(V),modelValue:l(c),"onUpdate:modelValue":t[6]||(t[6]=o=>I(c)?c.value=o:null),width:"580px","append-to-body":""},{footer:a(()=>[ne("div",me,[e(p,{type:"primary",onClick:G},{default:a(()=>[f("确 定")]),_:1}),e(p,{onClick:j},{default:a(()=>[f("取 消")]),_:1})])]),default:a(()=>[e(U,{ref:"tagRef",model:l(i),rules:l(Q),"label-width":"80px"},{default:a(()=>[e($,null,{default:a(()=>[e(B,{span:24},{default:a(()=>[e(h,{label:"标签标题",prop:"tagName"},{default:a(()=>[e(_,{modelValue:l(i).tagName,"onUpdate:modelValue":t[4]||(t[4]=o=>l(i).tagName=o),placeholder:"请输入标签标题"},null,8,["modelValue"])]),_:1})]),_:1}),e(B,{span:24},{default:a(()=>[e(h,{label:"排序",prop:"orderBy"},{default:a(()=>[e(_,{modelValue:l(i).orderBy,"onUpdate:modelValue":t[5]||(t[5]=o=>l(i).orderBy=o),placeholder:"请输入排序",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{he as default};
