import request from '@/utils/request'
// 查询订单列表
export function listOrder(query) {
  return request({
    url: '/device/order/getOrders',
    method: 'get',
    params: query
  })
}
//查询车位锁的订单
export function listLockOrder(query) {
  return request({
    url: '/device/order/getOrdersByLotId',
    method: 'get',
    params: query
  })
}

// 查询订单详情
export function getOrder(orderId) {
  return request({
    url: '/device/order/getOrders/' + orderId,
    method: 'get',

  })
}
// 查询当前下单用户的优惠价格详情
export function getOrderPrice(query) {
  return request({
    url: '/device/order/getDiscount',
    method: 'get',
    params: query

  })
}
// 获取用户订单中需要退款的信息
export function getRefundInfo(query) {
  return request({
    url: '/device/order/getManualRefund',
    method: 'get',
    params: query
  })
}
// 通过billId查询车位信息以及收费标准
export function getBillInfo(lotShareRuleId) {
  return request({
    url: '/device/order/getOrderDetailByBill/' + lotShareRuleId,
    method: 'get',
  })
}
//查询当前下单用户的违约价格详情
export function getBreachPrice(query) {
  return request({
    url: '/device/order/getViolation',
    method: 'get',
    params: query
  })
}