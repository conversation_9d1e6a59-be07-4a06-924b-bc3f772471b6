import{g as he,c1 as Oe,r as G,d as $e,w as jt,Z as Te,c2 as ke,e as Z,c as nt,o as tt,f as U,h as W,v as Q,j as z,i as X,A as mt,H as Re,s as Se,I as Ae,D as Ne,L as Bt,c3 as Ce}from"./index-DDqcBwar.js";import{t as Le,d as at,b as Me}from"./index-BMsBRakg.js";import{t as ht,f as Pe,i as De,s as Ie,l as je,R as Be}from"./RightPanel-BRZg-Qqm.js";import ze from"./DraggableItem-8WT6oSsM.js";import Ke from"./CodeTypeDialog-DP74hQu0.js";import"./IconsDialog-DN5_beFN.js";import"./TreeNodeDialog-Bm8Kovgo.js";var fe={exports:{}};/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */(function(t,o){(function(e,n){t.exports=n()})(Oe,function(){return function(){var a={686:function(_,l,s){s.d(l,{default:function(){return H}});var u=s(279),w=s.n(u),m=s(370),d=s.n(m),b=s(817),A=s.n(b);function T(S){try{return document.execCommand(S)}catch{return!1}}var M=function(k){var R=A()(k);return T("cut"),R},p=M;function r(S){var k=document.documentElement.getAttribute("dir")==="rtl",R=document.createElement("textarea");R.style.fontSize="12pt",R.style.border="0",R.style.padding="0",R.style.margin="0",R.style.position="absolute",R.style[k?"right":"left"]="-9999px";var D=window.pageYOffset||document.documentElement.scrollTop;return R.style.top="".concat(D,"px"),R.setAttribute("readonly",""),R.value=S,R}var f=function(k,R){var D=r(k);R.container.appendChild(D);var I=A()(D);return T("copy"),D.remove(),I},C=function(k){var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body},D="";return typeof k=="string"?D=f(k,R):k instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(k==null?void 0:k.type)?D=f(k.value,R):(D=A()(k),T("copy")),D},x=C;function L(S){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?L=function(R){return typeof R}:L=function(R){return R&&typeof Symbol=="function"&&R.constructor===Symbol&&R!==Symbol.prototype?"symbol":typeof R},L(S)}var c=function(){var k=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},R=k.action,D=R===void 0?"copy":R,I=k.container,B=k.target,V=k.text;if(D!=="copy"&&D!=="cut")throw new Error('Invalid "action" value, use either "copy" or "cut"');if(B!==void 0)if(B&&L(B)==="object"&&B.nodeType===1){if(D==="copy"&&B.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if(D==="cut"&&(B.hasAttribute("readonly")||B.hasAttribute("disabled")))throw new Error(`Invalid "target" attribute. You can't cut text from elements with "readonly" or "disabled" attributes`)}else throw new Error('Invalid "target" value, use a valid Element');if(V)return x(V,{container:I});if(B)return D==="cut"?p(B):x(B,{container:I})},y=c;function h(S){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?h=function(R){return typeof R}:h=function(R){return R&&typeof Symbol=="function"&&R.constructor===Symbol&&R!==Symbol.prototype?"symbol":typeof R},h(S)}function E(S,k){if(!(S instanceof k))throw new TypeError("Cannot call a class as a function")}function i(S,k){for(var R=0;R<k.length;R++){var D=k[R];D.enumerable=D.enumerable||!1,D.configurable=!0,"value"in D&&(D.writable=!0),Object.defineProperty(S,D.key,D)}}function g(S,k,R){return k&&i(S.prototype,k),R&&i(S,R),S}function v(S,k){if(typeof k!="function"&&k!==null)throw new TypeError("Super expression must either be null or a function");S.prototype=Object.create(k&&k.prototype,{constructor:{value:S,writable:!0,configurable:!0}}),k&&N(S,k)}function N(S,k){return N=Object.setPrototypeOf||function(D,I){return D.__proto__=I,D},N(S,k)}function P(S){var k=j();return function(){var D=K(S),I;if(k){var B=K(this).constructor;I=Reflect.construct(D,arguments,B)}else I=D.apply(this,arguments);return O(this,I)}}function O(S,k){return k&&(h(k)==="object"||typeof k=="function")?k:$(S)}function $(S){if(S===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return S}function j(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function K(S){return K=Object.setPrototypeOf?Object.getPrototypeOf:function(R){return R.__proto__||Object.getPrototypeOf(R)},K(S)}function F(S,k){var R="data-clipboard-".concat(S);if(k.hasAttribute(R))return k.getAttribute(R)}var J=function(S){v(R,S);var k=P(R);function R(D,I){var B;return E(this,R),B=k.call(this),B.resolveOptions(I),B.listenClick(D),B}return g(R,[{key:"resolveOptions",value:function(){var I=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.action=typeof I.action=="function"?I.action:this.defaultAction,this.target=typeof I.target=="function"?I.target:this.defaultTarget,this.text=typeof I.text=="function"?I.text:this.defaultText,this.container=h(I.container)==="object"?I.container:document.body}},{key:"listenClick",value:function(I){var B=this;this.listener=d()(I,"click",function(V){return B.onClick(V)})}},{key:"onClick",value:function(I){var B=I.delegateTarget||I.currentTarget,V=this.action(B)||"copy",st=y({action:V,container:this.container,target:this.target(B),text:this.text(B)});this.emit(st?"success":"error",{action:V,text:st,trigger:B,clearSelection:function(){B&&B.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(I){return F("action",I)}},{key:"defaultTarget",value:function(I){var B=F("target",I);if(B)return document.querySelector(B)}},{key:"defaultText",value:function(I){return F("text",I)}},{key:"destroy",value:function(){this.listener.destroy()}}],[{key:"copy",value:function(I){var B=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body};return x(I,B)}},{key:"cut",value:function(I){return p(I)}},{key:"isSupported",value:function(){var I=arguments.length>0&&arguments[0]!==void 0?arguments[0]:["copy","cut"],B=typeof I=="string"?[I]:I,V=!!document.queryCommandSupported;return B.forEach(function(st){V=V&&!!document.queryCommandSupported(st)}),V}}]),R}(w()),H=J},828:function(_){var l=9;if(typeof Element<"u"&&!Element.prototype.matches){var s=Element.prototype;s.matches=s.matchesSelector||s.mozMatchesSelector||s.msMatchesSelector||s.oMatchesSelector||s.webkitMatchesSelector}function u(w,m){for(;w&&w.nodeType!==l;){if(typeof w.matches=="function"&&w.matches(m))return w;w=w.parentNode}}_.exports=u},438:function(_,l,s){var u=s(828);function w(b,A,T,M,p){var r=d.apply(this,arguments);return b.addEventListener(T,r,p),{destroy:function(){b.removeEventListener(T,r,p)}}}function m(b,A,T,M,p){return typeof b.addEventListener=="function"?w.apply(null,arguments):typeof T=="function"?w.bind(null,document).apply(null,arguments):(typeof b=="string"&&(b=document.querySelectorAll(b)),Array.prototype.map.call(b,function(r){return w(r,A,T,M,p)}))}function d(b,A,T,M){return function(p){p.delegateTarget=u(p.target,A),p.delegateTarget&&M.call(b,p)}}_.exports=m},879:function(_,l){l.node=function(s){return s!==void 0&&s instanceof HTMLElement&&s.nodeType===1},l.nodeList=function(s){var u=Object.prototype.toString.call(s);return s!==void 0&&(u==="[object NodeList]"||u==="[object HTMLCollection]")&&"length"in s&&(s.length===0||l.node(s[0]))},l.string=function(s){return typeof s=="string"||s instanceof String},l.fn=function(s){var u=Object.prototype.toString.call(s);return u==="[object Function]"}},370:function(_,l,s){var u=s(879),w=s(438);function m(T,M,p){if(!T&&!M&&!p)throw new Error("Missing required arguments");if(!u.string(M))throw new TypeError("Second argument must be a String");if(!u.fn(p))throw new TypeError("Third argument must be a Function");if(u.node(T))return d(T,M,p);if(u.nodeList(T))return b(T,M,p);if(u.string(T))return A(T,M,p);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function d(T,M,p){return T.addEventListener(M,p),{destroy:function(){T.removeEventListener(M,p)}}}function b(T,M,p){return Array.prototype.forEach.call(T,function(r){r.addEventListener(M,p)}),{destroy:function(){Array.prototype.forEach.call(T,function(r){r.removeEventListener(M,p)})}}}function A(T,M,p){return w(document.body,T,M,p)}_.exports=m},817:function(_){function l(s){var u;if(s.nodeName==="SELECT")s.focus(),u=s.value;else if(s.nodeName==="INPUT"||s.nodeName==="TEXTAREA"){var w=s.hasAttribute("readonly");w||s.setAttribute("readonly",""),s.select(),s.setSelectionRange(0,s.value.length),w||s.removeAttribute("readonly"),u=s.value}else{s.hasAttribute("contenteditable")&&s.focus();var m=window.getSelection(),d=document.createRange();d.selectNodeContents(s),m.removeAllRanges(),m.addRange(d),u=m.toString()}return u}_.exports=l},279:function(_){function l(){}l.prototype={on:function(s,u,w){var m=this.e||(this.e={});return(m[s]||(m[s]=[])).push({fn:u,ctx:w}),this},once:function(s,u,w){var m=this;function d(){m.off(s,d),u.apply(w,arguments)}return d._=u,this.on(s,d,w)},emit:function(s){var u=[].slice.call(arguments,1),w=((this.e||(this.e={}))[s]||[]).slice(),m=0,d=w.length;for(m;m<d;m++)w[m].fn.apply(w[m].ctx,u);return this},off:function(s,u){var w=this.e||(this.e={}),m=w[s],d=[];if(m&&u)for(var b=0,A=m.length;b<A;b++)m[b].fn!==u&&m[b].fn._!==u&&d.push(m[b]);return d.length?w[s]=d:delete w[s],this}},_.exports=l,_.exports.TinyEmitter=l}},e={};function n(_){if(e[_])return e[_].exports;var l=e[_]={exports:{}};return a[_](l,l.exports,n),l.exports}return function(){n.n=function(_){var l=_&&_.__esModule?function(){return _.default}:function(){return _};return n.d(l,{a:l}),l}}(),function(){n.d=function(_,l){for(var s in l)n.o(l,s)&&!n.o(_,s)&&Object.defineProperty(_,s,{enumerable:!0,get:l[s]})}}(),function(){n.o=function(_,l){return Object.prototype.hasOwnProperty.call(_,l)}}(),n(686)}().default})})(fe);var We=fe.exports;const Ue=he(We);var ce={exports:{}},et={},rt={exports:{}},bt={},vt={},zt;function Mt(){if(zt)return vt;zt=1;function t(e){this.__parent=e,this.__character_count=0,this.__indent_count=-1,this.__alignment_count=0,this.__wrap_point_index=0,this.__wrap_point_character_count=0,this.__wrap_point_indent_count=-1,this.__wrap_point_alignment_count=0,this.__items=[]}t.prototype.clone_empty=function(){var e=new t(this.__parent);return e.set_indent(this.__indent_count,this.__alignment_count),e},t.prototype.item=function(e){return e<0?this.__items[this.__items.length+e]:this.__items[e]},t.prototype.has_match=function(e){for(var n=this.__items.length-1;n>=0;n--)if(this.__items[n].match(e))return!0;return!1},t.prototype.set_indent=function(e,n){this.is_empty()&&(this.__indent_count=e||0,this.__alignment_count=n||0,this.__character_count=this.__parent.get_indent_size(this.__indent_count,this.__alignment_count))},t.prototype._set_wrap_point=function(){this.__parent.wrap_line_length&&(this.__wrap_point_index=this.__items.length,this.__wrap_point_character_count=this.__character_count,this.__wrap_point_indent_count=this.__parent.next_line.__indent_count,this.__wrap_point_alignment_count=this.__parent.next_line.__alignment_count)},t.prototype._should_wrap=function(){return this.__wrap_point_index&&this.__character_count>this.__parent.wrap_line_length&&this.__wrap_point_character_count>this.__parent.next_line.__character_count},t.prototype._allow_wrap=function(){if(this._should_wrap()){this.__parent.add_new_line();var e=this.__parent.current_line;return e.set_indent(this.__wrap_point_indent_count,this.__wrap_point_alignment_count),e.__items=this.__items.slice(this.__wrap_point_index),this.__items=this.__items.slice(0,this.__wrap_point_index),e.__character_count+=this.__character_count-this.__wrap_point_character_count,this.__character_count=this.__wrap_point_character_count,e.__items[0]===" "&&(e.__items.splice(0,1),e.__character_count-=1),!0}return!1},t.prototype.is_empty=function(){return this.__items.length===0},t.prototype.last=function(){return this.is_empty()?null:this.__items[this.__items.length-1]},t.prototype.push=function(e){this.__items.push(e);var n=e.lastIndexOf(`
`);n!==-1?this.__character_count=e.length-n:this.__character_count+=e.length},t.prototype.pop=function(){var e=null;return this.is_empty()||(e=this.__items.pop(),this.__character_count-=e.length),e},t.prototype._remove_indent=function(){this.__indent_count>0&&(this.__indent_count-=1,this.__character_count-=this.__parent.indent_size)},t.prototype._remove_wrap_indent=function(){this.__wrap_point_indent_count>0&&(this.__wrap_point_indent_count-=1)},t.prototype.trim=function(){for(;this.last()===" ";)this.__items.pop(),this.__character_count-=1},t.prototype.toString=function(){var e="";return this.is_empty()?this.__parent.indent_empty_lines&&(e=this.__parent.get_indent_string(this.__indent_count)):(e=this.__parent.get_indent_string(this.__indent_count,this.__alignment_count),e+=this.__items.join("")),e};function o(e,n){this.__cache=[""],this.__indent_size=e.indent_size,this.__indent_string=e.indent_char,e.indent_with_tabs||(this.__indent_string=new Array(e.indent_size+1).join(e.indent_char)),n=n||"",e.indent_level>0&&(n=new Array(e.indent_level+1).join(this.__indent_string)),this.__base_string=n,this.__base_string_length=n.length}o.prototype.get_indent_size=function(e,n){var _=this.__base_string_length;return n=n||0,e<0&&(_=0),_+=e*this.__indent_size,_+=n,_},o.prototype.get_indent_string=function(e,n){var _=this.__base_string;return n=n||0,e<0&&(e=0,_=""),n+=e*this.__indent_size,this.__ensure_cache(n),_+=this.__cache[n],_},o.prototype.__ensure_cache=function(e){for(;e>=this.__cache.length;)this.__add_column()},o.prototype.__add_column=function(){var e=this.__cache.length,n=0,_="";this.__indent_size&&e>=this.__indent_size&&(n=Math.floor(e/this.__indent_size),e-=n*this.__indent_size,_=new Array(n+1).join(this.__indent_string)),e&&(_+=new Array(e+1).join(" ")),this.__cache.push(_)};function a(e,n){this.__indent_cache=new o(e,n),this.raw=!1,this._end_with_newline=e.end_with_newline,this.indent_size=e.indent_size,this.wrap_line_length=e.wrap_line_length,this.indent_empty_lines=e.indent_empty_lines,this.__lines=[],this.previous_line=null,this.current_line=null,this.next_line=new t(this),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=!1,this.__add_outputline()}return a.prototype.__add_outputline=function(){this.previous_line=this.current_line,this.current_line=this.next_line.clone_empty(),this.__lines.push(this.current_line)},a.prototype.get_line_number=function(){return this.__lines.length},a.prototype.get_indent_string=function(e,n){return this.__indent_cache.get_indent_string(e,n)},a.prototype.get_indent_size=function(e,n){return this.__indent_cache.get_indent_size(e,n)},a.prototype.is_empty=function(){return!this.previous_line&&this.current_line.is_empty()},a.prototype.add_new_line=function(e){return this.is_empty()||!e&&this.just_added_newline()?!1:(this.raw||this.__add_outputline(),!0)},a.prototype.get_code=function(e){this.trim(!0);var n=this.current_line.pop();n&&(n[n.length-1]===`
`&&(n=n.replace(/\n+$/g,"")),this.current_line.push(n)),this._end_with_newline&&this.__add_outputline();var _=this.__lines.join(`
`);return e!==`
`&&(_=_.replace(/[\n]/g,e)),_},a.prototype.set_wrap_point=function(){this.current_line._set_wrap_point()},a.prototype.set_indent=function(e,n){return e=e||0,n=n||0,this.next_line.set_indent(e,n),this.__lines.length>1?(this.current_line.set_indent(e,n),!0):(this.current_line.set_indent(),!1)},a.prototype.add_raw_token=function(e){for(var n=0;n<e.newlines;n++)this.__add_outputline();this.current_line.set_indent(-1),this.current_line.push(e.whitespace_before),this.current_line.push(e.text),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=!1},a.prototype.add_token=function(e){this.__add_space_before_token(),this.current_line.push(e),this.space_before_token=!1,this.non_breaking_space=!1,this.previous_token_wrapped=this.current_line._allow_wrap()},a.prototype.__add_space_before_token=function(){this.space_before_token&&!this.just_added_newline()&&(this.non_breaking_space||this.set_wrap_point(),this.current_line.push(" "))},a.prototype.remove_indent=function(e){for(var n=this.__lines.length;e<n;)this.__lines[e]._remove_indent(),e++;this.current_line._remove_wrap_indent()},a.prototype.trim=function(e){for(e=e===void 0?!1:e,this.current_line.trim();e&&this.__lines.length>1&&this.current_line.is_empty();)this.__lines.pop(),this.current_line=this.__lines[this.__lines.length-1],this.current_line.trim();this.previous_line=this.__lines.length>1?this.__lines[this.__lines.length-2]:null},a.prototype.just_added_newline=function(){return this.current_line.is_empty()},a.prototype.just_added_blankline=function(){return this.is_empty()||this.current_line.is_empty()&&this.previous_line.is_empty()},a.prototype.ensure_empty_line_above=function(e,n){for(var _=this.__lines.length-2;_>=0;){var l=this.__lines[_];if(l.is_empty())break;if(l.item(0).indexOf(e)!==0&&l.item(-1)!==n){this.__lines.splice(_+1,0,new t(this)),this.previous_line=this.__lines[this.__lines.length-2];break}_--}},vt.Output=a,vt}var yt={},Kt;function de(){if(Kt)return yt;Kt=1;function t(o,a,e,n){this.type=o,this.text=a,this.comments_before=null,this.newlines=e||0,this.whitespace_before=n||"",this.parent=null,this.next=null,this.previous=null,this.opened=null,this.closed=null,this.directives=null}return yt.Token=t,yt}var wt={},Wt;function ge(){return Wt||(Wt=1,function(t){var o="\\x23\\x24\\x40\\x41-\\x5a\\x5f\\x61-\\x7a",a="\\x24\\x30-\\x39\\x41-\\x5a\\x5f\\x61-\\x7a",e="\\xaa\\xb5\\xba\\xc0-\\xd6\\xd8-\\xf6\\xf8-\\u02c1\\u02c6-\\u02d1\\u02e0-\\u02e4\\u02ec\\u02ee\\u0370-\\u0374\\u0376\\u0377\\u037a-\\u037d\\u0386\\u0388-\\u038a\\u038c\\u038e-\\u03a1\\u03a3-\\u03f5\\u03f7-\\u0481\\u048a-\\u0527\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05d0-\\u05ea\\u05f0-\\u05f2\\u0620-\\u064a\\u066e\\u066f\\u0671-\\u06d3\\u06d5\\u06e5\\u06e6\\u06ee\\u06ef\\u06fa-\\u06fc\\u06ff\\u0710\\u0712-\\u072f\\u074d-\\u07a5\\u07b1\\u07ca-\\u07ea\\u07f4\\u07f5\\u07fa\\u0800-\\u0815\\u081a\\u0824\\u0828\\u0840-\\u0858\\u08a0\\u08a2-\\u08ac\\u0904-\\u0939\\u093d\\u0950\\u0958-\\u0961\\u0971-\\u0977\\u0979-\\u097f\\u0985-\\u098c\\u098f\\u0990\\u0993-\\u09a8\\u09aa-\\u09b0\\u09b2\\u09b6-\\u09b9\\u09bd\\u09ce\\u09dc\\u09dd\\u09df-\\u09e1\\u09f0\\u09f1\\u0a05-\\u0a0a\\u0a0f\\u0a10\\u0a13-\\u0a28\\u0a2a-\\u0a30\\u0a32\\u0a33\\u0a35\\u0a36\\u0a38\\u0a39\\u0a59-\\u0a5c\\u0a5e\\u0a72-\\u0a74\\u0a85-\\u0a8d\\u0a8f-\\u0a91\\u0a93-\\u0aa8\\u0aaa-\\u0ab0\\u0ab2\\u0ab3\\u0ab5-\\u0ab9\\u0abd\\u0ad0\\u0ae0\\u0ae1\\u0b05-\\u0b0c\\u0b0f\\u0b10\\u0b13-\\u0b28\\u0b2a-\\u0b30\\u0b32\\u0b33\\u0b35-\\u0b39\\u0b3d\\u0b5c\\u0b5d\\u0b5f-\\u0b61\\u0b71\\u0b83\\u0b85-\\u0b8a\\u0b8e-\\u0b90\\u0b92-\\u0b95\\u0b99\\u0b9a\\u0b9c\\u0b9e\\u0b9f\\u0ba3\\u0ba4\\u0ba8-\\u0baa\\u0bae-\\u0bb9\\u0bd0\\u0c05-\\u0c0c\\u0c0e-\\u0c10\\u0c12-\\u0c28\\u0c2a-\\u0c33\\u0c35-\\u0c39\\u0c3d\\u0c58\\u0c59\\u0c60\\u0c61\\u0c85-\\u0c8c\\u0c8e-\\u0c90\\u0c92-\\u0ca8\\u0caa-\\u0cb3\\u0cb5-\\u0cb9\\u0cbd\\u0cde\\u0ce0\\u0ce1\\u0cf1\\u0cf2\\u0d05-\\u0d0c\\u0d0e-\\u0d10\\u0d12-\\u0d3a\\u0d3d\\u0d4e\\u0d60\\u0d61\\u0d7a-\\u0d7f\\u0d85-\\u0d96\\u0d9a-\\u0db1\\u0db3-\\u0dbb\\u0dbd\\u0dc0-\\u0dc6\\u0e01-\\u0e30\\u0e32\\u0e33\\u0e40-\\u0e46\\u0e81\\u0e82\\u0e84\\u0e87\\u0e88\\u0e8a\\u0e8d\\u0e94-\\u0e97\\u0e99-\\u0e9f\\u0ea1-\\u0ea3\\u0ea5\\u0ea7\\u0eaa\\u0eab\\u0ead-\\u0eb0\\u0eb2\\u0eb3\\u0ebd\\u0ec0-\\u0ec4\\u0ec6\\u0edc-\\u0edf\\u0f00\\u0f40-\\u0f47\\u0f49-\\u0f6c\\u0f88-\\u0f8c\\u1000-\\u102a\\u103f\\u1050-\\u1055\\u105a-\\u105d\\u1061\\u1065\\u1066\\u106e-\\u1070\\u1075-\\u1081\\u108e\\u10a0-\\u10c5\\u10c7\\u10cd\\u10d0-\\u10fa\\u10fc-\\u1248\\u124a-\\u124d\\u1250-\\u1256\\u1258\\u125a-\\u125d\\u1260-\\u1288\\u128a-\\u128d\\u1290-\\u12b0\\u12b2-\\u12b5\\u12b8-\\u12be\\u12c0\\u12c2-\\u12c5\\u12c8-\\u12d6\\u12d8-\\u1310\\u1312-\\u1315\\u1318-\\u135a\\u1380-\\u138f\\u13a0-\\u13f4\\u1401-\\u166c\\u166f-\\u167f\\u1681-\\u169a\\u16a0-\\u16ea\\u16ee-\\u16f0\\u1700-\\u170c\\u170e-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176c\\u176e-\\u1770\\u1780-\\u17b3\\u17d7\\u17dc\\u1820-\\u1877\\u1880-\\u18a8\\u18aa\\u18b0-\\u18f5\\u1900-\\u191c\\u1950-\\u196d\\u1970-\\u1974\\u1980-\\u19ab\\u19c1-\\u19c7\\u1a00-\\u1a16\\u1a20-\\u1a54\\u1aa7\\u1b05-\\u1b33\\u1b45-\\u1b4b\\u1b83-\\u1ba0\\u1bae\\u1baf\\u1bba-\\u1be5\\u1c00-\\u1c23\\u1c4d-\\u1c4f\\u1c5a-\\u1c7d\\u1ce9-\\u1cec\\u1cee-\\u1cf1\\u1cf5\\u1cf6\\u1d00-\\u1dbf\\u1e00-\\u1f15\\u1f18-\\u1f1d\\u1f20-\\u1f45\\u1f48-\\u1f4d\\u1f50-\\u1f57\\u1f59\\u1f5b\\u1f5d\\u1f5f-\\u1f7d\\u1f80-\\u1fb4\\u1fb6-\\u1fbc\\u1fbe\\u1fc2-\\u1fc4\\u1fc6-\\u1fcc\\u1fd0-\\u1fd3\\u1fd6-\\u1fdb\\u1fe0-\\u1fec\\u1ff2-\\u1ff4\\u1ff6-\\u1ffc\\u2071\\u207f\\u2090-\\u209c\\u2102\\u2107\\u210a-\\u2113\\u2115\\u2119-\\u211d\\u2124\\u2126\\u2128\\u212a-\\u212d\\u212f-\\u2139\\u213c-\\u213f\\u2145-\\u2149\\u214e\\u2160-\\u2188\\u2c00-\\u2c2e\\u2c30-\\u2c5e\\u2c60-\\u2ce4\\u2ceb-\\u2cee\\u2cf2\\u2cf3\\u2d00-\\u2d25\\u2d27\\u2d2d\\u2d30-\\u2d67\\u2d6f\\u2d80-\\u2d96\\u2da0-\\u2da6\\u2da8-\\u2dae\\u2db0-\\u2db6\\u2db8-\\u2dbe\\u2dc0-\\u2dc6\\u2dc8-\\u2dce\\u2dd0-\\u2dd6\\u2dd8-\\u2dde\\u2e2f\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303c\\u3041-\\u3096\\u309d-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312d\\u3131-\\u318e\\u31a0-\\u31ba\\u31f0-\\u31ff\\u3400-\\u4db5\\u4e00-\\u9fcc\\ua000-\\ua48c\\ua4d0-\\ua4fd\\ua500-\\ua60c\\ua610-\\ua61f\\ua62a\\ua62b\\ua640-\\ua66e\\ua67f-\\ua697\\ua6a0-\\ua6ef\\ua717-\\ua71f\\ua722-\\ua788\\ua78b-\\ua78e\\ua790-\\ua793\\ua7a0-\\ua7aa\\ua7f8-\\ua801\\ua803-\\ua805\\ua807-\\ua80a\\ua80c-\\ua822\\ua840-\\ua873\\ua882-\\ua8b3\\ua8f2-\\ua8f7\\ua8fb\\ua90a-\\ua925\\ua930-\\ua946\\ua960-\\ua97c\\ua984-\\ua9b2\\ua9cf\\uaa00-\\uaa28\\uaa40-\\uaa42\\uaa44-\\uaa4b\\uaa60-\\uaa76\\uaa7a\\uaa80-\\uaaaf\\uaab1\\uaab5\\uaab6\\uaab9-\\uaabd\\uaac0\\uaac2\\uaadb-\\uaadd\\uaae0-\\uaaea\\uaaf2-\\uaaf4\\uab01-\\uab06\\uab09-\\uab0e\\uab11-\\uab16\\uab20-\\uab26\\uab28-\\uab2e\\uabc0-\\uabe2\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufb00-\\ufb06\\ufb13-\\ufb17\\ufb1d\\ufb1f-\\ufb28\\ufb2a-\\ufb36\\ufb38-\\ufb3c\\ufb3e\\ufb40\\ufb41\\ufb43\\ufb44\\ufb46-\\ufbb1\\ufbd3-\\ufd3d\\ufd50-\\ufd8f\\ufd92-\\ufdc7\\ufdf0-\\ufdfb\\ufe70-\\ufe74\\ufe76-\\ufefc\\uff21-\\uff3a\\uff41-\\uff5a\\uff66-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc",n="\\u0300-\\u036f\\u0483-\\u0487\\u0591-\\u05bd\\u05bf\\u05c1\\u05c2\\u05c4\\u05c5\\u05c7\\u0610-\\u061a\\u0620-\\u0649\\u0672-\\u06d3\\u06e7-\\u06e8\\u06fb-\\u06fc\\u0730-\\u074a\\u0800-\\u0814\\u081b-\\u0823\\u0825-\\u0827\\u0829-\\u082d\\u0840-\\u0857\\u08e4-\\u08fe\\u0900-\\u0903\\u093a-\\u093c\\u093e-\\u094f\\u0951-\\u0957\\u0962-\\u0963\\u0966-\\u096f\\u0981-\\u0983\\u09bc\\u09be-\\u09c4\\u09c7\\u09c8\\u09d7\\u09df-\\u09e0\\u0a01-\\u0a03\\u0a3c\\u0a3e-\\u0a42\\u0a47\\u0a48\\u0a4b-\\u0a4d\\u0a51\\u0a66-\\u0a71\\u0a75\\u0a81-\\u0a83\\u0abc\\u0abe-\\u0ac5\\u0ac7-\\u0ac9\\u0acb-\\u0acd\\u0ae2-\\u0ae3\\u0ae6-\\u0aef\\u0b01-\\u0b03\\u0b3c\\u0b3e-\\u0b44\\u0b47\\u0b48\\u0b4b-\\u0b4d\\u0b56\\u0b57\\u0b5f-\\u0b60\\u0b66-\\u0b6f\\u0b82\\u0bbe-\\u0bc2\\u0bc6-\\u0bc8\\u0bca-\\u0bcd\\u0bd7\\u0be6-\\u0bef\\u0c01-\\u0c03\\u0c46-\\u0c48\\u0c4a-\\u0c4d\\u0c55\\u0c56\\u0c62-\\u0c63\\u0c66-\\u0c6f\\u0c82\\u0c83\\u0cbc\\u0cbe-\\u0cc4\\u0cc6-\\u0cc8\\u0cca-\\u0ccd\\u0cd5\\u0cd6\\u0ce2-\\u0ce3\\u0ce6-\\u0cef\\u0d02\\u0d03\\u0d46-\\u0d48\\u0d57\\u0d62-\\u0d63\\u0d66-\\u0d6f\\u0d82\\u0d83\\u0dca\\u0dcf-\\u0dd4\\u0dd6\\u0dd8-\\u0ddf\\u0df2\\u0df3\\u0e34-\\u0e3a\\u0e40-\\u0e45\\u0e50-\\u0e59\\u0eb4-\\u0eb9\\u0ec8-\\u0ecd\\u0ed0-\\u0ed9\\u0f18\\u0f19\\u0f20-\\u0f29\\u0f35\\u0f37\\u0f39\\u0f41-\\u0f47\\u0f71-\\u0f84\\u0f86-\\u0f87\\u0f8d-\\u0f97\\u0f99-\\u0fbc\\u0fc6\\u1000-\\u1029\\u1040-\\u1049\\u1067-\\u106d\\u1071-\\u1074\\u1082-\\u108d\\u108f-\\u109d\\u135d-\\u135f\\u170e-\\u1710\\u1720-\\u1730\\u1740-\\u1750\\u1772\\u1773\\u1780-\\u17b2\\u17dd\\u17e0-\\u17e9\\u180b-\\u180d\\u1810-\\u1819\\u1920-\\u192b\\u1930-\\u193b\\u1951-\\u196d\\u19b0-\\u19c0\\u19c8-\\u19c9\\u19d0-\\u19d9\\u1a00-\\u1a15\\u1a20-\\u1a53\\u1a60-\\u1a7c\\u1a7f-\\u1a89\\u1a90-\\u1a99\\u1b46-\\u1b4b\\u1b50-\\u1b59\\u1b6b-\\u1b73\\u1bb0-\\u1bb9\\u1be6-\\u1bf3\\u1c00-\\u1c22\\u1c40-\\u1c49\\u1c5b-\\u1c7d\\u1cd0-\\u1cd2\\u1d00-\\u1dbe\\u1e01-\\u1f15\\u200c\\u200d\\u203f\\u2040\\u2054\\u20d0-\\u20dc\\u20e1\\u20e5-\\u20f0\\u2d81-\\u2d96\\u2de0-\\u2dff\\u3021-\\u3028\\u3099\\u309a\\ua640-\\ua66d\\ua674-\\ua67d\\ua69f\\ua6f0-\\ua6f1\\ua7f8-\\ua800\\ua806\\ua80b\\ua823-\\ua827\\ua880-\\ua881\\ua8b4-\\ua8c4\\ua8d0-\\ua8d9\\ua8f3-\\ua8f7\\ua900-\\ua909\\ua926-\\ua92d\\ua930-\\ua945\\ua980-\\ua983\\ua9b3-\\ua9c0\\uaa00-\\uaa27\\uaa40-\\uaa41\\uaa4c-\\uaa4d\\uaa50-\\uaa59\\uaa7b\\uaae0-\\uaae9\\uaaf2-\\uaaf3\\uabc0-\\uabe1\\uabec\\uabed\\uabf0-\\uabf9\\ufb20-\\ufb28\\ufe00-\\ufe0f\\ufe20-\\ufe26\\ufe33\\ufe34\\ufe4d-\\ufe4f\\uff10-\\uff19\\uff3f",_="\\\\u[0-9a-fA-F]{4}|\\\\u\\{[0-9a-fA-F]+\\}",l="(?:"+_+"|["+o+e+"])",s="(?:"+_+"|["+a+e+n+"])*";t.identifier=new RegExp(l+s,"g"),t.identifierStart=new RegExp(l),t.identifierMatch=new RegExp("(?:"+_+"|["+a+e+n+"])+"),t.newline=/[\n\r\u2028\u2029]/,t.lineBreak=new RegExp(`\r
|`+t.newline.source),t.allLineBreaks=new RegExp(t.lineBreak.source,"g")}(wt)),wt}var xt={},it={},Ut;function Pt(){if(Ut)return it;Ut=1;function t(e,n){this.raw_options=o(e,n),this.disabled=this._get_boolean("disabled"),this.eol=this._get_characters("eol","auto"),this.end_with_newline=this._get_boolean("end_with_newline"),this.indent_size=this._get_number("indent_size",4),this.indent_char=this._get_characters("indent_char"," "),this.indent_level=this._get_number("indent_level"),this.preserve_newlines=this._get_boolean("preserve_newlines",!0),this.max_preserve_newlines=this._get_number("max_preserve_newlines",32786),this.preserve_newlines||(this.max_preserve_newlines=0),this.indent_with_tabs=this._get_boolean("indent_with_tabs",this.indent_char==="	"),this.indent_with_tabs&&(this.indent_char="	",this.indent_size===1&&(this.indent_size=4)),this.wrap_line_length=this._get_number("wrap_line_length",this._get_number("max_char")),this.indent_empty_lines=this._get_boolean("indent_empty_lines"),this.templating=this._get_selection_list("templating",["auto","none","django","erb","handlebars","php","smarty"],["auto"])}t.prototype._get_array=function(e,n){var _=this.raw_options[e],l=n||[];return typeof _=="object"?_!==null&&typeof _.concat=="function"&&(l=_.concat()):typeof _=="string"&&(l=_.split(/[^a-zA-Z0-9_\/\-]+/)),l},t.prototype._get_boolean=function(e,n){var _=this.raw_options[e],l=_===void 0?!!n:!!_;return l},t.prototype._get_characters=function(e,n){var _=this.raw_options[e],l=n||"";return typeof _=="string"&&(l=_.replace(/\\r/,"\r").replace(/\\n/,`
`).replace(/\\t/,"	")),l},t.prototype._get_number=function(e,n){var _=this.raw_options[e];n=parseInt(n,10),isNaN(n)&&(n=0);var l=parseInt(_,10);return isNaN(l)&&(l=n),l},t.prototype._get_selection=function(e,n,_){var l=this._get_selection_list(e,n,_);if(l.length!==1)throw new Error("Invalid Option Value: The option '"+e+`' can only be one of the following values:
`+n+`
You passed in: '`+this.raw_options[e]+"'");return l[0]},t.prototype._get_selection_list=function(e,n,_){if(!n||n.length===0)throw new Error("Selection list cannot be empty.");if(_=_||[n[0]],!this._is_valid_selection(_,n))throw new Error("Invalid Default Value!");var l=this._get_array(e,_);if(!this._is_valid_selection(l,n))throw new Error("Invalid Option Value: The option '"+e+`' can contain only the following values:
`+n+`
You passed in: '`+this.raw_options[e]+"'");return l},t.prototype._is_valid_selection=function(e,n){return e.length&&n.length&&!e.some(function(_){return n.indexOf(_)===-1})};function o(e,n){var _={};e=a(e);var l;for(l in e)l!==n&&(_[l]=e[l]);if(n&&e[n])for(l in e[n])_[l]=e[n][l];return _}function a(e){var n={},_;for(_ in e){var l=_.replace(/-/g,"_");n[l]=e[_]}return n}return it.Options=t,it.normalizeOpts=a,it.mergeOpts=o,it}var qt;function me(){if(qt)return xt;qt=1;var t=Pt().Options,o=["before-newline","after-newline","preserve-newline"];function a(e){t.call(this,e,"js");var n=this.raw_options.brace_style||null;n==="expand-strict"?this.raw_options.brace_style="expand":n==="collapse-preserve-inline"?this.raw_options.brace_style="collapse,preserve-inline":this.raw_options.braces_on_own_line!==void 0&&(this.raw_options.brace_style=this.raw_options.braces_on_own_line?"expand":"collapse");var _=this._get_selection_list("brace_style",["collapse","expand","end-expand","none","preserve-inline"]);this.brace_preserve_inline=!1,this.brace_style="collapse";for(var l=0;l<_.length;l++)_[l]==="preserve-inline"?this.brace_preserve_inline=!0:this.brace_style=_[l];this.unindent_chained_methods=this._get_boolean("unindent_chained_methods"),this.break_chained_methods=this._get_boolean("break_chained_methods"),this.space_in_paren=this._get_boolean("space_in_paren"),this.space_in_empty_paren=this._get_boolean("space_in_empty_paren"),this.jslint_happy=this._get_boolean("jslint_happy"),this.space_after_anon_function=this._get_boolean("space_after_anon_function"),this.space_after_named_function=this._get_boolean("space_after_named_function"),this.keep_array_indentation=this._get_boolean("keep_array_indentation"),this.space_before_conditional=this._get_boolean("space_before_conditional",!0),this.unescape_strings=this._get_boolean("unescape_strings"),this.e4x=this._get_boolean("e4x"),this.comma_first=this._get_boolean("comma_first"),this.operator_position=this._get_selection("operator_position",o),this.test_output_raw=this._get_boolean("test_output_raw"),this.jslint_happy&&(this.space_after_anon_function=!0)}return a.prototype=new t,xt.Options=a,xt}var Y={},Et={},Ft;function Dt(){if(Ft)return Et;Ft=1;var t=RegExp.prototype.hasOwnProperty("sticky");function o(a){this.__input=a||"",this.__input_length=this.__input.length,this.__position=0}return o.prototype.restart=function(){this.__position=0},o.prototype.back=function(){this.__position>0&&(this.__position-=1)},o.prototype.hasNext=function(){return this.__position<this.__input_length},o.prototype.next=function(){var a=null;return this.hasNext()&&(a=this.__input.charAt(this.__position),this.__position+=1),a},o.prototype.peek=function(a){var e=null;return a=a||0,a+=this.__position,a>=0&&a<this.__input_length&&(e=this.__input.charAt(a)),e},o.prototype.__match=function(a,e){a.lastIndex=e;var n=a.exec(this.__input);return n&&!(t&&a.sticky)&&n.index!==e&&(n=null),n},o.prototype.test=function(a,e){return e=e||0,e+=this.__position,e>=0&&e<this.__input_length?!!this.__match(a,e):!1},o.prototype.testChar=function(a,e){var n=this.peek(e);return a.lastIndex=0,n!==null&&a.test(n)},o.prototype.match=function(a){var e=this.__match(a,this.__position);return e?this.__position+=e[0].length:e=null,e},o.prototype.read=function(a,e,n){var _="",l;return a&&(l=this.match(a),l&&(_+=l[0])),e&&(l||!a)&&(_+=this.readUntil(e,n)),_},o.prototype.readUntil=function(a,e){var n="",_=this.__position;a.lastIndex=this.__position;var l=a.exec(this.__input);return l?(_=l.index,e&&(_+=l[0].length)):_=this.__input_length,n=this.__input.substring(this.__position,_),this.__position=_,n},o.prototype.readUntilAfter=function(a){return this.readUntil(a,!0)},o.prototype.get_regexp=function(a,e){var n=null,_="g";return e&&t&&(_="y"),typeof a=="string"&&a!==""?n=new RegExp(a,_):a&&(n=new RegExp(a.source,_)),n},o.prototype.get_literal_regexp=function(a){return RegExp(a.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"))},o.prototype.peekUntilAfter=function(a){var e=this.__position,n=this.readUntilAfter(a);return this.__position=e,n},o.prototype.lookBack=function(a){var e=this.__position-1;return e>=a.length&&this.__input.substring(e-a.length,e).toLowerCase()===a},Et.InputScanner=o,Et}var _t={},Ot={},Vt;function qe(){if(Vt)return Ot;Vt=1;function t(o){this.__tokens=[],this.__tokens_length=this.__tokens.length,this.__position=0,this.__parent_token=o}return t.prototype.restart=function(){this.__position=0},t.prototype.isEmpty=function(){return this.__tokens_length===0},t.prototype.hasNext=function(){return this.__position<this.__tokens_length},t.prototype.next=function(){var o=null;return this.hasNext()&&(o=this.__tokens[this.__position],this.__position+=1),o},t.prototype.peek=function(o){var a=null;return o=o||0,o+=this.__position,o>=0&&o<this.__tokens_length&&(a=this.__tokens[o]),a},t.prototype.add=function(o){this.__parent_token&&(o.parent=this.__parent_token),this.__tokens.push(o),this.__tokens_length+=1},Ot.TokenStream=t,Ot}var $t={},Tt={},Xt;function dt(){if(Xt)return Tt;Xt=1;function t(o,a){this._input=o,this._starting_pattern=null,this._match_pattern=null,this._until_pattern=null,this._until_after=!1,a&&(this._starting_pattern=this._input.get_regexp(a._starting_pattern,!0),this._match_pattern=this._input.get_regexp(a._match_pattern,!0),this._until_pattern=this._input.get_regexp(a._until_pattern),this._until_after=a._until_after)}return t.prototype.read=function(){var o=this._input.read(this._starting_pattern);return(!this._starting_pattern||o)&&(o+=this._input.read(this._match_pattern,this._until_pattern,this._until_after)),o},t.prototype.read_match=function(){return this._input.match(this._match_pattern)},t.prototype.until_after=function(o){var a=this._create();return a._until_after=!0,a._until_pattern=this._input.get_regexp(o),a._update(),a},t.prototype.until=function(o){var a=this._create();return a._until_after=!1,a._until_pattern=this._input.get_regexp(o),a._update(),a},t.prototype.starting_with=function(o){var a=this._create();return a._starting_pattern=this._input.get_regexp(o,!0),a._update(),a},t.prototype.matching=function(o){var a=this._create();return a._match_pattern=this._input.get_regexp(o,!0),a._update(),a},t.prototype._create=function(){return new t(this._input,this)},t.prototype._update=function(){},Tt.Pattern=t,Tt}var Gt;function Fe(){if(Gt)return $t;Gt=1;var t=dt().Pattern;function o(a,e){t.call(this,a,e),e?this._line_regexp=this._input.get_regexp(e._line_regexp):this.__set_whitespace_patterns("",""),this.newline_count=0,this.whitespace_before_token=""}return o.prototype=new t,o.prototype.__set_whitespace_patterns=function(a,e){a+="\\t ",e+="\\n\\r",this._match_pattern=this._input.get_regexp("["+a+e+"]+",!0),this._newline_regexp=this._input.get_regexp("\\r\\n|["+e+"]")},o.prototype.read=function(){this.newline_count=0,this.whitespace_before_token="";var a=this._input.read(this._match_pattern);if(a===" ")this.whitespace_before_token=" ";else if(a){var e=this.__split(this._newline_regexp,a);this.newline_count=e.length-1,this.whitespace_before_token=e[this.newline_count]}return a},o.prototype.matching=function(a,e){var n=this._create();return n.__set_whitespace_patterns(a,e),n._update(),n},o.prototype._create=function(){return new o(this._input,this)},o.prototype.__split=function(a,e){a.lastIndex=0;for(var n=0,_=[],l=a.exec(e);l;)_.push(e.substring(n,l.index)),n=l.index+l[0].length,l=a.exec(e);return n<e.length?_.push(e.substring(n,e.length)):_.push(""),_},$t.WhitespacePattern=o,$t}var Qt;function ft(){if(Qt)return _t;Qt=1;var t=Dt().InputScanner,o=de().Token,a=qe().TokenStream,e=Fe().WhitespacePattern,n={START:"TK_START",RAW:"TK_RAW",EOF:"TK_EOF"},_=function(l,s){this._input=new t(l),this._options=s||{},this.__tokens=null,this._patterns={},this._patterns.whitespace=new e(this._input)};return _.prototype.tokenize=function(){this._input.restart(),this.__tokens=new a,this._reset();for(var l,s=new o(n.START,""),u=null,w=[],m=new a;s.type!==n.EOF;){for(l=this._get_next_token(s,u);this._is_comment(l);)m.add(l),l=this._get_next_token(s,u);m.isEmpty()||(l.comments_before=m,m=new a),l.parent=u,this._is_opening(l)?(w.push(u),u=l):u&&this._is_closing(l,u)&&(l.opened=u,u.closed=l,u=w.pop(),l.parent=u),l.previous=s,s.next=l,this.__tokens.add(l),s=l}return this.__tokens},_.prototype._is_first_token=function(){return this.__tokens.isEmpty()},_.prototype._reset=function(){},_.prototype._get_next_token=function(l,s){this._readWhitespace();var u=this._input.read(/.+/g);return u?this._create_token(n.RAW,u):this._create_token(n.EOF,"")},_.prototype._is_comment=function(l){return!1},_.prototype._is_opening=function(l){return!1},_.prototype._is_closing=function(l,s){return!1},_.prototype._create_token=function(l,s){var u=new o(l,s,this._patterns.whitespace.newline_count,this._patterns.whitespace.whitespace_before_token);return u},_.prototype._readWhitespace=function(){return this._patterns.whitespace.read()},_t.Tokenizer=_,_t.TOKEN=n,_t}var kt={},Jt;function It(){if(Jt)return kt;Jt=1;function t(o,a){o=typeof o=="string"?o:o.source,a=typeof a=="string"?a:a.source,this.__directives_block_pattern=new RegExp(o+/ beautify( \w+[:]\w+)+ /.source+a,"g"),this.__directive_pattern=/ (\w+)[:](\w+)/g,this.__directives_end_ignore_pattern=new RegExp(o+/\sbeautify\signore:end\s/.source+a,"g")}return t.prototype.get_directives=function(o){if(!o.match(this.__directives_block_pattern))return null;var a={};this.__directive_pattern.lastIndex=0;for(var e=this.__directive_pattern.exec(o);e;)a[e[1]]=e[2],e=this.__directive_pattern.exec(o);return a},t.prototype.readIgnored=function(o){return o.readUntilAfter(this.__directives_end_ignore_pattern)},kt.Directives=t,kt}var Rt={},Ht;function be(){if(Ht)return Rt;Ht=1;var t=dt().Pattern,o={django:!1,erb:!1,handlebars:!1,php:!1,smarty:!1};function a(e,n){t.call(this,e,n),this.__template_pattern=null,this._disabled=Object.assign({},o),this._excluded=Object.assign({},o),n&&(this.__template_pattern=this._input.get_regexp(n.__template_pattern),this._excluded=Object.assign(this._excluded,n._excluded),this._disabled=Object.assign(this._disabled,n._disabled));var _=new t(e);this.__patterns={handlebars_comment:_.starting_with(/{{!--/).until_after(/--}}/),handlebars_unescaped:_.starting_with(/{{{/).until_after(/}}}/),handlebars:_.starting_with(/{{/).until_after(/}}/),php:_.starting_with(/<\?(?:[= ]|php)/).until_after(/\?>/),erb:_.starting_with(/<%[^%]/).until_after(/[^%]%>/),django:_.starting_with(/{%/).until_after(/%}/),django_value:_.starting_with(/{{/).until_after(/}}/),django_comment:_.starting_with(/{#/).until_after(/#}/),smarty:_.starting_with(/{(?=[^}{\s\n])/).until_after(/[^\s\n]}/),smarty_comment:_.starting_with(/{\*/).until_after(/\*}/),smarty_literal:_.starting_with(/{literal}/).until_after(/{\/literal}/)}}return a.prototype=new t,a.prototype._create=function(){return new a(this._input,this)},a.prototype._update=function(){this.__set_templated_pattern()},a.prototype.disable=function(e){var n=this._create();return n._disabled[e]=!0,n._update(),n},a.prototype.read_options=function(e){var n=this._create();for(var _ in o)n._disabled[_]=e.templating.indexOf(_)===-1;return n._update(),n},a.prototype.exclude=function(e){var n=this._create();return n._excluded[e]=!0,n._update(),n},a.prototype.read=function(){var e="";this._match_pattern?e=this._input.read(this._starting_pattern):e=this._input.read(this._starting_pattern,this.__template_pattern);for(var n=this._read_template();n;)this._match_pattern?n+=this._input.read(this._match_pattern):n+=this._input.readUntil(this.__template_pattern),e+=n,n=this._read_template();return this._until_after&&(e+=this._input.readUntilAfter(this._until_pattern)),e},a.prototype.__set_templated_pattern=function(){var e=[];this._disabled.php||e.push(this.__patterns.php._starting_pattern.source),this._disabled.handlebars||e.push(this.__patterns.handlebars._starting_pattern.source),this._disabled.erb||e.push(this.__patterns.erb._starting_pattern.source),this._disabled.django||(e.push(this.__patterns.django._starting_pattern.source),e.push(this.__patterns.django_value._starting_pattern.source),e.push(this.__patterns.django_comment._starting_pattern.source)),this._disabled.smarty||e.push(this.__patterns.smarty._starting_pattern.source),this._until_pattern&&e.push(this._until_pattern.source),this.__template_pattern=this._input.get_regexp("(?:"+e.join("|")+")")},a.prototype._read_template=function(){var e="",n=this._input.peek();if(n==="<"){var _=this._input.peek(1);!this._disabled.php&&!this._excluded.php&&_==="?"&&(e=e||this.__patterns.php.read()),!this._disabled.erb&&!this._excluded.erb&&_==="%"&&(e=e||this.__patterns.erb.read())}else n==="{"&&(!this._disabled.handlebars&&!this._excluded.handlebars&&(e=e||this.__patterns.handlebars_comment.read(),e=e||this.__patterns.handlebars_unescaped.read(),e=e||this.__patterns.handlebars.read()),this._disabled.django||(!this._excluded.django&&!this._excluded.handlebars&&(e=e||this.__patterns.django_value.read()),this._excluded.django||(e=e||this.__patterns.django_comment.read(),e=e||this.__patterns.django.read())),this._disabled.smarty||this._disabled.django&&this._disabled.handlebars&&(e=e||this.__patterns.smarty_comment.read(),e=e||this.__patterns.smarty_literal.read(),e=e||this.__patterns.smarty.read()));return e},Rt.TemplatablePattern=a,Rt}var Yt;function ot(){if(Yt)return Y;Yt=1;var t=Dt().InputScanner,o=ft().Tokenizer,a=ft().TOKEN,e=It().Directives,n=ge(),_=dt().Pattern,l=be().TemplatablePattern;function s(c,y){return y.indexOf(c)!==-1}var u={START_EXPR:"TK_START_EXPR",END_EXPR:"TK_END_EXPR",START_BLOCK:"TK_START_BLOCK",END_BLOCK:"TK_END_BLOCK",WORD:"TK_WORD",RESERVED:"TK_RESERVED",SEMICOLON:"TK_SEMICOLON",STRING:"TK_STRING",EQUALS:"TK_EQUALS",OPERATOR:"TK_OPERATOR",COMMA:"TK_COMMA",BLOCK_COMMENT:"TK_BLOCK_COMMENT",COMMENT:"TK_COMMENT",DOT:"TK_DOT",UNKNOWN:"TK_UNKNOWN",START:a.START,RAW:a.RAW,EOF:a.EOF},w=new e(/\/\*/,/\*\//),m=/0[xX][0123456789abcdefABCDEF_]*n?|0[oO][01234567_]*n?|0[bB][01_]*n?|\d[\d_]*n|(?:\.\d[\d_]*|\d[\d_]*\.?[\d_]*)(?:[eE][+-]?[\d_]+)?/,d=/[0-9]/,b=/[^\d\.]/,A=">>> === !== &&= ??= ||= << && >= ** != == <= >> || ?? |> < / - + > : & % ? ^ | *".split(" "),T=">>>= ... >>= <<= === >>> !== **= &&= ??= ||= => ^= :: /= << <= == && -= >= >> != -- += ** || ?? ++ %= &= *= |= |> = ! ? > < : / ^ - + * & % ~ |";T=T.replace(/[-[\]{}()*+?.,\\^$|#]/g,"\\$&"),T="\\?\\.(?!\\d) "+T,T=T.replace(/ /g,"|");var M=new RegExp(T),p="continue,try,throw,return,var,let,const,if,switch,case,default,for,while,break,function,import,export".split(","),r=p.concat(["do","in","of","else","get","set","new","catch","finally","typeof","yield","async","await","from","as","class","extends"]),f=new RegExp("^(?:"+r.join("|")+")$"),C,x=function(c,y){o.call(this,c,y),this._patterns.whitespace=this._patterns.whitespace.matching(/\u00A0\u1680\u180e\u2000-\u200a\u202f\u205f\u3000\ufeff/.source,/\u2028\u2029/.source);var h=new _(this._input),E=new l(this._input).read_options(this._options);this.__patterns={template:E,identifier:E.starting_with(n.identifier).matching(n.identifierMatch),number:h.matching(m),punct:h.matching(M),comment:h.starting_with(/\/\//).until(/[\n\r\u2028\u2029]/),block_comment:h.starting_with(/\/\*/).until_after(/\*\//),html_comment_start:h.matching(/<!--/),html_comment_end:h.matching(/-->/),include:h.starting_with(/#include/).until_after(n.lineBreak),shebang:h.starting_with(/#!/).until_after(n.lineBreak),xml:h.matching(/[\s\S]*?<(\/?)([-a-zA-Z:0-9_.]+|{[^}]+?}|!\[CDATA\[[^\]]*?\]\]|)(\s*{[^}]+?}|\s+[-a-zA-Z:0-9_.]+|\s+[-a-zA-Z:0-9_.]+\s*=\s*('[^']*'|"[^"]*"|{([^{}]|{[^}]+?})+?}))*\s*(\/?)\s*>/),single_quote:E.until(/['\\\n\r\u2028\u2029]/),double_quote:E.until(/["\\\n\r\u2028\u2029]/),template_text:E.until(/[`\\$]/),template_expression:E.until(/[`}\\]/)}};x.prototype=new o,x.prototype._is_comment=function(c){return c.type===u.COMMENT||c.type===u.BLOCK_COMMENT||c.type===u.UNKNOWN},x.prototype._is_opening=function(c){return c.type===u.START_BLOCK||c.type===u.START_EXPR},x.prototype._is_closing=function(c,y){return(c.type===u.END_BLOCK||c.type===u.END_EXPR)&&y&&(c.text==="]"&&y.text==="["||c.text===")"&&y.text==="("||c.text==="}"&&y.text==="{")},x.prototype._reset=function(){C=!1},x.prototype._get_next_token=function(c,y){var h=null;this._readWhitespace();var E=this._input.peek();return E===null?this._create_token(u.EOF,""):(h=h||this._read_non_javascript(E),h=h||this._read_string(E),h=h||this._read_pair(E,this._input.peek(1)),h=h||this._read_word(c),h=h||this._read_singles(E),h=h||this._read_comment(E),h=h||this._read_regexp(E,c),h=h||this._read_xml(E,c),h=h||this._read_punctuation(),h=h||this._create_token(u.UNKNOWN,this._input.next()),h)},x.prototype._read_word=function(c){var y;if(y=this.__patterns.identifier.read(),y!=="")return y=y.replace(n.allLineBreaks,`
`),!(c.type===u.DOT||c.type===u.RESERVED&&(c.text==="set"||c.text==="get"))&&f.test(y)?(y==="in"||y==="of")&&(c.type===u.WORD||c.type===u.STRING)?this._create_token(u.OPERATOR,y):this._create_token(u.RESERVED,y):this._create_token(u.WORD,y);if(y=this.__patterns.number.read(),y!=="")return this._create_token(u.WORD,y)},x.prototype._read_singles=function(c){var y=null;return c==="("||c==="["?y=this._create_token(u.START_EXPR,c):c===")"||c==="]"?y=this._create_token(u.END_EXPR,c):c==="{"?y=this._create_token(u.START_BLOCK,c):c==="}"?y=this._create_token(u.END_BLOCK,c):c===";"?y=this._create_token(u.SEMICOLON,c):c==="."&&b.test(this._input.peek(1))?y=this._create_token(u.DOT,c):c===","&&(y=this._create_token(u.COMMA,c)),y&&this._input.next(),y},x.prototype._read_pair=function(c,y){var h=null;return c==="#"&&y==="{"&&(h=this._create_token(u.START_BLOCK,c+y)),h&&(this._input.next(),this._input.next()),h},x.prototype._read_punctuation=function(){var c=this.__patterns.punct.read();if(c!=="")return c==="="?this._create_token(u.EQUALS,c):c==="?."?this._create_token(u.DOT,c):this._create_token(u.OPERATOR,c)},x.prototype._read_non_javascript=function(c){var y="";if(c==="#"){if(this._is_first_token()&&(y=this.__patterns.shebang.read(),y))return this._create_token(u.UNKNOWN,y.trim()+`
`);if(y=this.__patterns.include.read(),y)return this._create_token(u.UNKNOWN,y.trim()+`
`);c=this._input.next();var h="#";if(this._input.hasNext()&&this._input.testChar(d)){do c=this._input.next(),h+=c;while(this._input.hasNext()&&c!=="#"&&c!=="=");return c==="#"||(this._input.peek()==="["&&this._input.peek(1)==="]"?(h+="[]",this._input.next(),this._input.next()):this._input.peek()==="{"&&this._input.peek(1)==="}"&&(h+="{}",this._input.next(),this._input.next())),this._create_token(u.WORD,h)}this._input.back()}else if(c==="<"&&this._is_first_token()){if(y=this.__patterns.html_comment_start.read(),y){for(;this._input.hasNext()&&!this._input.testChar(n.newline);)y+=this._input.next();return C=!0,this._create_token(u.COMMENT,y)}}else if(C&&c==="-"&&(y=this.__patterns.html_comment_end.read(),y))return C=!1,this._create_token(u.COMMENT,y);return null},x.prototype._read_comment=function(c){var y=null;if(c==="/"){var h="";if(this._input.peek(1)==="*"){h=this.__patterns.block_comment.read();var E=w.get_directives(h);E&&E.ignore==="start"&&(h+=w.readIgnored(this._input)),h=h.replace(n.allLineBreaks,`
`),y=this._create_token(u.BLOCK_COMMENT,h),y.directives=E}else this._input.peek(1)==="/"&&(h=this.__patterns.comment.read(),y=this._create_token(u.COMMENT,h))}return y},x.prototype._read_string=function(c){if(c==="`"||c==="'"||c==='"'){var y=this._input.next();return this.has_char_escapes=!1,c==="`"?y+=this._read_string_recursive("`",!0,"${"):y+=this._read_string_recursive(c),this.has_char_escapes&&this._options.unescape_strings&&(y=L(y)),this._input.peek()===c&&(y+=this._input.next()),y=y.replace(n.allLineBreaks,`
`),this._create_token(u.STRING,y)}return null},x.prototype._allow_regexp_or_xml=function(c){return c.type===u.RESERVED&&s(c.text,["return","case","throw","else","do","typeof","yield"])||c.type===u.END_EXPR&&c.text===")"&&c.opened.previous.type===u.RESERVED&&s(c.opened.previous.text,["if","while","for"])||s(c.type,[u.COMMENT,u.START_EXPR,u.START_BLOCK,u.START,u.END_BLOCK,u.OPERATOR,u.EQUALS,u.EOF,u.SEMICOLON,u.COMMA])},x.prototype._read_regexp=function(c,y){if(c==="/"&&this._allow_regexp_or_xml(y)){for(var h=this._input.next(),E=!1,i=!1;this._input.hasNext()&&(E||i||this._input.peek()!==c)&&!this._input.testChar(n.newline);)h+=this._input.peek(),E?E=!1:(E=this._input.peek()==="\\",this._input.peek()==="["?i=!0:this._input.peek()==="]"&&(i=!1)),this._input.next();return this._input.peek()===c&&(h+=this._input.next(),h+=this._input.read(n.identifier)),this._create_token(u.STRING,h)}return null},x.prototype._read_xml=function(c,y){if(this._options.e4x&&c==="<"&&this._allow_regexp_or_xml(y)){var h="",E=this.__patterns.xml.read_match();if(E){for(var i=E[2].replace(/^{\s+/,"{").replace(/\s+}$/,"}"),g=i.indexOf("{")===0,v=0;E;){var N=!!E[1],P=E[2],O=!!E[E.length-1]||P.slice(0,8)==="![CDATA[";if(!O&&(P===i||g&&P.replace(/^{\s+/,"{").replace(/\s+}$/,"}"))&&(N?--v:++v),h+=E[0],v<=0)break;E=this.__patterns.xml.read_match()}return E||(h+=this._input.match(/[\s\S]*/g)[0]),h=h.replace(n.allLineBreaks,`
`),this._create_token(u.STRING,h)}}return null};function L(c){for(var y="",h=0,E=new t(c),i=null;E.hasNext();)if(i=E.match(/([\s]|[^\\]|\\\\)+/g),i&&(y+=i[0]),E.peek()==="\\"){if(E.next(),E.peek()==="x")i=E.match(/x([0-9A-Fa-f]{2})/g);else if(E.peek()==="u")i=E.match(/u([0-9A-Fa-f]{4})/g),i||(i=E.match(/u\{([0-9A-Fa-f]+)\}/g));else{y+="\\",E.hasNext()&&(y+=E.next());continue}if(!i||(h=parseInt(i[1],16),h>126&&h<=255&&i[0].indexOf("x")===0))return c;h>=0&&h<32||h>1114111?y+="\\"+i[0]:h===34||h===39||h===92?y+="\\"+String.fromCharCode(h):y+=String.fromCharCode(h)}return y}return x.prototype._read_string_recursive=function(c,y,h){var E,i;c==="'"?i=this.__patterns.single_quote:c==='"'?i=this.__patterns.double_quote:c==="`"?i=this.__patterns.template_text:c==="}"&&(i=this.__patterns.template_expression);for(var g=i.read(),v="";this._input.hasNext();){if(v=this._input.next(),v===c||!y&&n.newline.test(v)){this._input.back();break}else v==="\\"&&this._input.hasNext()?(E=this._input.peek(),E==="x"||E==="u"?this.has_char_escapes=!0:E==="\r"&&this._input.peek(1)===`
`&&this._input.next(),v+=this._input.next()):h&&(h==="${"&&v==="$"&&this._input.peek()==="{"&&(v+=this._input.next()),h===v&&(c==="`"?v+=this._read_string_recursive("}",y,"`"):v+=this._read_string_recursive("`",y,"${"),this._input.hasNext()&&(v+=this._input.next())));v+=i.read(),g+=v}return g},Y.Tokenizer=x,Y.TOKEN=u,Y.positionable_operators=A.slice(),Y.line_starters=p.slice(),Y}var Zt;function Ve(){if(Zt)return bt;Zt=1;var t=Mt().Output,o=de().Token,a=ge(),e=me().Options,n=ot().Tokenizer,_=ot().line_starters,l=ot().positionable_operators,s=ot().TOKEN;function u(i,g){return g.indexOf(i)!==-1}function w(i){return i.replace(/^\s+/g,"")}function m(i){for(var g={},v=0;v<i.length;v++)g[i[v].replace(/-/g,"_")]=i[v];return g}function d(i,g){return i&&i.type===s.RESERVED&&i.text===g}function b(i,g){return i&&i.type===s.RESERVED&&u(i.text,g)}var A=["case","return","do","if","throw","else","await","break","continue","async"],T=["before-newline","after-newline","preserve-newline"],M=m(T),p=[M.before_newline,M.preserve_newline],r={BlockStatement:"BlockStatement",Statement:"Statement",ObjectLiteral:"ObjectLiteral",ArrayLiteral:"ArrayLiteral",ForInitializer:"ForInitializer",Conditional:"Conditional",Expression:"Expression"};function f(i,g){g.multiline_frame||g.mode===r.ForInitializer||g.mode===r.Conditional||i.remove_indent(g.start_line_index)}function C(i){i=i.replace(a.allLineBreaks,`
`);for(var g=[],v=i.indexOf(`
`);v!==-1;)g.push(i.substring(0,v)),i=i.substring(v+1),v=i.indexOf(`
`);return i.length&&g.push(i),g}function x(i){return i===r.ArrayLiteral}function L(i){return u(i,[r.Expression,r.ForInitializer,r.Conditional])}function c(i,g){for(var v=0;v<i.length;v++){var N=i[v].trim();if(N.charAt(0)!==g)return!1}return!0}function y(i,g){for(var v=0,N=i.length,P;v<N;v++)if(P=i[v],P&&P.indexOf(g)!==0)return!1;return!0}function h(i,g){g=g||{},this._source_text=i||"",this._output=null,this._tokens=null,this._last_last_text=null,this._flags=null,this._previous_flags=null,this._flag_store=null,this._options=new e(g)}h.prototype.create_flags=function(i,g){var v=0;i&&(v=i.indentation_level,!this._output.just_added_newline()&&i.line_indent_level>v&&(v=i.line_indent_level));var N={mode:g,parent:i,last_token:i?i.last_token:new o(s.START_BLOCK,""),last_word:i?i.last_word:"",declaration_statement:!1,declaration_assignment:!1,multiline_frame:!1,inline_frame:!1,if_block:!1,else_block:!1,class_start_block:!1,do_block:!1,do_while:!1,import_block:!1,in_case_statement:!1,in_case:!1,case_body:!1,case_block:!1,indentation_level:v,alignment:0,line_indent_level:i?i.line_indent_level:v,start_line_index:this._output.get_line_number(),ternary_depth:0};return N},h.prototype._reset=function(i){var g=i.match(/^[\t ]*/)[0];this._last_last_text="",this._output=new t(this._options,g),this._output.raw=this._options.test_output_raw,this._flag_store=[],this.set_mode(r.BlockStatement);var v=new n(i,this._options);return this._tokens=v.tokenize(),i},h.prototype.beautify=function(){if(this._options.disabled)return this._source_text;var i,g=this._reset(this._source_text),v=this._options.eol;this._options.eol==="auto"&&(v=`
`,g&&a.lineBreak.test(g||"")&&(v=g.match(a.lineBreak)[0]));for(var N=this._tokens.next();N;)this.handle_token(N),this._last_last_text=this._flags.last_token.text,this._flags.last_token=N,N=this._tokens.next();return i=this._output.get_code(v),i},h.prototype.handle_token=function(i,g){i.type===s.START_EXPR?this.handle_start_expr(i):i.type===s.END_EXPR?this.handle_end_expr(i):i.type===s.START_BLOCK?this.handle_start_block(i):i.type===s.END_BLOCK?this.handle_end_block(i):i.type===s.WORD?this.handle_word(i):i.type===s.RESERVED?this.handle_word(i):i.type===s.SEMICOLON?this.handle_semicolon(i):i.type===s.STRING?this.handle_string(i):i.type===s.EQUALS?this.handle_equals(i):i.type===s.OPERATOR?this.handle_operator(i):i.type===s.COMMA?this.handle_comma(i):i.type===s.BLOCK_COMMENT?this.handle_block_comment(i,g):i.type===s.COMMENT?this.handle_comment(i,g):i.type===s.DOT?this.handle_dot(i):i.type===s.EOF?this.handle_eof(i):i.type===s.UNKNOWN?this.handle_unknown(i,g):this.handle_unknown(i,g)},h.prototype.handle_whitespace_and_comments=function(i,g){var v=i.newlines,N=this._options.keep_array_indentation&&x(this._flags.mode);if(i.comments_before)for(var P=i.comments_before.next();P;)this.handle_whitespace_and_comments(P,g),this.handle_token(P,g),P=i.comments_before.next();if(N)for(var O=0;O<v;O+=1)this.print_newline(O>0,g);else if(this._options.max_preserve_newlines&&v>this._options.max_preserve_newlines&&(v=this._options.max_preserve_newlines),this._options.preserve_newlines&&v>1){this.print_newline(!1,g);for(var $=1;$<v;$+=1)this.print_newline(!0,g)}};var E=["async","break","continue","return","throw","yield"];return h.prototype.allow_wrap_or_preserved_newline=function(i,g){if(g=g===void 0?!1:g,!this._output.just_added_newline()){var v=this._options.preserve_newlines&&i.newlines||g,N=u(this._flags.last_token.text,l)||u(i.text,l);if(N){var P=u(this._flags.last_token.text,l)&&u(this._options.operator_position,p)||u(i.text,l);v=v&&P}if(v)this.print_newline(!1,!0);else if(this._options.wrap_line_length){if(b(this._flags.last_token,E))return;this._output.set_wrap_point()}}},h.prototype.print_newline=function(i,g){if(!g&&this._flags.last_token.text!==";"&&this._flags.last_token.text!==","&&this._flags.last_token.text!=="="&&(this._flags.last_token.type!==s.OPERATOR||this._flags.last_token.text==="--"||this._flags.last_token.text==="++"))for(var v=this._tokens.peek();this._flags.mode===r.Statement&&!(this._flags.if_block&&d(v,"else"))&&!this._flags.do_block;)this.restore_mode();this._output.add_new_line(i)&&(this._flags.multiline_frame=!0)},h.prototype.print_token_line_indentation=function(i){this._output.just_added_newline()&&(this._options.keep_array_indentation&&i.newlines&&(i.text==="["||x(this._flags.mode))?(this._output.current_line.set_indent(-1),this._output.current_line.push(i.whitespace_before),this._output.space_before_token=!1):this._output.set_indent(this._flags.indentation_level,this._flags.alignment)&&(this._flags.line_indent_level=this._flags.indentation_level))},h.prototype.print_token=function(i){if(this._output.raw){this._output.add_raw_token(i);return}if(this._options.comma_first&&i.previous&&i.previous.type===s.COMMA&&this._output.just_added_newline()&&this._output.previous_line.last()===","){var g=this._output.previous_line.pop();this._output.previous_line.is_empty()&&(this._output.previous_line.push(g),this._output.trim(!0),this._output.current_line.pop(),this._output.trim()),this.print_token_line_indentation(i),this._output.add_token(","),this._output.space_before_token=!0}this.print_token_line_indentation(i),this._output.non_breaking_space=!0,this._output.add_token(i.text),this._output.previous_token_wrapped&&(this._flags.multiline_frame=!0)},h.prototype.indent=function(){this._flags.indentation_level+=1,this._output.set_indent(this._flags.indentation_level,this._flags.alignment)},h.prototype.deindent=function(){this._flags.indentation_level>0&&(!this._flags.parent||this._flags.indentation_level>this._flags.parent.indentation_level)&&(this._flags.indentation_level-=1,this._output.set_indent(this._flags.indentation_level,this._flags.alignment))},h.prototype.set_mode=function(i){this._flags?(this._flag_store.push(this._flags),this._previous_flags=this._flags):this._previous_flags=this.create_flags(null,i),this._flags=this.create_flags(this._previous_flags,i),this._output.set_indent(this._flags.indentation_level,this._flags.alignment)},h.prototype.restore_mode=function(){this._flag_store.length>0&&(this._previous_flags=this._flags,this._flags=this._flag_store.pop(),this._previous_flags.mode===r.Statement&&f(this._output,this._previous_flags),this._output.set_indent(this._flags.indentation_level,this._flags.alignment))},h.prototype.start_of_object_property=function(){return this._flags.parent.mode===r.ObjectLiteral&&this._flags.mode===r.Statement&&(this._flags.last_token.text===":"&&this._flags.ternary_depth===0||b(this._flags.last_token,["get","set"]))},h.prototype.start_of_statement=function(i){var g=!1;return g=g||b(this._flags.last_token,["var","let","const"])&&i.type===s.WORD,g=g||d(this._flags.last_token,"do"),g=g||!(this._flags.parent.mode===r.ObjectLiteral&&this._flags.mode===r.Statement)&&b(this._flags.last_token,E)&&!i.newlines,g=g||d(this._flags.last_token,"else")&&!(d(i,"if")&&!i.comments_before),g=g||this._flags.last_token.type===s.END_EXPR&&(this._previous_flags.mode===r.ForInitializer||this._previous_flags.mode===r.Conditional),g=g||this._flags.last_token.type===s.WORD&&this._flags.mode===r.BlockStatement&&!this._flags.in_case&&!(i.text==="--"||i.text==="++")&&this._last_last_text!=="function"&&i.type!==s.WORD&&i.type!==s.RESERVED,g=g||this._flags.mode===r.ObjectLiteral&&(this._flags.last_token.text===":"&&this._flags.ternary_depth===0||b(this._flags.last_token,["get","set"])),g?(this.set_mode(r.Statement),this.indent(),this.handle_whitespace_and_comments(i,!0),this.start_of_object_property()||this.allow_wrap_or_preserved_newline(i,b(i,["do","for","if","while"])),!0):!1},h.prototype.handle_start_expr=function(i){this.start_of_statement(i)||this.handle_whitespace_and_comments(i);var g=r.Expression;if(i.text==="["){if(this._flags.last_token.type===s.WORD||this._flags.last_token.text===")"){b(this._flags.last_token,_)&&(this._output.space_before_token=!0),this.print_token(i),this.set_mode(g),this.indent(),this._options.space_in_paren&&(this._output.space_before_token=!0);return}g=r.ArrayLiteral,x(this._flags.mode)&&(this._flags.last_token.text==="["||this._flags.last_token.text===","&&(this._last_last_text==="]"||this._last_last_text==="}"))&&(this._options.keep_array_indentation||this.print_newline()),u(this._flags.last_token.type,[s.START_EXPR,s.END_EXPR,s.WORD,s.OPERATOR,s.DOT])||(this._output.space_before_token=!0)}else{if(this._flags.last_token.type===s.RESERVED)this._flags.last_token.text==="for"?(this._output.space_before_token=this._options.space_before_conditional,g=r.ForInitializer):u(this._flags.last_token.text,["if","while","switch"])?(this._output.space_before_token=this._options.space_before_conditional,g=r.Conditional):u(this._flags.last_word,["await","async"])?this._output.space_before_token=!0:this._flags.last_token.text==="import"&&i.whitespace_before===""?this._output.space_before_token=!1:(u(this._flags.last_token.text,_)||this._flags.last_token.text==="catch")&&(this._output.space_before_token=!0);else if(this._flags.last_token.type===s.EQUALS||this._flags.last_token.type===s.OPERATOR)this.start_of_object_property()||this.allow_wrap_or_preserved_newline(i);else if(this._flags.last_token.type===s.WORD){this._output.space_before_token=!1;var v=this._tokens.peek(-3);if(this._options.space_after_named_function&&v){var N=this._tokens.peek(-4);b(v,["async","function"])||v.text==="*"&&b(N,["async","function"])?this._output.space_before_token=!0:this._flags.mode===r.ObjectLiteral?(v.text==="{"||v.text===","||v.text==="*"&&(N.text==="{"||N.text===","))&&(this._output.space_before_token=!0):this._flags.parent&&this._flags.parent.class_start_block&&(this._output.space_before_token=!0)}}else this.allow_wrap_or_preserved_newline(i);(this._flags.last_token.type===s.RESERVED&&(this._flags.last_word==="function"||this._flags.last_word==="typeof")||this._flags.last_token.text==="*"&&(u(this._last_last_text,["function","yield"])||this._flags.mode===r.ObjectLiteral&&u(this._last_last_text,["{",","])))&&(this._output.space_before_token=this._options.space_after_anon_function)}this._flags.last_token.text===";"||this._flags.last_token.type===s.START_BLOCK?this.print_newline():(this._flags.last_token.type===s.END_EXPR||this._flags.last_token.type===s.START_EXPR||this._flags.last_token.type===s.END_BLOCK||this._flags.last_token.text==="."||this._flags.last_token.type===s.COMMA)&&this.allow_wrap_or_preserved_newline(i,i.newlines),this.print_token(i),this.set_mode(g),this._options.space_in_paren&&(this._output.space_before_token=!0),this.indent()},h.prototype.handle_end_expr=function(i){for(;this._flags.mode===r.Statement;)this.restore_mode();this.handle_whitespace_and_comments(i),this._flags.multiline_frame&&this.allow_wrap_or_preserved_newline(i,i.text==="]"&&x(this._flags.mode)&&!this._options.keep_array_indentation),this._options.space_in_paren&&(this._flags.last_token.type===s.START_EXPR&&!this._options.space_in_empty_paren?(this._output.trim(),this._output.space_before_token=!1):this._output.space_before_token=!0),this.deindent(),this.print_token(i),this.restore_mode(),f(this._output,this._previous_flags),this._flags.do_while&&this._previous_flags.mode===r.Conditional&&(this._previous_flags.mode=r.Expression,this._flags.do_block=!1,this._flags.do_while=!1)},h.prototype.handle_start_block=function(i){this.handle_whitespace_and_comments(i);var g=this._tokens.peek(),v=this._tokens.peek(1);this._flags.last_word==="switch"&&this._flags.last_token.type===s.END_EXPR?(this.set_mode(r.BlockStatement),this._flags.in_case_statement=!0):this._flags.case_body?this.set_mode(r.BlockStatement):v&&(u(v.text,[":",","])&&u(g.type,[s.STRING,s.WORD,s.RESERVED])||u(g.text,["get","set","..."])&&u(v.type,[s.WORD,s.RESERVED]))?u(this._last_last_text,["class","interface"])&&!u(v.text,[":",","])?this.set_mode(r.BlockStatement):this.set_mode(r.ObjectLiteral):this._flags.last_token.type===s.OPERATOR&&this._flags.last_token.text==="=>"?this.set_mode(r.BlockStatement):u(this._flags.last_token.type,[s.EQUALS,s.START_EXPR,s.COMMA,s.OPERATOR])||b(this._flags.last_token,["return","throw","import","default"])?this.set_mode(r.ObjectLiteral):this.set_mode(r.BlockStatement),this._flags.last_token&&b(this._flags.last_token.previous,["class","extends"])&&(this._flags.class_start_block=!0);var N=!g.comments_before&&g.text==="}",P=N&&this._flags.last_word==="function"&&this._flags.last_token.type===s.END_EXPR;if(this._options.brace_preserve_inline){var O=0,$=null;this._flags.inline_frame=!0;do if(O+=1,$=this._tokens.peek(O-1),$.newlines){this._flags.inline_frame=!1;break}while($.type!==s.EOF&&!($.type===s.END_BLOCK&&$.opened===i))}(this._options.brace_style==="expand"||this._options.brace_style==="none"&&i.newlines)&&!this._flags.inline_frame?this._flags.last_token.type!==s.OPERATOR&&(P||this._flags.last_token.type===s.EQUALS||b(this._flags.last_token,A)&&this._flags.last_token.text!=="else")?this._output.space_before_token=!0:this.print_newline(!1,!0):(x(this._previous_flags.mode)&&(this._flags.last_token.type===s.START_EXPR||this._flags.last_token.type===s.COMMA)&&((this._flags.last_token.type===s.COMMA||this._options.space_in_paren)&&(this._output.space_before_token=!0),(this._flags.last_token.type===s.COMMA||this._flags.last_token.type===s.START_EXPR&&this._flags.inline_frame)&&(this.allow_wrap_or_preserved_newline(i),this._previous_flags.multiline_frame=this._previous_flags.multiline_frame||this._flags.multiline_frame,this._flags.multiline_frame=!1)),this._flags.last_token.type!==s.OPERATOR&&this._flags.last_token.type!==s.START_EXPR&&(u(this._flags.last_token.type,[s.START_BLOCK,s.SEMICOLON])&&!this._flags.inline_frame?this.print_newline():this._output.space_before_token=!0)),this.print_token(i),this.indent(),!N&&!(this._options.brace_preserve_inline&&this._flags.inline_frame)&&this.print_newline()},h.prototype.handle_end_block=function(i){for(this.handle_whitespace_and_comments(i);this._flags.mode===r.Statement;)this.restore_mode();var g=this._flags.last_token.type===s.START_BLOCK;this._flags.inline_frame&&!g?this._output.space_before_token=!0:this._options.brace_style==="expand"?g||this.print_newline():g||(x(this._flags.mode)&&this._options.keep_array_indentation?(this._options.keep_array_indentation=!1,this.print_newline(),this._options.keep_array_indentation=!0):this.print_newline()),this.restore_mode(),this.print_token(i)},h.prototype.handle_word=function(i){if(i.type===s.RESERVED){if(u(i.text,["set","get"])&&this._flags.mode!==r.ObjectLiteral)i.type=s.WORD;else if(i.text==="import"&&u(this._tokens.peek().text,["(","."]))i.type=s.WORD;else if(u(i.text,["as","from"])&&!this._flags.import_block)i.type=s.WORD;else if(this._flags.mode===r.ObjectLiteral){var g=this._tokens.peek();g.text===":"&&(i.type=s.WORD)}}if(this.start_of_statement(i)?b(this._flags.last_token,["var","let","const"])&&i.type===s.WORD&&(this._flags.declaration_statement=!0):i.newlines&&!L(this._flags.mode)&&(this._flags.last_token.type!==s.OPERATOR||this._flags.last_token.text==="--"||this._flags.last_token.text==="++")&&this._flags.last_token.type!==s.EQUALS&&(this._options.preserve_newlines||!b(this._flags.last_token,["var","let","const","set","get"]))?(this.handle_whitespace_and_comments(i),this.print_newline()):this.handle_whitespace_and_comments(i),this._flags.do_block&&!this._flags.do_while)if(d(i,"while")){this._output.space_before_token=!0,this.print_token(i),this._output.space_before_token=!0,this._flags.do_while=!0;return}else this.print_newline(),this._flags.do_block=!1;if(this._flags.if_block)if(!this._flags.else_block&&d(i,"else"))this._flags.else_block=!0;else{for(;this._flags.mode===r.Statement;)this.restore_mode();this._flags.if_block=!1,this._flags.else_block=!1}if(this._flags.in_case_statement&&b(i,["case","default"])){this.print_newline(),!this._flags.case_block&&(this._flags.case_body||this._options.jslint_happy)&&this.deindent(),this._flags.case_body=!1,this.print_token(i),this._flags.in_case=!0;return}if((this._flags.last_token.type===s.COMMA||this._flags.last_token.type===s.START_EXPR||this._flags.last_token.type===s.EQUALS||this._flags.last_token.type===s.OPERATOR)&&!this.start_of_object_property()&&!(u(this._flags.last_token.text,["+","-"])&&this._last_last_text===":"&&this._flags.parent.mode===r.ObjectLiteral)&&this.allow_wrap_or_preserved_newline(i),d(i,"function")){(u(this._flags.last_token.text,["}",";"])||this._output.just_added_newline()&&!(u(this._flags.last_token.text,["(","[","{",":","=",","])||this._flags.last_token.type===s.OPERATOR))&&!this._output.just_added_blankline()&&!i.comments_before&&(this.print_newline(),this.print_newline(!0)),this._flags.last_token.type===s.RESERVED||this._flags.last_token.type===s.WORD?b(this._flags.last_token,["get","set","new","export"])||b(this._flags.last_token,E)?this._output.space_before_token=!0:d(this._flags.last_token,"default")&&this._last_last_text==="export"?this._output.space_before_token=!0:this._flags.last_token.text==="declare"?this._output.space_before_token=!0:this.print_newline():this._flags.last_token.type===s.OPERATOR||this._flags.last_token.text==="="?this._output.space_before_token=!0:!this._flags.multiline_frame&&(L(this._flags.mode)||x(this._flags.mode))||this.print_newline(),this.print_token(i),this._flags.last_word=i.text;return}var v="NONE";if(this._flags.last_token.type===s.END_BLOCK?this._previous_flags.inline_frame?v="SPACE":b(i,["else","catch","finally","from"])?this._options.brace_style==="expand"||this._options.brace_style==="end-expand"||this._options.brace_style==="none"&&i.newlines?v="NEWLINE":(v="SPACE",this._output.space_before_token=!0):v="NEWLINE":this._flags.last_token.type===s.SEMICOLON&&this._flags.mode===r.BlockStatement?v="NEWLINE":this._flags.last_token.type===s.SEMICOLON&&L(this._flags.mode)?v="SPACE":this._flags.last_token.type===s.STRING?v="NEWLINE":this._flags.last_token.type===s.RESERVED||this._flags.last_token.type===s.WORD||this._flags.last_token.text==="*"&&(u(this._last_last_text,["function","yield"])||this._flags.mode===r.ObjectLiteral&&u(this._last_last_text,["{",","]))?v="SPACE":this._flags.last_token.type===s.START_BLOCK?this._flags.inline_frame?v="SPACE":v="NEWLINE":this._flags.last_token.type===s.END_EXPR&&(this._output.space_before_token=!0,v="NEWLINE"),b(i,_)&&this._flags.last_token.text!==")"&&(this._flags.inline_frame||this._flags.last_token.text==="else"||this._flags.last_token.text==="export"?v="SPACE":v="NEWLINE"),b(i,["else","catch","finally"]))if((!(this._flags.last_token.type===s.END_BLOCK&&this._previous_flags.mode===r.BlockStatement)||this._options.brace_style==="expand"||this._options.brace_style==="end-expand"||this._options.brace_style==="none"&&i.newlines)&&!this._flags.inline_frame)this.print_newline();else{this._output.trim(!0);var N=this._output.current_line;N.last()!=="}"&&this.print_newline(),this._output.space_before_token=!0}else v==="NEWLINE"?b(this._flags.last_token,A)?this._output.space_before_token=!0:this._flags.last_token.text==="declare"&&b(i,["var","let","const"])?this._output.space_before_token=!0:this._flags.last_token.type!==s.END_EXPR?(this._flags.last_token.type!==s.START_EXPR||!b(i,["var","let","const"]))&&this._flags.last_token.text!==":"&&(d(i,"if")&&d(i.previous,"else")?this._output.space_before_token=!0:this.print_newline()):b(i,_)&&this._flags.last_token.text!==")"&&this.print_newline():this._flags.multiline_frame&&x(this._flags.mode)&&this._flags.last_token.text===","&&this._last_last_text==="}"?this.print_newline():v==="SPACE"&&(this._output.space_before_token=!0);i.previous&&(i.previous.type===s.WORD||i.previous.type===s.RESERVED)&&(this._output.space_before_token=!0),this.print_token(i),this._flags.last_word=i.text,i.type===s.RESERVED&&(i.text==="do"?this._flags.do_block=!0:i.text==="if"?this._flags.if_block=!0:i.text==="import"?this._flags.import_block=!0:this._flags.import_block&&d(i,"from")&&(this._flags.import_block=!1))},h.prototype.handle_semicolon=function(i){this.start_of_statement(i)?this._output.space_before_token=!1:this.handle_whitespace_and_comments(i);for(var g=this._tokens.peek();this._flags.mode===r.Statement&&!(this._flags.if_block&&d(g,"else"))&&!this._flags.do_block;)this.restore_mode();this._flags.import_block&&(this._flags.import_block=!1),this.print_token(i)},h.prototype.handle_string=function(i){i.text.startsWith("`")&&i.newlines===0&&i.whitespace_before===""&&(i.previous.text===")"||this._flags.last_token.type===s.WORD)||(this.start_of_statement(i)?this._output.space_before_token=!0:(this.handle_whitespace_and_comments(i),this._flags.last_token.type===s.RESERVED||this._flags.last_token.type===s.WORD||this._flags.inline_frame?this._output.space_before_token=!0:this._flags.last_token.type===s.COMMA||this._flags.last_token.type===s.START_EXPR||this._flags.last_token.type===s.EQUALS||this._flags.last_token.type===s.OPERATOR?this.start_of_object_property()||this.allow_wrap_or_preserved_newline(i):i.text.startsWith("`")&&this._flags.last_token.type===s.END_EXPR&&(i.previous.text==="]"||i.previous.text===")")&&i.newlines===0?this._output.space_before_token=!0:this.print_newline())),this.print_token(i)},h.prototype.handle_equals=function(i){this.start_of_statement(i)||this.handle_whitespace_and_comments(i),this._flags.declaration_statement&&(this._flags.declaration_assignment=!0),this._output.space_before_token=!0,this.print_token(i),this._output.space_before_token=!0},h.prototype.handle_comma=function(i){this.handle_whitespace_and_comments(i,!0),this.print_token(i),this._output.space_before_token=!0,this._flags.declaration_statement?(L(this._flags.parent.mode)&&(this._flags.declaration_assignment=!1),this._flags.declaration_assignment?(this._flags.declaration_assignment=!1,this.print_newline(!1,!0)):this._options.comma_first&&this.allow_wrap_or_preserved_newline(i)):this._flags.mode===r.ObjectLiteral||this._flags.mode===r.Statement&&this._flags.parent.mode===r.ObjectLiteral?(this._flags.mode===r.Statement&&this.restore_mode(),this._flags.inline_frame||this.print_newline()):this._options.comma_first&&this.allow_wrap_or_preserved_newline(i)},h.prototype.handle_operator=function(i){var g=i.text==="*"&&(b(this._flags.last_token,["function","yield"])||u(this._flags.last_token.type,[s.START_BLOCK,s.COMMA,s.END_BLOCK,s.SEMICOLON])),v=u(i.text,["-","+"])&&(u(this._flags.last_token.type,[s.START_BLOCK,s.START_EXPR,s.EQUALS,s.OPERATOR])||u(this._flags.last_token.text,_)||this._flags.last_token.text===",");if(!this.start_of_statement(i)){var N=!g;this.handle_whitespace_and_comments(i,N)}if(i.text==="*"&&this._flags.last_token.type===s.DOT){this.print_token(i);return}if(i.text==="::"){this.print_token(i);return}if(u(i.text,["-","+"])&&this.start_of_object_property()){this.print_token(i);return}if(this._flags.last_token.type===s.OPERATOR&&u(this._options.operator_position,p)&&this.allow_wrap_or_preserved_newline(i),i.text===":"&&this._flags.in_case){this.print_token(i),this._flags.in_case=!1,this._flags.case_body=!0,this._tokens.peek().type!==s.START_BLOCK?(this.indent(),this.print_newline(),this._flags.case_block=!1):(this._flags.case_block=!0,this._output.space_before_token=!0);return}var P=!0,O=!0,$=!1;if(i.text===":"?this._flags.ternary_depth===0?P=!1:(this._flags.ternary_depth-=1,$=!0):i.text==="?"&&(this._flags.ternary_depth+=1),!v&&!g&&this._options.preserve_newlines&&u(i.text,l)){var j=i.text===":",K=j&&$,F=j&&!$;switch(this._options.operator_position){case M.before_newline:this._output.space_before_token=!F,this.print_token(i),(!j||K)&&this.allow_wrap_or_preserved_newline(i),this._output.space_before_token=!0;return;case M.after_newline:this._output.space_before_token=!0,!j||K?this._tokens.peek().newlines?this.print_newline(!1,!0):this.allow_wrap_or_preserved_newline(i):this._output.space_before_token=!1,this.print_token(i),this._output.space_before_token=!0;return;case M.preserve_newline:F||this.allow_wrap_or_preserved_newline(i),P=!(this._output.just_added_newline()||F),this._output.space_before_token=P,this.print_token(i),this._output.space_before_token=!0;return}}if(g){this.allow_wrap_or_preserved_newline(i),P=!1;var J=this._tokens.peek();O=J&&u(J.type,[s.WORD,s.RESERVED])}else if(i.text==="...")this.allow_wrap_or_preserved_newline(i),P=this._flags.last_token.type===s.START_BLOCK,O=!1;else if(u(i.text,["--","++","!","~"])||v){if((this._flags.last_token.type===s.COMMA||this._flags.last_token.type===s.START_EXPR)&&this.allow_wrap_or_preserved_newline(i),P=!1,O=!1,i.newlines&&(i.text==="--"||i.text==="++"||i.text==="~")){var H=b(this._flags.last_token,A)&&i.newlines;H&&(this._previous_flags.if_block||this._previous_flags.else_block)&&this.restore_mode(),this.print_newline(H,!0)}this._flags.last_token.text===";"&&L(this._flags.mode)&&(P=!0),this._flags.last_token.type===s.RESERVED?P=!0:this._flags.last_token.type===s.END_EXPR?P=!(this._flags.last_token.text==="]"&&(i.text==="--"||i.text==="++")):this._flags.last_token.type===s.OPERATOR&&(P=u(i.text,["--","-","++","+"])&&u(this._flags.last_token.text,["--","-","++","+"]),u(i.text,["+","-"])&&u(this._flags.last_token.text,["--","++"])&&(O=!0)),(this._flags.mode===r.BlockStatement&&!this._flags.inline_frame||this._flags.mode===r.Statement)&&(this._flags.last_token.text==="{"||this._flags.last_token.text===";")&&this.print_newline()}this._output.space_before_token=this._output.space_before_token||P,this.print_token(i),this._output.space_before_token=O},h.prototype.handle_block_comment=function(i,g){if(this._output.raw){this._output.add_raw_token(i),i.directives&&i.directives.preserve==="end"&&(this._output.raw=this._options.test_output_raw);return}if(i.directives){this.print_newline(!1,g),this.print_token(i),i.directives.preserve==="start"&&(this._output.raw=!0),this.print_newline(!1,!0);return}if(!a.newline.test(i.text)&&!i.newlines){this._output.space_before_token=!0,this.print_token(i),this._output.space_before_token=!0;return}else this.print_block_commment(i,g)},h.prototype.print_block_commment=function(i,g){var v=C(i.text),N,P=!1,O=!1,$=i.whitespace_before,j=$.length;if(this.print_newline(!1,g),this.print_token_line_indentation(i),this._output.add_token(v[0]),this.print_newline(!1,g),v.length>1){for(v=v.slice(1),P=c(v,"*"),O=y(v,$),P&&(this._flags.alignment=1),N=0;N<v.length;N++)P?(this.print_token_line_indentation(i),this._output.add_token(w(v[N]))):O&&v[N]?(this.print_token_line_indentation(i),this._output.add_token(v[N].substring(j))):(this._output.current_line.set_indent(-1),this._output.add_token(v[N])),this.print_newline(!1,g);this._flags.alignment=0}},h.prototype.handle_comment=function(i,g){i.newlines?this.print_newline(!1,g):this._output.trim(!0),this._output.space_before_token=!0,this.print_token(i),this.print_newline(!1,g)},h.prototype.handle_dot=function(i){this.start_of_statement(i)||this.handle_whitespace_and_comments(i,!0),this._flags.last_token.text.match("^[0-9]+$")&&(this._output.space_before_token=!0),b(this._flags.last_token,A)?this._output.space_before_token=!1:this.allow_wrap_or_preserved_newline(i,this._flags.last_token.text===")"&&this._options.break_chained_methods),this._options.unindent_chained_methods&&this._output.just_added_newline()&&this.deindent(),this.print_token(i)},h.prototype.handle_unknown=function(i,g){this.print_token(i),i.text[i.text.length-1]===`
`&&this.print_newline(!1,g)},h.prototype.handle_eof=function(i){for(;this._flags.mode===r.Statement;)this.restore_mode();this.handle_whitespace_and_comments(i)},bt.Beautifier=h,bt}var te;function Xe(){if(te)return rt.exports;te=1;var t=Ve().Beautifier,o=me().Options;function a(e,n){var _=new t(e,n);return _.beautify()}return rt.exports=a,rt.exports.defaultOptions=function(){return new o},rt.exports}var ut={exports:{}},St={},At={},ee;function ve(){if(ee)return At;ee=1;var t=Pt().Options;function o(a){t.call(this,a,"css"),this.selector_separator_newline=this._get_boolean("selector_separator_newline",!0),this.newline_between_rules=this._get_boolean("newline_between_rules",!0);var e=this._get_boolean("space_around_selector_separator");this.space_around_combinator=this._get_boolean("space_around_combinator")||e;var n=this._get_selection_list("brace_style",["collapse","expand","end-expand","none","preserve-inline"]);this.brace_style="collapse";for(var _=0;_<n.length;_++)n[_]!=="expand"?this.brace_style="collapse":this.brace_style=n[_]}return o.prototype=new t,At.Options=o,At}var ie;function Ge(){if(ie)return St;ie=1;var t=ve().Options,o=Mt().Output,a=Dt().InputScanner,e=It().Directives,n=new e(/\/\*/,/\*\//),_=/\r\n|[\r\n]/,l=/\r\n|[\r\n]/g,s=/\s/,u=/(?:\s|\n)+/g,w=/\/\*(?:[\s\S]*?)((?:\*\/)|$)/g,m=/\/\/(?:[^\n\r\u2028\u2029]*)/g;function d(b,A){this._source_text=b||"",this._options=new t(A),this._ch=null,this._input=null,this.NESTED_AT_RULE={page:!0,"font-face":!0,keyframes:!0,media:!0,supports:!0,document:!0},this.CONDITIONAL_GROUP_RULE={media:!0,supports:!0,document:!0},this.NON_SEMICOLON_NEWLINE_PROPERTY=["grid-template-areas","grid-template"]}return d.prototype.eatString=function(b){var A="";for(this._ch=this._input.next();this._ch;){if(A+=this._ch,this._ch==="\\")A+=this._input.next();else if(b.indexOf(this._ch)!==-1||this._ch===`
`)break;this._ch=this._input.next()}return A},d.prototype.eatWhitespace=function(b){for(var A=s.test(this._input.peek()),T=0;s.test(this._input.peek());)this._ch=this._input.next(),b&&this._ch===`
`&&(T===0||T<this._options.max_preserve_newlines)&&(T++,this._output.add_new_line(!0));return A},d.prototype.foundNestedPseudoClass=function(){for(var b=0,A=1,T=this._input.peek(A);T;){if(T==="{")return!0;if(T==="(")b+=1;else if(T===")"){if(b===0)return!1;b-=1}else if(T===";"||T==="}")return!1;A++,T=this._input.peek(A)}return!1},d.prototype.print_string=function(b){this._output.set_indent(this._indentLevel),this._output.non_breaking_space=!0,this._output.add_token(b)},d.prototype.preserveSingleSpace=function(b){b&&(this._output.space_before_token=!0)},d.prototype.indent=function(){this._indentLevel++},d.prototype.outdent=function(){this._indentLevel>0&&this._indentLevel--},d.prototype.beautify=function(){if(this._options.disabled)return this._source_text;var b=this._source_text,A=this._options.eol;A==="auto"&&(A=`
`,b&&_.test(b||"")&&(A=b.match(_)[0])),b=b.replace(l,`
`);var T=b.match(/^[\t ]*/)[0];this._output=new o(this._options,T),this._input=new a(b),this._indentLevel=0,this._nestedLevel=0,this._ch=null;for(var M=0,p=!1,r=!1,f=!1,C=!1,x=!1,L=this._ch,c=!1,y,h,E;y=this._input.read(u),h=y!=="",E=L,this._ch=this._input.next(),this._ch==="\\"&&this._input.hasNext()&&(this._ch+=this._input.next()),L=this._ch,this._ch;)if(this._ch==="/"&&this._input.peek()==="*"){this._output.add_new_line(),this._input.back();var i=this._input.read(w),g=n.get_directives(i);g&&g.ignore==="start"&&(i+=n.readIgnored(this._input)),this.print_string(i),this.eatWhitespace(!0),this._output.add_new_line()}else if(this._ch==="/"&&this._input.peek()==="/")this._output.space_before_token=!0,this._input.back(),this.print_string(this._input.read(m)),this.eatWhitespace(!0);else if(this._ch==="$"){this.preserveSingleSpace(h),this.print_string(this._ch);var v=this._input.peekUntilAfter(/[: ,;{}()[\]\/='"]/g);v.match(/[ :]$/)&&(v=this.eatString(": ").replace(/\s+$/,""),this.print_string(v),this._output.space_before_token=!0),M===0&&v.indexOf(":")!==-1&&(r=!0,this.indent())}else if(this._ch==="@")if(this.preserveSingleSpace(h),this._input.peek()==="{")this.print_string(this._ch+this.eatString("}"));else{this.print_string(this._ch);var N=this._input.peekUntilAfter(/[: ,;{}()[\]\/='"]/g);N.match(/[ :]$/)&&(N=this.eatString(": ").replace(/\s+$/,""),this.print_string(N),this._output.space_before_token=!0),M===0&&N.indexOf(":")!==-1?(r=!0,this.indent()):N in this.NESTED_AT_RULE?(this._nestedLevel+=1,N in this.CONDITIONAL_GROUP_RULE&&(f=!0)):M===0&&!r&&(C=!0)}else if(this._ch==="#"&&this._input.peek()==="{")this.preserveSingleSpace(h),this.print_string(this._ch+this.eatString("}"));else if(this._ch==="{")r&&(r=!1,this.outdent()),C=!1,f?(f=!1,p=this._indentLevel>=this._nestedLevel):p=this._indentLevel>=this._nestedLevel-1,this._options.newline_between_rules&&p&&this._output.previous_line&&this._output.previous_line.item(-1)!=="{"&&this._output.ensure_empty_line_above("/",","),this._output.space_before_token=!0,this._options.brace_style==="expand"?(this._output.add_new_line(),this.print_string(this._ch),this.indent(),this._output.set_indent(this._indentLevel)):(E==="("?this._output.space_before_token=!1:E!==","&&this.indent(),this.print_string(this._ch)),this.eatWhitespace(!0),this._output.add_new_line();else if(this._ch==="}")this.outdent(),this._output.add_new_line(),E==="{"&&this._output.trim(!0),r&&(this.outdent(),r=!1),this.print_string(this._ch),p=!1,this._nestedLevel&&this._nestedLevel--,this.eatWhitespace(!0),this._output.add_new_line(),this._options.newline_between_rules&&!this._output.just_added_blankline()&&this._input.peek()!=="}"&&this._output.add_new_line(!0),this._input.peek()===")"&&(this._output.trim(!0),this._options.brace_style==="expand"&&this._output.add_new_line(!0));else if(this._ch===":"){for(var P=0;P<this.NON_SEMICOLON_NEWLINE_PROPERTY.length;P++)if(this._input.lookBack(this.NON_SEMICOLON_NEWLINE_PROPERTY[P])){c=!0;break}(p||f)&&!(this._input.lookBack("&")||this.foundNestedPseudoClass())&&!this._input.lookBack("(")&&!C&&M===0?(this.print_string(":"),r||(r=!0,this._output.space_before_token=!0,this.eatWhitespace(!0),this.indent())):(this._input.lookBack(" ")&&(this._output.space_before_token=!0),this._input.peek()===":"?(this._ch=this._input.next(),this.print_string("::")):this.print_string(":"))}else if(this._ch==='"'||this._ch==="'"){var O=E==='"'||E==="'";this.preserveSingleSpace(O||h),this.print_string(this._ch+this.eatString(this._ch)),this.eatWhitespace(!0)}else if(this._ch===";")c=!1,M===0?(r&&(this.outdent(),r=!1),C=!1,this.print_string(this._ch),this.eatWhitespace(!0),this._input.peek()!=="/"&&this._output.add_new_line()):(this.print_string(this._ch),this.eatWhitespace(!0),this._output.space_before_token=!0);else if(this._ch==="(")if(this._input.lookBack("url"))this.print_string(this._ch),this.eatWhitespace(),M++,this.indent(),this._ch=this._input.next(),this._ch===")"||this._ch==='"'||this._ch==="'"?this._input.back():this._ch&&(this.print_string(this._ch+this.eatString(")")),M&&(M--,this.outdent()));else{var $=!1;this._input.lookBack("with")&&($=!0),this.preserveSingleSpace(h||$),this.print_string(this._ch),r&&E==="$"&&this._options.selector_separator_newline?(this._output.add_new_line(),x=!0):(this.eatWhitespace(),M++,this.indent())}else if(this._ch===")")M&&(M--,this.outdent()),x&&this._input.peek()===";"&&this._options.selector_separator_newline&&(x=!1,this.outdent(),this._output.add_new_line()),this.print_string(this._ch);else if(this._ch===",")this.print_string(this._ch),this.eatWhitespace(!0),this._options.selector_separator_newline&&(!r||x)&&M===0&&!C?this._output.add_new_line():this._output.space_before_token=!0;else if((this._ch===">"||this._ch==="+"||this._ch==="~")&&!r&&M===0)this._options.space_around_combinator?(this._output.space_before_token=!0,this.print_string(this._ch),this._output.space_before_token=!0):(this.print_string(this._ch),this.eatWhitespace(),this._ch&&s.test(this._ch)&&(this._ch=""));else if(this._ch==="]")this.print_string(this._ch);else if(this._ch==="[")this.preserveSingleSpace(h),this.print_string(this._ch);else if(this._ch==="=")this.eatWhitespace(),this.print_string("="),s.test(this._ch)&&(this._ch="");else if(this._ch==="!"&&!this._input.lookBack("\\"))this._output.space_before_token=!0,this.print_string(this._ch);else{var j=E==='"'||E==="'";this.preserveSingleSpace(j||h),this.print_string(this._ch),!this._output.just_added_newline()&&this._input.peek()===`
`&&c&&this._output.add_new_line()}var K=this._output.get_code(A);return K},St.Beautifier=d,St}var se;function Qe(){if(se)return ut.exports;se=1;var t=Ge().Beautifier,o=ve().Options;function a(e,n){var _=new t(e,n);return _.beautify()}return ut.exports=a,ut.exports.defaultOptions=function(){return new o},ut.exports}var lt={exports:{}},Nt={},Ct={},ne;function ye(){if(ne)return Ct;ne=1;var t=Pt().Options;function o(a){t.call(this,a,"html"),this.templating.length===1&&this.templating[0]==="auto"&&(this.templating=["django","erb","handlebars","php"]),this.indent_inner_html=this._get_boolean("indent_inner_html"),this.indent_body_inner_html=this._get_boolean("indent_body_inner_html",!0),this.indent_head_inner_html=this._get_boolean("indent_head_inner_html",!0),this.indent_handlebars=this._get_boolean("indent_handlebars",!0),this.wrap_attributes=this._get_selection("wrap_attributes",["auto","force","force-aligned","force-expand-multiline","aligned-multiple","preserve","preserve-aligned"]),this.wrap_attributes_min_attrs=this._get_number("wrap_attributes_min_attrs",2),this.wrap_attributes_indent_size=this._get_number("wrap_attributes_indent_size",this.indent_size),this.extra_liners=this._get_array("extra_liners",["head","body","/html"]),this.inline=this._get_array("inline",["a","abbr","area","audio","b","bdi","bdo","br","button","canvas","cite","code","data","datalist","del","dfn","em","embed","i","iframe","img","input","ins","kbd","keygen","label","map","mark","math","meter","noscript","object","output","progress","q","ruby","s","samp","select","small","span","strong","sub","sup","svg","template","textarea","time","u","var","video","wbr","text","acronym","big","strike","tt"]),this.inline_custom_elements=this._get_boolean("inline_custom_elements",!0),this.void_elements=this._get_array("void_elements",["area","base","br","col","embed","hr","img","input","keygen","link","menuitem","meta","param","source","track","wbr","!doctype","?xml","basefont","isindex"]),this.unformatted=this._get_array("unformatted",[]),this.content_unformatted=this._get_array("content_unformatted",["pre","textarea"]),this.unformatted_content_delimiter=this._get_characters("unformatted_content_delimiter"),this.indent_scripts=this._get_selection("indent_scripts",["normal","keep","separate"])}return o.prototype=new t,Ct.Options=o,Ct}var pt={},ae;function re(){if(ae)return pt;ae=1;var t=ft().Tokenizer,o=ft().TOKEN,a=It().Directives,e=be().TemplatablePattern,n=dt().Pattern,_={TAG_OPEN:"TK_TAG_OPEN",TAG_CLOSE:"TK_TAG_CLOSE",ATTRIBUTE:"TK_ATTRIBUTE",EQUALS:"TK_EQUALS",VALUE:"TK_VALUE",COMMENT:"TK_COMMENT",TEXT:"TK_TEXT",UNKNOWN:"TK_UNKNOWN",START:o.START,RAW:o.RAW,EOF:o.EOF},l=new a(/<\!--/,/-->/),s=function(u,w){t.call(this,u,w),this._current_tag_name="";var m=new e(this._input).read_options(this._options),d=new n(this._input);if(this.__patterns={word:m.until(/[\n\r\t <]/),single_quote:m.until_after(/'/),double_quote:m.until_after(/"/),attribute:m.until(/[\n\r\t =>]|\/>/),element_name:m.until(/[\n\r\t >\/]/),handlebars_comment:d.starting_with(/{{!--/).until_after(/--}}/),handlebars:d.starting_with(/{{/).until_after(/}}/),handlebars_open:d.until(/[\n\r\t }]/),handlebars_raw_close:d.until(/}}/),comment:d.starting_with(/<!--/).until_after(/-->/),cdata:d.starting_with(/<!\[CDATA\[/).until_after(/]]>/),conditional_comment:d.starting_with(/<!\[/).until_after(/]>/),processing:d.starting_with(/<\?/).until_after(/\?>/)},this._options.indent_handlebars&&(this.__patterns.word=this.__patterns.word.exclude("handlebars")),this._unformatted_content_delimiter=null,this._options.unformatted_content_delimiter){var b=this._input.get_literal_regexp(this._options.unformatted_content_delimiter);this.__patterns.unformatted_content_delimiter=d.matching(b).until_after(b)}};return s.prototype=new t,s.prototype._is_comment=function(u){return!1},s.prototype._is_opening=function(u){return u.type===_.TAG_OPEN},s.prototype._is_closing=function(u,w){return u.type===_.TAG_CLOSE&&w&&((u.text===">"||u.text==="/>")&&w.text[0]==="<"||u.text==="}}"&&w.text[0]==="{"&&w.text[1]==="{")},s.prototype._reset=function(){this._current_tag_name=""},s.prototype._get_next_token=function(u,w){var m=null;this._readWhitespace();var d=this._input.peek();return d===null?this._create_token(_.EOF,""):(m=m||this._read_open_handlebars(d,w),m=m||this._read_attribute(d,u,w),m=m||this._read_close(d,w),m=m||this._read_raw_content(d,u,w),m=m||this._read_content_word(d),m=m||this._read_comment_or_cdata(d),m=m||this._read_processing(d),m=m||this._read_open(d,w),m=m||this._create_token(_.UNKNOWN,this._input.next()),m)},s.prototype._read_comment_or_cdata=function(u){var w=null,m=null,d=null;if(u==="<"){var b=this._input.peek(1);b==="!"&&(m=this.__patterns.comment.read(),m?(d=l.get_directives(m),d&&d.ignore==="start"&&(m+=l.readIgnored(this._input))):m=this.__patterns.cdata.read()),m&&(w=this._create_token(_.COMMENT,m),w.directives=d)}return w},s.prototype._read_processing=function(u){var w=null,m=null,d=null;if(u==="<"){var b=this._input.peek(1);(b==="!"||b==="?")&&(m=this.__patterns.conditional_comment.read(),m=m||this.__patterns.processing.read()),m&&(w=this._create_token(_.COMMENT,m),w.directives=d)}return w},s.prototype._read_open=function(u,w){var m=null,d=null;return w||u==="<"&&(m=this._input.next(),this._input.peek()==="/"&&(m+=this._input.next()),m+=this.__patterns.element_name.read(),d=this._create_token(_.TAG_OPEN,m)),d},s.prototype._read_open_handlebars=function(u,w){var m=null,d=null;return w||this._options.indent_handlebars&&u==="{"&&this._input.peek(1)==="{"&&(this._input.peek(2)==="!"?(m=this.__patterns.handlebars_comment.read(),m=m||this.__patterns.handlebars.read(),d=this._create_token(_.COMMENT,m)):(m=this.__patterns.handlebars_open.read(),d=this._create_token(_.TAG_OPEN,m))),d},s.prototype._read_close=function(u,w){var m=null,d=null;return w&&(w.text[0]==="<"&&(u===">"||u==="/"&&this._input.peek(1)===">")?(m=this._input.next(),u==="/"&&(m+=this._input.next()),d=this._create_token(_.TAG_CLOSE,m)):w.text[0]==="{"&&u==="}"&&this._input.peek(1)==="}"&&(this._input.next(),this._input.next(),d=this._create_token(_.TAG_CLOSE,"}}"))),d},s.prototype._read_attribute=function(u,w,m){var d=null,b="";if(m&&m.text[0]==="<")if(u==="=")d=this._create_token(_.EQUALS,this._input.next());else if(u==='"'||u==="'"){var A=this._input.next();u==='"'?A+=this.__patterns.double_quote.read():A+=this.__patterns.single_quote.read(),d=this._create_token(_.VALUE,A)}else b=this.__patterns.attribute.read(),b&&(w.type===_.EQUALS?d=this._create_token(_.VALUE,b):d=this._create_token(_.ATTRIBUTE,b));return d},s.prototype._is_content_unformatted=function(u){return this._options.void_elements.indexOf(u)===-1&&(this._options.content_unformatted.indexOf(u)!==-1||this._options.unformatted.indexOf(u)!==-1)},s.prototype._read_raw_content=function(u,w,m){var d="";if(m&&m.text[0]==="{")d=this.__patterns.handlebars_raw_close.read();else if(w.type===_.TAG_CLOSE&&w.opened.text[0]==="<"&&w.text[0]!=="/"){var b=w.opened.text.substr(1).toLowerCase();if(b==="script"||b==="style"){var A=this._read_comment_or_cdata(u);if(A)return A.type=_.TEXT,A;d=this._input.readUntil(new RegExp("</"+b+"[\\n\\r\\t ]*?>","ig"))}else this._is_content_unformatted(b)&&(d=this._input.readUntil(new RegExp("</"+b+"[\\n\\r\\t ]*?>","ig")))}return d?this._create_token(_.TEXT,d):null},s.prototype._read_content_word=function(u){var w="";if(this._options.unformatted_content_delimiter&&u===this._options.unformatted_content_delimiter[0]&&(w=this.__patterns.unformatted_content_delimiter.read()),w||(w=this.__patterns.word.read()),w)return this._create_token(_.TEXT,w)},pt.Tokenizer=s,pt.TOKEN=_,pt}var _e;function Je(){if(_e)return Nt;_e=1;var t=ye().Options,o=Mt().Output,a=re().Tokenizer,e=re().TOKEN,n=/\r\n|[\r\n]/,_=/\r\n|[\r\n]/g,l=function(p,r){this.indent_level=0,this.alignment_size=0,this.max_preserve_newlines=p.max_preserve_newlines,this.preserve_newlines=p.preserve_newlines,this._output=new o(p,r)};l.prototype.current_line_has_match=function(p){return this._output.current_line.has_match(p)},l.prototype.set_space_before_token=function(p,r){this._output.space_before_token=p,this._output.non_breaking_space=r},l.prototype.set_wrap_point=function(){this._output.set_indent(this.indent_level,this.alignment_size),this._output.set_wrap_point()},l.prototype.add_raw_token=function(p){this._output.add_raw_token(p)},l.prototype.print_preserved_newlines=function(p){var r=0;p.type!==e.TEXT&&p.previous.type!==e.TEXT&&(r=p.newlines?1:0),this.preserve_newlines&&(r=p.newlines<this.max_preserve_newlines+1?p.newlines:this.max_preserve_newlines+1);for(var f=0;f<r;f++)this.print_newline(f>0);return r!==0},l.prototype.traverse_whitespace=function(p){return p.whitespace_before||p.newlines?(this.print_preserved_newlines(p)||(this._output.space_before_token=!0),!0):!1},l.prototype.previous_token_wrapped=function(){return this._output.previous_token_wrapped},l.prototype.print_newline=function(p){this._output.add_new_line(p)},l.prototype.print_token=function(p){p.text&&(this._output.set_indent(this.indent_level,this.alignment_size),this._output.add_token(p.text))},l.prototype.indent=function(){this.indent_level++},l.prototype.get_full_indent=function(p){return p=this.indent_level+(p||0),p<1?"":this._output.get_indent_string(p)};var s=function(p){for(var r=null,f=p.next;f.type!==e.EOF&&p.closed!==f;){if(f.type===e.ATTRIBUTE&&f.text==="type"){f.next&&f.next.type===e.EQUALS&&f.next.next&&f.next.next.type===e.VALUE&&(r=f.next.next.text);break}f=f.next}return r},u=function(p,r){var f=null,C=null;return r.closed?(p==="script"?f="text/javascript":p==="style"&&(f="text/css"),f=s(r)||f,f.search("text/css")>-1?C="css":f.search(/module|((text|application|dojo)\/(x-)?(javascript|ecmascript|jscript|livescript|(ld\+)?json|method|aspect))/)>-1?C="javascript":f.search(/(text|application|dojo)\/(x-)?(html)/)>-1?C="html":f.search(/test\/null/)>-1&&(C="null"),C):null};function w(p,r){return r.indexOf(p)!==-1}function m(p,r,f){this.parent=p||null,this.tag=r?r.tag_name:"",this.indent_level=f||0,this.parser_token=r||null}function d(p){this._printer=p,this._current_frame=null}d.prototype.get_parser_token=function(){return this._current_frame?this._current_frame.parser_token:null},d.prototype.record_tag=function(p){var r=new m(this._current_frame,p,this._printer.indent_level);this._current_frame=r},d.prototype._try_pop_frame=function(p){var r=null;return p&&(r=p.parser_token,this._printer.indent_level=p.indent_level,this._current_frame=p.parent),r},d.prototype._get_frame=function(p,r){for(var f=this._current_frame;f&&p.indexOf(f.tag)===-1;){if(r&&r.indexOf(f.tag)!==-1){f=null;break}f=f.parent}return f},d.prototype.try_pop=function(p,r){var f=this._get_frame([p],r);return this._try_pop_frame(f)},d.prototype.indent_to_tag=function(p){var r=this._get_frame(p);r&&(this._printer.indent_level=r.indent_level)};function b(p,r,f,C){this._source_text=p||"",r=r||{},this._js_beautify=f,this._css_beautify=C,this._tag_stack=null;var x=new t(r,"html");this._options=x,this._is_wrap_attributes_force=this._options.wrap_attributes.substr(0,5)==="force",this._is_wrap_attributes_force_expand_multiline=this._options.wrap_attributes==="force-expand-multiline",this._is_wrap_attributes_force_aligned=this._options.wrap_attributes==="force-aligned",this._is_wrap_attributes_aligned_multiple=this._options.wrap_attributes==="aligned-multiple",this._is_wrap_attributes_preserve=this._options.wrap_attributes.substr(0,8)==="preserve",this._is_wrap_attributes_preserve_aligned=this._options.wrap_attributes==="preserve-aligned"}b.prototype.beautify=function(){if(this._options.disabled)return this._source_text;var p=this._source_text,r=this._options.eol;this._options.eol==="auto"&&(r=`
`,p&&n.test(p)&&(r=p.match(n)[0])),p=p.replace(_,`
`);var f=p.match(/^[\t ]*/)[0],C={text:"",type:""},x=new A,L=new l(this._options,f),c=new a(p,this._options).tokenize();this._tag_stack=new d(L);for(var y=null,h=c.next();h.type!==e.EOF;)h.type===e.TAG_OPEN||h.type===e.COMMENT?(y=this._handle_tag_open(L,h,x,C,c),x=y):h.type===e.ATTRIBUTE||h.type===e.EQUALS||h.type===e.VALUE||h.type===e.TEXT&&!x.tag_complete?y=this._handle_inside_tag(L,h,x,C):h.type===e.TAG_CLOSE?y=this._handle_tag_close(L,h,x):h.type===e.TEXT?y=this._handle_text(L,h,x):L.add_raw_token(h),C=y,h=c.next();var E=L._output.get_code(r);return E},b.prototype._handle_tag_close=function(p,r,f){var C={text:r.text,type:r.type};return p.alignment_size=0,f.tag_complete=!0,p.set_space_before_token(r.newlines||r.whitespace_before!=="",!0),f.is_unformatted?p.add_raw_token(r):(f.tag_start_char==="<"&&(p.set_space_before_token(r.text[0]==="/",!0),this._is_wrap_attributes_force_expand_multiline&&f.has_wrapped_attrs&&p.print_newline(!1)),p.print_token(r)),f.indent_content&&!(f.is_unformatted||f.is_content_unformatted)&&(p.indent(),f.indent_content=!1),!f.is_inline_element&&!(f.is_unformatted||f.is_content_unformatted)&&p.set_wrap_point(),C},b.prototype._handle_inside_tag=function(p,r,f,C){var x=f.has_wrapped_attrs,L={text:r.text,type:r.type};return p.set_space_before_token(r.newlines||r.whitespace_before!=="",!0),f.is_unformatted?p.add_raw_token(r):f.tag_start_char==="{"&&r.type===e.TEXT?p.print_preserved_newlines(r)?(r.newlines=0,p.add_raw_token(r)):p.print_token(r):(r.type===e.ATTRIBUTE?p.set_space_before_token(!0):(r.type===e.EQUALS||r.type===e.VALUE&&r.previous.type===e.EQUALS)&&p.set_space_before_token(!1),r.type===e.ATTRIBUTE&&f.tag_start_char==="<"&&((this._is_wrap_attributes_preserve||this._is_wrap_attributes_preserve_aligned)&&(p.traverse_whitespace(r),x=x||r.newlines!==0),this._is_wrap_attributes_force&&f.attr_count>=this._options.wrap_attributes_min_attrs&&(C.type!==e.TAG_OPEN||this._is_wrap_attributes_force_expand_multiline)&&(p.print_newline(!1),x=!0)),p.print_token(r),x=x||p.previous_token_wrapped(),f.has_wrapped_attrs=x),L},b.prototype._handle_text=function(p,r,f){var C={text:r.text,type:"TK_CONTENT"};return f.custom_beautifier_name?this._print_custom_beatifier_text(p,r,f):f.is_unformatted||f.is_content_unformatted?p.add_raw_token(r):(p.traverse_whitespace(r),p.print_token(r)),C},b.prototype._print_custom_beatifier_text=function(p,r,f){var C=this;if(r.text!==""){var x=r.text,L,c=1,y="",h="";f.custom_beautifier_name==="javascript"&&typeof this._js_beautify=="function"?L=this._js_beautify:f.custom_beautifier_name==="css"&&typeof this._css_beautify=="function"?L=this._css_beautify:f.custom_beautifier_name==="html"&&(L=function(P,O){var $=new b(P,O,C._js_beautify,C._css_beautify);return $.beautify()}),this._options.indent_scripts==="keep"?c=0:this._options.indent_scripts==="separate"&&(c=-p.indent_level);var E=p.get_full_indent(c);if(x=x.replace(/\n[ \t]*$/,""),f.custom_beautifier_name!=="html"&&x[0]==="<"&&x.match(/^(<!--|<!\[CDATA\[)/)){var i=/^(<!--[^\n]*|<!\[CDATA\[)(\n?)([ \t\n]*)([\s\S]*)(-->|]]>)$/.exec(x);if(!i){p.add_raw_token(r);return}y=E+i[1]+`
`,x=i[4],i[5]&&(h=E+i[5]),x=x.replace(/\n[ \t]*$/,""),(i[2]||i[3].indexOf(`
`)!==-1)&&(i=i[3].match(/[ \t]+$/),i&&(r.whitespace_before=i[0]))}if(x)if(L){var g=function(){this.eol=`
`};g.prototype=this._options.raw_options;var v=new g;x=L(E+x,v)}else{var N=r.whitespace_before;N&&(x=x.replace(new RegExp(`
(`+N+")?","g"),`
`)),x=E+x.replace(/\n/g,`
`+E)}y&&(x?x=y+x+`
`+h:x=y+h),p.print_newline(!1),x&&(r.text=x,r.whitespace_before="",r.newlines=0,p.add_raw_token(r),p.print_newline(!0))}},b.prototype._handle_tag_open=function(p,r,f,C,x){var L=this._get_tag_open_token(r);if((f.is_unformatted||f.is_content_unformatted)&&!f.is_empty_element&&r.type===e.TAG_OPEN&&!L.is_start_tag?(p.add_raw_token(r),L.start_tag_token=this._tag_stack.try_pop(L.tag_name)):(p.traverse_whitespace(r),this._set_tag_position(p,r,L,f,C),L.is_inline_element||p.set_wrap_point(),p.print_token(r)),L.is_start_tag&&this._is_wrap_attributes_force){var c=0,y;do y=x.peek(c),y.type===e.ATTRIBUTE&&(L.attr_count+=1),c+=1;while(y.type!==e.EOF&&y.type!==e.TAG_CLOSE)}return(this._is_wrap_attributes_force_aligned||this._is_wrap_attributes_aligned_multiple||this._is_wrap_attributes_preserve_aligned)&&(L.alignment_size=r.text.length+1),!L.tag_complete&&!L.is_unformatted&&(p.alignment_size=L.alignment_size),L};var A=function(p,r){if(this.parent=p||null,this.text="",this.type="TK_TAG_OPEN",this.tag_name="",this.is_inline_element=!1,this.is_unformatted=!1,this.is_content_unformatted=!1,this.is_empty_element=!1,this.is_start_tag=!1,this.is_end_tag=!1,this.indent_content=!1,this.multiline_content=!1,this.custom_beautifier_name=null,this.start_tag_token=null,this.attr_count=0,this.has_wrapped_attrs=!1,this.alignment_size=0,this.tag_complete=!1,this.tag_start_char="",this.tag_check="",!r)this.tag_complete=!0;else{var f;this.tag_start_char=r.text[0],this.text=r.text,this.tag_start_char==="<"?(f=r.text.match(/^<([^\s>]*)/),this.tag_check=f?f[1]:""):(f=r.text.match(/^{{~?(?:[\^]|#\*?)?([^\s}]+)/),this.tag_check=f?f[1]:"",(r.text.startsWith("{{#>")||r.text.startsWith("{{~#>"))&&this.tag_check[0]===">"&&(this.tag_check===">"&&r.next!==null?this.tag_check=r.next.text.split(" ")[0]:this.tag_check=r.text.split(">")[1])),this.tag_check=this.tag_check.toLowerCase(),r.type===e.COMMENT&&(this.tag_complete=!0),this.is_start_tag=this.tag_check.charAt(0)!=="/",this.tag_name=this.is_start_tag?this.tag_check:this.tag_check.substr(1),this.is_end_tag=!this.is_start_tag||r.closed&&r.closed.text==="/>";var C=2;this.tag_start_char==="{"&&this.text.length>=3&&this.text.charAt(2)==="~"&&(C=3),this.is_end_tag=this.is_end_tag||this.tag_start_char==="{"&&(this.text.length<3||/[^#\^]/.test(this.text.charAt(C)))}};b.prototype._get_tag_open_token=function(p){var r=new A(this._tag_stack.get_parser_token(),p);return r.alignment_size=this._options.wrap_attributes_indent_size,r.is_end_tag=r.is_end_tag||w(r.tag_check,this._options.void_elements),r.is_empty_element=r.tag_complete||r.is_start_tag&&r.is_end_tag,r.is_unformatted=!r.tag_complete&&w(r.tag_check,this._options.unformatted),r.is_content_unformatted=!r.is_empty_element&&w(r.tag_check,this._options.content_unformatted),r.is_inline_element=w(r.tag_name,this._options.inline)||this._options.inline_custom_elements&&r.tag_name.includes("-")||r.tag_start_char==="{",r},b.prototype._set_tag_position=function(p,r,f,C,x){if(f.is_empty_element||(f.is_end_tag?f.start_tag_token=this._tag_stack.try_pop(f.tag_name):(this._do_optional_end_element(f)&&(f.is_inline_element||p.print_newline(!1)),this._tag_stack.record_tag(f),(f.tag_name==="script"||f.tag_name==="style")&&!(f.is_unformatted||f.is_content_unformatted)&&(f.custom_beautifier_name=u(f.tag_check,r)))),w(f.tag_check,this._options.extra_liners)&&(p.print_newline(!1),p._output.just_added_blankline()||p.print_newline(!0)),f.is_empty_element){if(f.tag_start_char==="{"&&f.tag_check==="else"){this._tag_stack.indent_to_tag(["if","unless","each"]),f.indent_content=!0;var L=p.current_line_has_match(/{{#if/);L||p.print_newline(!1)}f.tag_name==="!--"&&x.type===e.TAG_CLOSE&&C.is_end_tag&&f.text.indexOf(`
`)===-1||(f.is_inline_element||f.is_unformatted||p.print_newline(!1),this._calcluate_parent_multiline(p,f))}else if(f.is_end_tag){var c=!1;c=f.start_tag_token&&f.start_tag_token.multiline_content,c=c||!f.is_inline_element&&!(C.is_inline_element||C.is_unformatted)&&!(x.type===e.TAG_CLOSE&&f.start_tag_token===C)&&x.type!=="TK_CONTENT",(f.is_content_unformatted||f.is_unformatted)&&(c=!1),c&&p.print_newline(!1)}else f.indent_content=!f.custom_beautifier_name,f.tag_start_char==="<"&&(f.tag_name==="html"?f.indent_content=this._options.indent_inner_html:f.tag_name==="head"?f.indent_content=this._options.indent_head_inner_html:f.tag_name==="body"&&(f.indent_content=this._options.indent_body_inner_html)),!(f.is_inline_element||f.is_unformatted)&&(x.type!=="TK_CONTENT"||f.is_content_unformatted)&&p.print_newline(!1),this._calcluate_parent_multiline(p,f)},b.prototype._calcluate_parent_multiline=function(p,r){r.parent&&p._output.just_added_newline()&&!((r.is_inline_element||r.is_unformatted)&&r.parent.is_inline_element)&&(r.parent.multiline_content=!0)};var T=["address","article","aside","blockquote","details","div","dl","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hr","main","menu","nav","ol","p","pre","section","table","ul"],M=["a","audio","del","ins","map","noscript","video"];return b.prototype._do_optional_end_element=function(p){var r=null;if(!(p.is_empty_element||!p.is_start_tag||!p.parent)){if(p.tag_name==="body")r=r||this._tag_stack.try_pop("head");else if(p.tag_name==="li")r=r||this._tag_stack.try_pop("li",["ol","ul","menu"]);else if(p.tag_name==="dd"||p.tag_name==="dt")r=r||this._tag_stack.try_pop("dt",["dl"]),r=r||this._tag_stack.try_pop("dd",["dl"]);else if(p.parent.tag_name==="p"&&T.indexOf(p.tag_name)!==-1){var f=p.parent.parent;(!f||M.indexOf(f.tag_name)===-1)&&(r=r||this._tag_stack.try_pop("p"))}else p.tag_name==="rp"||p.tag_name==="rt"?(r=r||this._tag_stack.try_pop("rt",["ruby","rtc"]),r=r||this._tag_stack.try_pop("rp",["ruby","rtc"])):p.tag_name==="optgroup"?r=r||this._tag_stack.try_pop("optgroup",["select"]):p.tag_name==="option"?r=r||this._tag_stack.try_pop("option",["select","datalist","optgroup"]):p.tag_name==="colgroup"?r=r||this._tag_stack.try_pop("caption",["table"]):p.tag_name==="thead"?(r=r||this._tag_stack.try_pop("caption",["table"]),r=r||this._tag_stack.try_pop("colgroup",["table"])):p.tag_name==="tbody"||p.tag_name==="tfoot"?(r=r||this._tag_stack.try_pop("caption",["table"]),r=r||this._tag_stack.try_pop("colgroup",["table"]),r=r||this._tag_stack.try_pop("thead",["table"]),r=r||this._tag_stack.try_pop("tbody",["table"])):p.tag_name==="tr"?(r=r||this._tag_stack.try_pop("caption",["table"]),r=r||this._tag_stack.try_pop("colgroup",["table"]),r=r||this._tag_stack.try_pop("tr",["table","thead","tbody","tfoot"])):(p.tag_name==="th"||p.tag_name==="td")&&(r=r||this._tag_stack.try_pop("td",["table","thead","tbody","tfoot","tr"]),r=r||this._tag_stack.try_pop("th",["table","thead","tbody","tfoot","tr"]));return p.parent=this._tag_stack.get_parser_token(),r}},Nt.Beautifier=b,Nt}var oe;function He(){if(oe)return lt.exports;oe=1;var t=Je().Beautifier,o=ye().Options;function a(e,n,_,l){var s=new t(e,n,_,l);return s.beautify()}return lt.exports=a,lt.exports.defaultOptions=function(){return new o},lt.exports}var ue;function Ye(){if(ue)return et;ue=1;var t=Xe(),o=Qe(),a=He();function e(n,_,l,s){return l=l||t,s=s||o,a(n,_,l,s)}return e.defaultOptions=a.defaultOptions,et.js=t,et.css=o,et.html=e,et}(function(t){function o(a,e,n){var _=function(l,s){return a.js_beautify(l,s)};return _.js=a.js_beautify,_.css=e.css_beautify,_.html=n.html_beautify,_.js_beautify=a.js_beautify,_.css_beautify=e.css_beautify,_.html_beautify=n.html_beautify,_}(function(a){var e=Ye();e.js_beautify=e.js,e.css_beautify=e.css,e.html_beautify=e.html,a.exports=o(e,e,e)})(t)})(ce);var Ze=ce.exports;const ti=he(Ze),ei="/assets/logo-BjsPc2xl.png",Lt=[{layout:"colFormItem",tagIcon:"input",label:"手机号",vModel:"mobile",formId:6,tag:"el-input",placeholder:"请输入手机号",defaultValue:"",span:24,style:{width:"100%"},clearable:!0,prepend:"",append:"","prefix-icon":"Cellphone","suffix-icon":"",maxlength:11,"show-word-limit":!0,readonly:!1,disabled:!1,required:!0,changeTag:!0,regList:[{pattern:"/^1(3|4|5|7|8|9)\\d{9}$/",message:"手机号格式错误"}]}];let ct,gt;function ii(t){return`<el-dialog v-model="dialogVisible"  @open="onOpen" @close="onClose" title="Dialog Titile">
    ${t}
    <template #footer>
      <el-button @click="close">取消</el-button>
	  <el-button type="primary" @click="handelConfirm">确定</el-button>
    </template>
  </el-dialog>`}function si(t){return`<template>
    <div class="app-container">
      ${t}
    </div>
  </template>`}function ni(t){return`<script setup>
    ${t}
  <\/script>`}function ai(t){return`<style>
    ${t}
  </style>`}function ri(t,o,a){let e="";t.labelPosition!=="right"&&(e=`label-position="${t.labelPosition}"`);const n=t.disabled?`:disabled="${t.disabled}"`:"";let _=`<el-form ref="${t.formRef}" :model="${t.formModel}" :rules="${t.formRules}" size="${t.size}" ${n} label-width="${t.labelWidth}px" ${e}>
      ${o}
      ${_i(t,a)}
    </el-form>`;return gt&&(_=`<el-row :gutter="${t.gutter}">
        ${_}
      </el-row>`),_}function _i(t,o){let a="";return t.formBtns&&o==="file"&&(a=`<el-form-item>
          <el-button type="primary" @click="submitForm">提交</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>`,gt&&(a=`<el-col :span="24">
          ${a}
        </el-col>`)),a}function le(t,o){return gt||t.span!==24?`<el-col :span="${t.span}">
      ${o}
    </el-col>`:o}const we={colFormItem(t){let o="";t.labelWidth&&t.labelWidth!==ct.labelWidth&&(o=`label-width="${t.labelWidth}px"`);const a=!ht[t.tag]&&t.required?"required":"",e=pe[t.tag]?pe[t.tag](t):null;let n=`<el-form-item ${o} label="${t.label}" prop="${t.vModel}" ${a}>
        ${e}
      </el-form-item>`;return n=le(t,n),n},rowFormItem(t){const o=t.type==="default"?"":`type="${t.type}"`,a=t.type==="default"?"":`justify="${t.justify}"`,e=t.type==="default"?"":`align="${t.align}"`,n=t.gutter?`gutter="${t.gutter}"`:"",_=t.children.map(s=>we[s.layout](s));let l=`<el-row ${o} ${a} ${e} ${n}>
      ${_.join(`
`)}
    </el-row>`;return l=le(t,l),l}},pe={"el-button":t=>{const{disabled:o}=q(t),a=t.type?`type="${t.type}"`:"",e=t.icon?`icon="${t.icon}"`:"",n=t.size?`size="${t.size}"`:"";let _=oi(t);return _&&(_=`
${_}
`),`<${t.tag} ${a} ${e} ${n} ${o}>${_}</${t.tag}>`},"el-input":t=>{const{disabled:o,vModel:a,clearable:e,placeholder:n,width:_}=q(t),l=t.maxlength?`:maxlength="${t.maxlength}"`:"",s=t["show-word-limit"]?"show-word-limit":"",u=t.readonly?"readonly":"",w=t["prefix-icon"]?`prefix-icon='${t["prefix-icon"]}'`:"",m=t["suffix-icon"]?`suffix-icon='${t["suffix-icon"]}'`:"",d=t["show-password"]?"show-password":"",b=t.type?`type="${t.type}"`:"",A=t.autosize&&t.autosize.minRows?`:autosize="{minRows: ${t.autosize.minRows}, maxRows: ${t.autosize.maxRows}}"`:"";let T=ui(t);return T&&(T=`
${T}
`),`<${t.tag} ${a} ${b} ${n} ${l} ${s} ${u} ${o} ${e} ${w} ${m} ${d} ${A} ${_}>${T}</${t.tag}>`},"el-input-number":t=>{const{disabled:o,vModel:a,placeholder:e}=q(t),n=t["controls-position"]?`controls-position=${t["controls-position"]}`:"",_=t.min?`:min='${t.min}'`:"",l=t.max?`:max='${t.max}'`:"",s=t.step?`:step='${t.step}'`:"",u=t["step-strictly"]?"step-strictly":"",w=t.precision?`:precision='${t.precision}'`:"";return`<${t.tag} ${a} ${e} ${s} ${u} ${w} ${n} ${_} ${l} ${o}></${t.tag}>`},"el-select":t=>{const{disabled:o,vModel:a,clearable:e,placeholder:n,width:_}=q(t),l=t.filterable?"filterable":"",s=t.multiple?"multiple":"";let u=li(t);return u&&(u=`
${u}
`),`<${t.tag} ${a} ${n} ${o} ${s} ${l} ${e} ${_}>${u}</${t.tag}>`},"el-radio-group":t=>{const{disabled:o,vModel:a}=q(t),e=`size="${t.size}"`;let n=pi(t);return n&&(n=`
${n}
`),`<${t.tag} ${a} ${e} ${o}>${n}</${t.tag}>`},"el-checkbox-group":t=>{const{disabled:o,vModel:a}=q(t),e=`size="${t.size}"`,n=t.min?`:min="${t.min}"`:"",_=t.max?`:max="${t.max}"`:"";let l=hi(t);return l&&(l=`
${l}
`),`<${t.tag} ${a} ${n} ${_} ${e} ${o}>${l}</${t.tag}>`},"el-switch":t=>{const{disabled:o,vModel:a}=q(t),e=t["active-text"]?`active-text="${t["active-text"]}"`:"",n=t["inactive-text"]?`inactive-text="${t["inactive-text"]}"`:"",_=t["active-color"]?`active-color="${t["active-color"]}"`:"",l=t["inactive-color"]?`inactive-color="${t["inactive-color"]}"`:"",s=t["active-value"]!==!0?`:active-value='${JSON.stringify(t["active-value"])}'`:"",u=t["inactive-value"]!==!1?`:inactive-value='${JSON.stringify(t["inactive-value"])}'`:"";return`<${t.tag} ${a} ${e} ${n} ${_} ${l} ${s} ${u} ${o}></${t.tag}>`},"el-cascader":t=>{const{disabled:o,vModel:a,clearable:e,placeholder:n,width:_}=q(t),l=t.options?`:options="${t.vModel}Options"`:"",s=t.props?`:props="${t.vModel}Props"`:"",u=t["show-all-levels"]?"":':show-all-levels="false"',w=t.filterable?"filterable":"",m=t.separator==="/"?"":`separator="${t.separator}"`;return`<${t.tag} ${a} ${l} ${s} ${_} ${u} ${n} ${m} ${w} ${e} ${o}></${t.tag}>`},"el-slider":t=>{const{disabled:o,vModel:a}=q(t),e=t.min?`:min='${t.min}'`:"",n=t.max?`:max='${t.max}'`:"",_=t.step?`:step='${t.step}'`:"",l=t.range?"range":"",s=t["show-stops"]?`:show-stops="${t["show-stops"]}"`:"";return`<${t.tag} ${e} ${n} ${_} ${a} ${l} ${s} ${o}></${t.tag}>`},"el-time-picker":t=>{const{disabled:o,vModel:a,clearable:e,placeholder:n,width:_}=q(t),l=t["start-placeholder"]?`start-placeholder="${t["start-placeholder"]}"`:"",s=t["end-placeholder"]?`end-placeholder="${t["end-placeholder"]}"`:"",u=t["range-separator"]?`range-separator="${t["range-separator"]}"`:"",w=t["is-range"]?"is-range":"",m=t.format?`format="${t.format}"`:"",d=t["value-format"]?`value-format="${t["value-format"]}"`:"",b=t["picker-options"]?`:picker-options='${JSON.stringify(t["picker-options"])}'`:"";return`<${t.tag} ${a} ${w} ${m} ${d} ${b} ${_} ${n} ${l} ${s} ${u} ${e} ${o}></${t.tag}>`},"el-date-picker":t=>{const{disabled:o,vModel:a,clearable:e,placeholder:n,width:_}=q(t),l=t["start-placeholder"]?`start-placeholder="${t["start-placeholder"]}"`:"",s=t["end-placeholder"]?`end-placeholder="${t["end-placeholder"]}"`:"",u=t["range-separator"]?`range-separator="${t["range-separator"]}"`:"",w=t.format?`format="${t.format}"`:"",m=t["value-format"]?`value-format="${t["value-format"]}"`:"",d=t.type==="date"?"":`type="${t.type}"`,b=t.readonly?"readonly":"";return`<${t.tag} ${d} ${a} ${w} ${m} ${_} ${n} ${l} ${s} ${u} ${e} ${b} ${o}></${t.tag}>`},"el-rate":t=>{const{disabled:o,vModel:a}=q(t);t.max&&`${t.max}`;const e=t["allow-half"]?"allow-half":"",n=t["show-text"]?"show-text":"",_=t["show-score"]?"show-score":"";return`<${t.tag} ${a} ${e} ${n} ${_} ${o}></${t.tag}>`},"el-color-picker":t=>{const{disabled:o,vModel:a}=q(t),e=`size="${t.size}"`,n=t["show-alpha"]?"show-alpha":"",_=t["color-format"]?`color-format="${t["color-format"]}"`:"";return`<${t.tag} ${a} ${e} ${n} ${_} ${o}></${t.tag}>`},"el-upload":t=>{const o=t.disabled?":disabled='true'":"",a=t.action?`:action="${t.vModel}Action"`:"",e=t.multiple?"multiple":"",n=t["list-type"]!=="text"?`list-type="${t["list-type"]}"`:"",_=t.accept?`accept="${t.accept}"`:"",l=t.name!=="file"?`name="${t.name}"`:"",s=t["auto-upload"]===!1?':auto-upload="false"':"",u=`:before-upload="${t.vModel}BeforeUpload"`,w=`:file-list="${t.vModel}fileList"`,m=`ref="${t.vModel}"`;let d=fi(t);return d&&(d=`
${d}
`),`<${t.tag} ${m} ${w} ${a} ${s} ${e} ${u} ${n} ${_} ${l} ${o}>${d}</${t.tag}>`}};function q(t){return{vModel:`v-model="${ct.formModel}.${t.vModel}"`,clearable:t.clearable?"clearable":"",placeholder:t.placeholder?`placeholder="${t.placeholder}"`:"",width:t.style&&t.style.width?`:style="{width: '100%'}"`:"",disabled:t.disabled?":disabled='true'":""}}function oi(t){const o=[];return t.default&&o.push(t.default),o.join(`
`)}function ui(t){const o=[];return t.prepend&&o.push(`<template slot="prepend">${t.prepend}</template>`),t.append&&o.push(`<template slot="append">${t.append}</template>`),o.join(`
`)}function li(t){const o=[];return t.options&&t.options.length&&o.push(`<el-option v-for="(item, index) in ${t.vModel}Options" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>`),o.join(`
`)}function pi(t){const o=[];if(t.options&&t.options.length){const a=t.optionType==="button"?"el-radio-button":"el-radio",e=t.border?"border":"";o.push(`<${a} v-for="(item, index) in ${t.vModel}Options" :key="index" :label="item.value" :disabled="item.disabled" ${e}>{{item.label}}</${a}>`)}return o.join(`
`)}function hi(t){const o=[];if(t.options&&t.options.length){const a=t.optionType==="button"?"el-checkbox-button":"el-checkbox",e=t.border?"border":"";o.push(`<${a} v-for="(item, index) in ${t.vModel}Options" :key="index" :label="item.value" :disabled="item.disabled" ${e}>{{item.label}}</${a}>`)}return o.join(`
`)}function fi(t){const o=[];return t["list-type"]==="picture-card"?o.push('<i class="el-icon-plus"></i>'):o.push(`<el-button size="small" type="primary" icon="el-icon-upload">${t.buttonText}</el-button>`),t.showTip&&o.push(`<div slot="tip" class="el-upload__tip">只能上传不超过 ${t.fileSize}${t.sizeUnit} 的${t.accept}文件</div>`),o.join(`
`)}function ci(t,o){const a=[];ct=t,gt=t.fields.some(_=>_.span!==24),t.fields.forEach(_=>{a.push(we[_.layout](_))});const e=a.join(`
`);let n=ri(t,e,o);return o==="dialog"&&(n=ii(n)),ct=null,n}const di={KB:"1024",MB:"1024 / 1024",GB:"1024 / 1024 / 1024"};function gi(t,o){t=JSON.parse(JSON.stringify(t));const a=[],e=[],n=[],_=[],l=[],s=[];return t.fields.forEach(w=>{xe(w,a,e,n,l,_,s)}),Oi(t,o,a.join(`
`),e.join(`
`),n.join(`
`),s.join(`
`),_.join(`
`),l.join(`
`))}function xe(t,o,a,e,n,_,l){if(mi(t,o),bi(t,a),t.options&&t.options.length&&(vi(t,e),t.dataType==="dynamic")){const s=`${t.vModel}Options`,u=Le(s);yi(`get${u}`,s,n)}t.props&&t.props.props&&wi(t,_),t.action&&t.tag==="el-upload"&&(l.push(`
      // 上传请求路径
      const ${t.vModel}Action = ref('${t.action}')
      // 上传文件列表
      const ${t.vModel}fileList =  ref([])`),n.push(xi(t)),t["auto-upload"]||n.push(Ei(t))),t.children&&t.children.forEach(s=>{xe(s,o,a,e,n,_,l)})}function mi(t,o){if(t.vModel===void 0)return;let a;typeof t.defaultValue=="string"&&!t.multiple?a=`'${t.defaultValue}'`:a=`${JSON.stringify(t.defaultValue)}`,o.push(`${t.vModel}: ${a},`)}function bi(t,o){if(t.vModel===void 0)return;const a=[];if(ht[t.tag]){if(t.required){const e=Array.isArray(t.defaultValue)?"type: 'array',":"";let n=Array.isArray(t.defaultValue)?`请至少选择一个${t.vModel}`:t.placeholder;n===void 0&&(n=`${t.label}不能为空`),a.push(`{ required: true, ${e} message: '${n}', trigger: '${ht[t.tag]}' }`)}t.regList&&Array.isArray(t.regList)&&t.regList.forEach(e=>{e.pattern&&a.push(`{ pattern: new RegExp(${e.pattern}), message: '${e.message}', trigger: '${ht[t.tag]}' }`)}),o.push(`${t.vModel}: [${a.join(",")}],`)}}function vi(t,o){if(t.vModel===void 0)return;t.dataType==="dynamic"&&(t.options=[]);const a=`const ${t.vModel}Options = ref(${JSON.stringify(t.options)})`;o.push(a)}function yi(t,o,a){const e=`function ${t}() {
    // TODO 发起请求获取数据
    ${o}.value
  }`;a.push(e)}function wi(t,o){t.dataType==="dynamic"&&(t.valueKey!=="value"&&(t.props.props.value=t.valueKey),t.labelKey!=="label"&&(t.props.props.label=t.labelKey),t.childrenKey!=="children"&&(t.props.props.children=t.childrenKey));const a=`
  // props设置
  const ${t.vModel}Props = ref(${JSON.stringify(t.props.props)})`;o.push(a)}function xi(t){const o=di[t.sizeUnit];let a="",e="";const n=[];t.fileSize&&(a=`let isRightSize = file.size / ${o} < ${t.fileSize}
    if(!isRightSize){
      proxy.$modal.msgError('文件大小超过 ${t.fileSize}${t.sizeUnit}')
    }`,n.push("isRightSize")),t.accept&&(e=`let isAccept = new RegExp('${t.accept}').test(file.type)
    if(!isAccept){
      proxy.$modal.msgError('应该选择${t.accept}类型的文件')
    }`,n.push("isAccept"));const _=`
  /**
   * @name: 上传之前的文件判断
   * @description: 上传之前的文件判断，判断文件大小文件类型等
   * @param {*} file
   * @return {*}
   */  
  function ${t.vModel}BeforeUpload(file) {
    ${a}
    ${e}
    return ${n.join("&&")}
  }`;return n.length?_:""}function Ei(t){return`function submitUpload() {
    this.$refs['${t.vModel}'].submit()
  }`}function Oi(t,o,a,e,n,_,l,s){let u=`
    const { proxy } = getCurrentInstance()
    const ${t.formRef} = ref()
    const data = reactive({
      ${t.formModel}: {
        ${a}
      },
      ${t.formRules}: {
        ${e}
      }
    })

    const {${t.formModel}, ${t.formRules}} = toRefs(data)

    ${n}

    ${_}

    ${l}

    ${s}
  `;return o==="dialog"?u+=`
      // 弹窗设置
      const dialogVisible = defineModel()
      // 弹窗确认回调
      const emit = defineEmits(['confirm'])
      /**
       * @name: 弹窗打开后执行
       * @description: 弹窗打开后执行方法
       * @return {*}
       */
      function onOpen(){

      }
      /**
       * @name: 弹窗关闭时执行
       * @description: 弹窗关闭方法，重置表单
       * @return {*}
       */
      function onClose(){
        ${t.formRef}.value.resetFields()
      }
      /**
       * @name: 弹窗取消
       * @description: 弹窗取消方法
       * @return {*}
       */
      function close(){
        dialogVisible.value = false
      }
      /**
       * @name: 弹窗表单提交
       * @description: 弹窗表单提交方法
       * @return {*}
       */
      function handelConfirm(){
        ${t.formRef}.value.validate((valid) => {
          if (!valid) return
          // TODO 提交表单

          close()
          // 回调父级组件
          emit('confirm')
        })
      }
    `:u+=`
    /**
     * @name: 表单提交
     * @description: 表单提交方法
     * @return {*}
     */
    function submitForm() {
      ${t.formRef}.value.validate((valid) => {
        if (!valid) return
        // TODO 提交表单
      })
    }
    /**
     * @name: 表单重置
     * @description: 表单重置方法
     * @return {*}
     */
    function resetForm() {
      ${t.formRef}.value.resetFields()
    }
    `,u}const $i={"el-rate":".el-rate{display: inline-block; vertical-align: text-top;}","el-upload":".el-upload__tip{line-height: 1.2;}"};function Ee(t,o){const a=$i[o.tag];a&&t.indexOf(a)===-1&&t.push(a),o.children&&o.children.forEach(e=>Ee(t,e))}function Ti(t){const o=[];return t.fields.forEach(a=>Ee(o,a)),o.join(`
`)}const ki={class:"container"},Ri={class:"left-board"},Si={class:"logo-wrapper"},Ai={class:"logo"},Ni=["src"],Ci={class:"components-list"},Li={class:"components-title"},Mi=["onClick"],Pi={class:"components-body"},Di={class:"components-title"},Ii=["onClick"],ji={class:"components-body"},Bi={class:"components-title"},zi=["onClick"],Ki={class:"components-body"},Wi={class:"center-board"},Ui={class:"action-bar"},qi={class:"empty-info"},Fi=U("input",{id:"copyNode",type:"hidden"},null,-1),ts={__name:"index",setup(t){const o=G(Lt),{proxy:a}=$e(),e=G(!1),n=G(!1),_=G(""),l=G(100),s=G(Lt[0]),u=G(Lt[0].formId),w=G(null),m=G({}),d=G(Pe);let b,A;function T(O){s.value=O,u.value=O.formId}function M(){e.value=!0,n.value=!1,_.value="copy"}function p(){e.value=!0,n.value=!0,_.value="download"}function r(){a.$modal.confirm("确定要清空所有组件吗？","提示",{type:"warning"}).then(()=>{l.value=100,o.value=[]})}function f(O,$){O.from!==O.to&&(s.value=A,u.value=l.value)}function C(O){const $=x(O);o.value.push($),T($)}function x(O){const $=JSON.parse(JSON.stringify(O));return $.formId=++l.value,$.span=d.value.span,$.renderKey=+new Date,$.layout||($.layout="colFormItem"),$.layout==="colFormItem"?($.vModel=`field${l.value}`,$.placeholder!==void 0&&($.placeholder+=$.label),A=$):$.layout==="rowFormItem"&&(delete $.label,$.componentName=`row${l.value}`,$.gutter=d.value.gutter,A=$),A}function L(O,$){let j=JSON.parse(JSON.stringify(O));j=c(j),$.push(j),T(j)}function c(O){return O.formId=++l.value,O.renderKey=+new Date,O.layout==="colFormItem"?O.vModel=`field${l.value}`:O.layout==="rowFormItem"&&(O.componentName=`row${l.value}`),Array.isArray(O.children)&&(O.children=O.children.map($=>c($))),O}function y(O,$){$.splice(O,1),Bt(()=>{const j=o.value.length;j&&T(o.value[j-1])})}function h(O){O=x(O),O.vModel=s.value.vModel,O.formId=u.value,O.span=s.value.span,delete s.value.tag,delete s.value.tagIcon,delete s.value.document,Object.keys(O).forEach($=>{s.value[$]!==void 0&&typeof s.value[$]==typeof O[$]&&(O[$]=s.value[$])}),s.value=O,E(O,o.value)}function E(O,$){const j=$.findIndex(K=>K.formId===u.value);j>-1?$.splice(j,1,O):$.forEach(K=>{Array.isArray(K.children)&&E(O,K.children)})}function i(O){w.value=O,Bt(()=>{switch(_.value){case"copy":v();break;case"download":g(O);break}})}function g(O){const $=P(),j=new Blob([$],{type:"text/plain;charset=utf-8"});Ce.saveAs(j,O.fileName)}function v(O){document.getElementById("copyNode").click()}function N(){m.value={fields:JSON.parse(JSON.stringify(o.value)),...d.value}}function P(){const{type:O}=w.value;N();const $=ni(gi(m.value,O)),j=si(ci(m.value,O)),K=ai(Ti(m.value));return ti.html(j+$+K,Me.html)}return jt(()=>s.value.label,(O,$)=>{s.value.placeholder===void 0||!s.value.tag||b!==u.value||(s.value.placeholder=s.value.placeholder.replace($,"")+O)}),jt(u,O=>{b=O},{immediate:!0}),Te(()=>{new Ue("#copyNode",{text:$=>{const j=P();return ke({title:"成功",message:"代码已复制到剪切板，可粘贴。",type:"success"}),j}}).on("error",$=>{a.$modal.msgError("代码复制失败")})}),(O,$)=>{const j=Z("svg-icon"),K=Z("el-scrollbar"),F=Z("el-button"),J=Z("el-form"),H=Z("el-row");return tt(),nt("div",ki,[U("div",Ri,[U("div",Si,[U("div",Ai,[U("img",{src:z(ei),alt:"logo"},null,8,Ni),Q(" Form Generator ")])]),W(K,{class:"left-scrollbar"},{default:X(()=>[U("div",Ci,[U("div",Li,[W(j,{"icon-class":"component"}),Q("输入型组件 ")]),W(z(at),{class:"components-draggable",list:z(De),group:{name:"componentsGroup",pull:"clone",put:!1},clone:x,draggable:".components-item",sort:!1,onEnd:f,"item-key":"label"},{item:X(({element:S,index:k})=>[(tt(),nt("div",{key:k,class:"components-item",onClick:R=>C(S)},[U("div",Pi,[W(j,{"icon-class":S.tagIcon},null,8,["icon-class"]),Q(" "+mt(S.label),1)])],8,Mi))]),_:1},8,["list"]),U("div",Di,[W(j,{"icon-class":"component"}),Q("选择型组件 ")]),W(z(at),{class:"components-draggable",list:z(Ie),group:{name:"componentsGroup",pull:"clone",put:!1},clone:x,draggable:".components-item",sort:!1,onEnd:f,"item-key":"label"},{item:X(({element:S,index:k})=>[(tt(),nt("div",{key:k,class:"components-item",onClick:R=>C(S)},[U("div",ji,[W(j,{"icon-class":S.tagIcon},null,8,["icon-class"]),Q(" "+mt(S.label),1)])],8,Ii))]),_:1},8,["list"]),U("div",Bi,[W(j,{"icon-class":"component"}),Q(" 布局型组件 ")]),W(z(at),{class:"components-draggable",list:z(je),group:{name:"componentsGroup",pull:"clone",put:!1},clone:x,draggable:".components-item",sort:!1,onEnd:f,"item-key":"label"},{item:X(({element:S,index:k})=>[(tt(),nt("div",{key:k,class:"components-item",onClick:R=>C(S)},[U("div",Ki,[W(j,{"icon-class":S.tagIcon},null,8,["icon-class"]),Q(" "+mt(S.label),1)])],8,zi))]),_:1},8,["list"])])]),_:1})]),U("div",Wi,[U("div",Ui,[W(F,{icon:"Download",type:"primary",text:"",onClick:p},{default:X(()=>[Q(" 导出vue文件 ")]),_:1}),W(F,{class:"copy-btn-main",icon:"DocumentCopy",type:"primary",text:"",onClick:M},{default:X(()=>[Q(" 复制代码 ")]),_:1}),W(F,{class:"delete-btn",icon:"Delete",text:"",onClick:r,type:"danger"},{default:X(()=>[Q(" 清空 ")]),_:1})]),W(K,{class:"center-scrollbar"},{default:X(()=>[W(H,{class:"center-board-row",gutter:z(d).gutter},{default:X(()=>[W(J,{size:z(d).size,"label-position":z(d).labelPosition,disabled:z(d).disabled,"label-width":z(d).labelWidth+"px"},{default:X(()=>[W(z(at),{class:"drawing-board",list:z(o),animation:340,group:"componentsGroup","item-key":"label"},{item:X(({element:S,index:k})=>[(tt(),Se(z(ze),{key:S.renderKey,"drawing-list":z(o),element:S,index:k,"active-id":z(u),"form-conf":z(d),onActiveItem:T,onCopyItem:L,onDeleteItem:y},null,8,["drawing-list","element","index","active-id","form-conf"]))]),_:1},8,["list"]),Re(U("div",qi," 从左侧拖入或点选组件进行表单设计 ",512),[[Ae,!z(o).length]])]),_:1},8,["size","label-position","disabled","label-width"])]),_:1},8,["gutter"])]),_:1})]),W(z(Be),{"active-data":z(s),"form-conf":z(d),"show-field":!!z(o).length,onTagChange:h},null,8,["active-data","form-conf","show-field"]),W(z(Ke),{modelValue:z(e),"onUpdate:modelValue":$[0]||($[0]=S=>Ne(e)?e.value=S:null),title:"选择生成类型",showFileName:z(n),onConfirm:i},null,8,["modelValue","showFileName"]),Fi])}}};export{ts as default};
