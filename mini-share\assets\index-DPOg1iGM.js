import{T as H,B as me,d as fe,r as d,C as _e,F as ge,e as r,G as j,c as _,o as p,H as S,h as l,I as $,j as t,i as a,k as B,J as T,K as V,v as m,s as N,D as E,A as G,f as ve}from"./index-DDqcBwar.js";import{l as be,e as ke}from"./park-DZi9DkwV.js";function ye(w){return H({url:"/task/getTasks",method:"get",params:w})}function he(w){return H({url:"/task/addTask",method:"post",data:w})}const Te={class:"app-container"},Ve={class:"dialog-footer"},we=me({name:"Notice"}),Se=Object.assign(we,{setup(w){const{proxy:C}=fe(),D=d([]),f=d(!1),U=d(!0),x=d(!0),J=d([]),O=d(!0),W=d(!0),q=d(0),F=d(""),P=d([]),K=d([]),M=_e({form:{},queryParams:{pageNum:1,pageSize:10,taskType:void 0,status:void 0},rules:{parkId:[{required:!0,message:"车场不能为空",trigger:"change"}],taskType:[{required:!0,message:"工单类型不能为空",trigger:"change"}],executor:[{required:!0,message:"施工人员不能为空",trigger:"change"}],remark:[{required:!0,message:"任务概况不能为空",trigger:"blur"}]}}),{queryParams:u,form:s,rules:X}=ge(M);function g(){U.value=!0,ye(u.value).then(o=>{D.value=o.rows,q.value=o.total,U.value=!1})}function Y(){f.value=!1,L()}function L(){s.value={noticeId:void 0,noticeTitle:void 0,noticeType:void 0,noticeContent:void 0,status:"0"},C.resetForm("tagRef")}function v(){u.value.pageNum=1,g()}function Z(){C.resetForm("queryRef"),v()}function ee(o){J.value=o.map(n=>n.noticeId),O.value=o.length!=1,W.value=!o.length}function le(){L(),f.value=!0,F.value="添加工单"}function ae(){C.$refs.tagRef.validate(o=>{o&&he(s.value).then(n=>{C.$modal.msgSuccess("新增成功"),f.value=!1,g()})})}function te(){be().then(o=>{P.value=o.rows})}function ne(o){ke({phone:o}).then(n=>{K.value=n.data})}g(),te(),ne();const I=[{label:"维修",value:"1"},{label:"安装",value:"2"},{label:"其他",value:"3"}],z=[{label:"未完成",value:"0"},{label:"已完成",value:"1"}];return(o,n)=>{const b=r("el-option"),k=r("el-select"),c=r("el-form-item"),y=r("el-button"),A=r("el-form"),h=r("el-col"),oe=r("right-toolbar"),R=r("el-row"),i=r("el-table-column"),Q=r("dict-tag"),re=r("el-table"),ue=r("pagination"),se=r("el-input"),de=r("el-dialog"),ie=j("hasPermi"),pe=j("loading");return p(),_("div",Te,[S(l(A,{model:t(u),ref:"queryRef",inline:!0},{default:a(()=>[l(c,{label:"工单类型",prop:"taskType"},{default:a(()=>[l(k,{modelValue:t(u).taskType,"onUpdate:modelValue":n[0]||(n[0]=e=>t(u).taskType=e),placeholder:"请选择工单类型",style:{width:"200px"},onKeyup:B(v,["enter"])},{default:a(()=>[(p(),_(T,null,V(I,e=>l(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),l(c,{label:"状态",prop:"status"},{default:a(()=>[l(k,{modelValue:t(u).status,"onUpdate:modelValue":n[1]||(n[1]=e=>t(u).status=e),placeholder:"请选择状态",style:{width:"200px"},onKeyup:B(v,["enter"])},{default:a(()=>[(p(),_(T,null,V(z,e=>l(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),l(c,null,{default:a(()=>[l(y,{type:"primary",icon:"Search",onClick:v},{default:a(()=>[m("搜索")]),_:1}),l(y,{icon:"Refresh",onClick:Z},{default:a(()=>[m("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[$,t(x)]]),l(R,{gutter:10,class:"mb8"},{default:a(()=>[l(h,{span:1.5},{default:a(()=>[S((p(),N(y,{type:"primary",plain:"",icon:"Plus",onClick:le},{default:a(()=>[m("新增")]),_:1})),[[ie,["task:task:add"]]])]),_:1}),l(oe,{showSearch:t(x),"onUpdate:showSearch":n[2]||(n[2]=e=>E(x)?x.value=e:null),onQueryTable:g},null,8,["showSearch"])]),_:1}),S((p(),N(re,{data:t(D),onSelectionChange:ee},{default:a(()=>[l(i,{label:"序号",align:"center",type:"index",width:"100"}),l(i,{label:"车场",align:"center",prop:"parkName"}),l(i,{label:"创建人",align:"center",prop:"createBy"}),l(i,{label:"手机号",align:"center",prop:"phone"}),l(i,{label:"工单类型",align:"center",prop:"taskType"},{default:a(e=>[l(Q,{value:e.row.taskType,options:I},null,8,["value"])]),_:1}),l(i,{label:"施工人员",align:"center",prop:"executor"}),l(i,{label:"任务概况",align:"center",prop:"remark"}),l(i,{label:"创建时间",align:"center",prop:"createdTime"},{default:a(e=>[m(G(o.parseTime(e.row.createdTime)),1)]),_:1}),l(i,{label:"状态",align:"center",prop:"status"},{default:a(e=>[l(Q,{value:e.row.status,options:z},null,8,["value"])]),_:1}),l(i,{label:"完成时间",align:"center",prop:"finishTime"},{default:a(e=>[m(G(o.parseTime(e.row.finishTime)),1)]),_:1})]),_:1},8,["data"])),[[pe,t(U)]]),S(l(ue,{total:t(q),page:t(u).pageNum,"onUpdate:page":n[3]||(n[3]=e=>t(u).pageNum=e),limit:t(u).pageSize,"onUpdate:limit":n[4]||(n[4]=e=>t(u).pageSize=e),onPagination:g},null,8,["total","page","limit"]),[[$,t(q)>0]]),l(de,{title:t(F),modelValue:t(f),"onUpdate:modelValue":n[9]||(n[9]=e=>E(f)?f.value=e:null),width:"580px","append-to-body":""},{footer:a(()=>[ve("div",Ve,[l(y,{type:"primary",onClick:ae},{default:a(()=>[m("确 定")]),_:1}),l(y,{onClick:Y},{default:a(()=>[m("取 消")]),_:1})])]),default:a(()=>[l(A,{ref:"tagRef",model:t(s),rules:t(X),"label-width":"80px"},{default:a(()=>[l(R,null,{default:a(()=>[l(h,{span:24},{default:a(()=>[l(c,{label:"车场",prop:"parkId"},{default:a(()=>[l(k,{modelValue:t(s).parkId,"onUpdate:modelValue":n[5]||(n[5]=e=>t(s).parkId=e),clearable:"","reserve-keyword":"",remote:"",filterable:"","remote-method":t(P),onChange:o.parkChange},{default:a(()=>[(p(!0),_(T,null,V(t(P),(e,ce)=>(p(),N(b,{key:ce,label:e.parkName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","remote-method","onChange"])]),_:1})]),_:1}),l(h,{span:24},{default:a(()=>[l(c,{label:"工单类型",prop:"taskType"},{default:a(()=>[l(k,{modelValue:t(s).taskType,"onUpdate:modelValue":n[6]||(n[6]=e=>t(s).taskType=e),placeholder:"请选择工单类型",style:{width:"200px"},onKeyup:B(v,["enter"])},{default:a(()=>[(p(),_(T,null,V(I,e=>l(b,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1}),l(h,{span:24},{default:a(()=>[l(c,{label:"施工人员",prop:"executor"},{default:a(()=>[l(k,{modelValue:t(s).executor,"onUpdate:modelValue":n[7]||(n[7]=e=>t(s).executor=e),placeholder:"请选择",clearable:""},{default:a(()=>[(p(!0),_(T,null,V(t(K),e=>(p(),N(b,{key:e.phonenumber,label:e.nickName,value:e.phonenumber},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),l(R,null,{default:a(()=>[l(h,{span:24},{default:a(()=>[l(c,{label:"任务概况",prop:"remark"},{default:a(()=>[l(se,{modelValue:t(s).remark,"onUpdate:modelValue":n[8]||(n[8]=e=>t(s).remark=e),placeholder:"请输入任务概况",clearable:"",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Se as default};
