import{T as e}from"./index-DDqcBwar.js";function a(t){return e({url:"/system/park/getParks",method:"get",params:t})}function s(t){return e({url:"/system/park/getParkInfo/"+t,method:"get"})}function n(t){return e({url:"/system/park/addPark",method:"post",data:t})}function d(t){return e({url:"/system/park/updatePark",method:"put",data:t})}function u(t){return e({url:"/system/park/"+t,method:"delete"})}function m(){return e({url:"/system/area/list",method:"get"})}function o(t){return e({url:"/system/park/findTag/"+t,method:"get"})}function f(t){return e({url:"/system/park/findUserTagByPhone",method:"get",params:t})}function i(t){return e({url:"/system/park/addUserTagByPark",method:"post",data:t})}function p(t){return e({url:"/system/park/updateUserByPark",method:"put",data:t})}function k(t){return e({url:"/system/park/deleteUserTag",method:"delete",params:t})}function l(t){return e({url:"/system/park/findUserTag/"+t,method:"get"})}function g(t){return e({url:"/system/park/addManage",method:"post",data:t})}function c(t){return e({url:"/system/park/updateAdminUserByPark",method:"put",data:t})}function y(t){return e({url:"/system/park/findCarRecord",method:"get",params:t})}function h(t){return e({url:"/system/park/findDevice/"+t,method:"get"})}function P(t){return e({url:"/system/user/getUsers",method:"get",params:t})}function S(t){return e({url:"/system/park/deleteManage",method:"delete",params:t})}export{f as a,i as b,l as c,k as d,P as e,S as f,o as g,c as h,g as i,h as j,y as k,a as l,m,s as n,u as o,d as p,n as q,p as u};
