import{T as $,B as ne,d as ue,r as f,C as de,F as re,e as u,G as K,c as P,o as b,H as y,h as e,I as D,j as t,i as l,k as U,J as pe,K as se,v as c,s as x,A as ie,t as me,f as fe,D as ce}from"./index-DDqcBwar.js";function _e(V){return $({url:"/park/verify/list",method:"get",params:V})}function ge(V){return $({url:"/park/verify/identity",method:"put",data:V})}const ve={class:"app-container"},be={class:"dialog-footer"},ye=ne({name:"Notice"}),ke=Object.assign(ye,{setup(V){const{proxy:k}=ue(),S=f([]),_=f(!1),h=f(!0),L=f(!0),j=f([]),Q=f(!0),E=f(!0),w=f(0),T=f(""),G=de({form:{},queryParams:{pageNum:1,pageSize:10,tagName:void 0},rules:{}}),{queryParams:d,form:n,rules:H}=re(G);function N(){h.value=!0,_e(d.value).then(r=>{S.value=r.rows,w.value=r.total,h.value=!1})}function J(){_.value=!1,R()}function R(){n.value={noticeId:void 0,noticeTitle:void 0,noticeType:void 0,noticeContent:void 0},k.resetForm("tagRef")}function v(){d.value.pageNum=1,N()}function O(){k.resetForm("queryRef"),v()}function M(r){j.value=r.map(o=>o.noticeId),Q.value=r.length!=1,E.value=!r.length}function B(r){R(),n.value=r,n.value.applyStatus=r.status,_.value=!0,T.value="审核"}function W(){k.$refs.tagRef.validate(r=>{r&&n.value.verifyId!=null&&ge(n.value).then(o=>{k.$modal.msgSuccess("审核成功"),_.value=!1,N()})})}N();const C=[{label:"待审核",value:"1"},{label:"已通过",value:"2"},{label:"已拒绝",value:"3"}];return(r,o)=>{const s=u("el-input"),p=u("el-form-item"),X=u("el-option"),Y=u("el-select"),g=u("el-button"),I=u("el-form"),i=u("el-table-column"),q=u("dict-tag"),Z=u("el-table"),ee=u("pagination"),m=u("el-col"),z=u("el-radio"),le=u("el-radio-group"),A=u("el-row"),ae=u("el-dialog"),F=K("hasPermi"),te=K("loading");return b(),P("div",ve,[y(e(I,{model:t(d),ref:"queryRef",inline:!0},{default:l(()=>[e(p,{label:"车场",prop:"parkName"},{default:l(()=>[e(s,{modelValue:t(d).parkName,"onUpdate:modelValue":o[0]||(o[0]=a=>t(d).parkName=a),placeholder:"请输入车场",clearable:"",style:{width:"200px"},onKeyup:U(v,["enter"])},null,8,["modelValue"])]),_:1}),e(p,{label:"手机号",prop:"phone"},{default:l(()=>[e(s,{modelValue:t(d).phone,"onUpdate:modelValue":o[1]||(o[1]=a=>t(d).phone=a),placeholder:"请输入手机号",clearable:"",style:{width:"200px"},onKeyup:U(v,["enter"])},null,8,["modelValue"])]),_:1}),e(p,{label:"状态",prop:"status"},{default:l(()=>[e(Y,{modelValue:t(d).status,"onUpdate:modelValue":o[2]||(o[2]=a=>t(d).status=a),placeholder:"请选择设备状态",style:{width:"200px"},onKeyup:U(v,["enter"])},{default:l(()=>[(b(),P(pe,null,se(C,a=>e(X,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(p,null,{default:l(()=>[e(g,{type:"primary",icon:"Search",onClick:v},{default:l(()=>[c("搜索")]),_:1}),e(g,{icon:"Refresh",onClick:O},{default:l(()=>[c("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[D,t(L)]]),y((b(),x(Z,{data:t(S),onSelectionChange:M},{default:l(()=>[e(i,{label:"序号",align:"center",type:"index",width:"100"}),e(i,{label:"车场",align:"center",prop:"parkName"}),e(i,{label:"地址",align:"center",prop:"parkAddress"}),e(i,{label:"用户名",align:"center",prop:"wechatNickName"}),e(i,{label:"手机号",align:"center",prop:"phone"}),e(i,{label:"申请身份岗位",align:"center",prop:"tagName"}),e(i,{label:"申请时间",align:"center",prop:"applyTime"},{default:l(a=>[c(ie(r.parseTime(a.row.applyTime)),1)]),_:1}),e(i,{label:"状态",align:"center",prop:"status"},{default:l(a=>[e(q,{value:a.row.status,options:C},null,8,["value"])]),_:1}),e(i,{label:"操作",align:"center",width:"180px"},{default:l(a=>[a.row.status==1?y((b(),x(g,{key:0,type:"text",size:"small",onClick:oe=>B(a.row)},{default:l(()=>[c("审核")]),_:2},1032,["onClick"])),[[F,["park:verify:update"]]]):me("",!0),y((b(),x(g,{type:"text",size:"small",onClick:oe=>B(a.row)},{default:l(()=>[c("详情")]),_:2},1032,["onClick"])),[[F,["park:verify:query"]]])]),_:1})]),_:1},8,["data"])),[[te,t(h)]]),y(e(ee,{total:t(w),page:t(d).pageNum,"onUpdate:page":o[3]||(o[3]=a=>t(d).pageNum=a),limit:t(d).pageSize,"onUpdate:limit":o[4]||(o[4]=a=>t(d).pageSize=a),onPagination:N},null,8,["total","page","limit"]),[[D,t(w)>0]]),e(ae,{title:t(T),modelValue:t(_),"onUpdate:modelValue":o[13]||(o[13]=a=>ce(_)?_.value=a:null),width:"580px","append-to-body":""},{footer:l(()=>[fe("div",be,[e(g,{type:"primary",onClick:W},{default:l(()=>[c("确 定")]),_:1}),e(g,{onClick:J},{default:l(()=>[c("取 消")]),_:1})])]),default:l(()=>[e(I,{ref:"tagRef",model:t(n),rules:t(H),"label-width":"80px"},{default:l(()=>[e(A,null,{default:l(()=>[e(m,{span:24},{default:l(()=>[e(p,{label:"状态"},{default:l(()=>[e(q,{value:t(n).applyStatus,options:C},null,8,["value"])]),_:1})]),_:1}),e(m,{span:24},{default:l(()=>[e(p,{label:"车场",prop:"parkName"},{default:l(()=>[e(s,{modelValue:t(n).parkName,"onUpdate:modelValue":o[5]||(o[5]=a=>t(n).parkName=a),placeholder:"请输入车场"},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:24},{default:l(()=>[e(p,{label:"地址",prop:"parkAddress"},{default:l(()=>[e(s,{modelValue:t(n).parkAddress,"onUpdate:modelValue":o[6]||(o[6]=a=>t(n).parkAddress=a),placeholder:"请输入地址",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:24},{default:l(()=>[e(p,{label:"用户名",prop:"wechatNickName"},{default:l(()=>[e(s,{modelValue:t(n).wechatNickName,"onUpdate:modelValue":o[7]||(o[7]=a=>t(n).wechatNickName=a),placeholder:"请输入用户名",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:24},{default:l(()=>[e(p,{label:"手机号",prop:"phone"},{default:l(()=>[e(s,{modelValue:t(n).phone,"onUpdate:modelValue":o[8]||(o[8]=a=>t(n).phone=a),placeholder:"请输入手机号",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:24},{default:l(()=>[e(p,{label:"申请身份岗位",prop:"tagName","label-width":"120px"},{default:l(()=>[e(s,{modelValue:t(n).tagName,"onUpdate:modelValue":o[9]||(o[9]=a=>t(n).tagName=a),placeholder:"请输入申请身份岗位",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:24},{default:l(()=>[e(p,{label:"申请时间",prop:"applyTime"},{default:l(()=>[e(s,{modelValue:t(n).applyTime,"onUpdate:modelValue":o[10]||(o[10]=a=>t(n).applyTime=a),placeholder:"请输入地址",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(m,{span:24},{default:l(()=>[e(p,{label:"附件材料"},{default:l(()=>[e(s,{modelValue:t(n).orderBy,"onUpdate:modelValue":o[11]||(o[11]=a=>t(n).orderBy=a),placeholder:"请输入地址",clearable:""},null,8,["modelValue"])]),_:1})]),_:1}),e(A,null,{default:l(()=>[e(m,{span:24},{default:l(()=>[e(p,{label:"审核",prop:"status"},{default:l(()=>[e(le,{modelValue:t(n).status,"onUpdate:modelValue":o[12]||(o[12]=a=>t(n).status=a)},{default:l(()=>[e(z,{value:2},{default:l(()=>[c("通过")]),_:1}),e(z,{value:3},{default:l(()=>[c("拒绝")]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{ke as default};
