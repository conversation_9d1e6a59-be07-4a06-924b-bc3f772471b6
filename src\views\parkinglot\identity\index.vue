<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="车场" prop="parkName">
        <el-input
          v-model="queryParams.parkName"
          placeholder="请输入车场"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择设备状态"
          style="width: 200px"
          @keyup.enter="handleQuery"
        >
          <el-option
            v-for="item in status"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="logList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column label="车场" align="center" prop="parkName" />

      <el-table-column label="地址" align="center" prop="parkAddress" />
      <el-table-column label="用户名" align="center" prop="wechatNickName" />
      <el-table-column label="手机号" align="center" prop="phone" />
      <el-table-column label="申请身份岗位" align="center" prop="tagName" />
      <el-table-column label="申请时间" align="center" prop="applyTime">
        <template #default="scope">
          {{ parseTime(scope.row.applyTime) }}
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :value="scope.row.status" :options="status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180px">
        <template #default="scope">
          <el-button
            type="text"
            size="small"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['park:verify:update']"
            v-if="scope.row.status == 1"
            >审核</el-button
          >
          <el-button type="text" size="small" @click="handleUpdate(scope.row)"
           v-hasPermi="['park:verify:query']"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" v-model="open" width="580px" append-to-body>
      <el-form ref="tagRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="状态">
              <dict-tag :value="form.applyStatus" :options="status" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="车场" prop="parkName">
              <el-input v-model="form.parkName" placeholder="请输入车场" />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="地址" prop="parkAddress">
              <el-input
                v-model="form.parkAddress"
                placeholder="请输入地址"
                clearable
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="用户名" prop="wechatNickName">
              <el-input
                v-model="form.wechatNickName"
                placeholder="请输入用户名"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="手机号" prop="phone">
              <el-input
                v-model="form.phone"
                placeholder="请输入手机号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="申请身份岗位"
              prop="tagName"
              label-width="120px"
            >
              <el-input
                v-model="form.tagName"
                placeholder="请输入申请身份岗位"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="申请时间" prop="applyTime">
              <el-input
                v-model="form.applyTime"
                placeholder="请输入地址"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件材料">
              <el-input
                v-model="form.orderBy"
                placeholder="请输入地址"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-row>
            <el-col :span="24">
              <el-form-item label="审核" prop="status">
                <el-radio-group v-model="form.status">
                  <el-radio :value="2">通过</el-radio>
                  <el-radio :value="3">拒绝</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Notice">
import {
  listIdentity,
  getIdentity,
  passIdentity,
} from "@/api/parkinglot/identity";

const { proxy } = getCurrentInstance();

const logList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    tagName: undefined,
  },
  rules: {},
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  listIdentity(queryParams.value).then((response) => {
    logList.value = response.rows;
    total.value = parseInt(response.total);
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
  };
  proxy.resetForm("tagRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset();

  // getIdentity(row.verifyId).then((response) => {
  //   form.value = response.data;
  //   open.value = true;
  //   title.value = "修改标签";
  // });
  form.value = row;

  form.value.applyStatus = row.status;
  open.value = true;
  title.value = "审核";
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["tagRef"].validate((valid) => {
    if (valid) {
      if (form.value.verifyId != undefined) {
        passIdentity(form.value).then((response) => {
          proxy.$modal.msgSuccess("审核成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const tagIds = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除标签编号为"' + tagIds + '"的数据项？')
    .then(function () {
      return delTag(tagIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

getList();
// 静态数据
//操作类型 value = "操作类型", allowableValues = "1, 2, 3"

//设备状态 value = "设备状态", allowableValues = "0, 1, 2"
const status = [
  { label: "待审核", value: "1" },
  { label: "已通过", value: "2" },
  { label: "已拒绝", value: "3" },
];
</script>
