import{g as re,_ as ie,b as ne,u as se,a as oe,d as he,r as ft,w as ae,e as dt,c as Ot,f as ot,h as W,i as nt,j as st,k as ue,l as ce,o as It,m as fe,n as $,p as le,q as pe}from"./index-DDqcBwar.js";var kt={exports:{}};/*! For license information please see jsencrypt.min.js.LICENSE.txt */(function(Z,J){(function(lt,ht){Z.exports=ht()})(window,()=>(()=>{var lt={155:q=>{var m,w,v=q.exports={};function G(){throw new Error("setTimeout has not been defined")}function Q(){throw new Error("clearTimeout has not been defined")}function rt(E){if(m===setTimeout)return setTimeout(E,0);if((m===G||!m)&&setTimeout)return m=setTimeout,setTimeout(E,0);try{return m(E,0)}catch{try{return m.call(null,E,0)}catch{return m.call(this,E,0)}}}(function(){try{m=typeof setTimeout=="function"?setTimeout:G}catch{m=G}try{w=typeof clearTimeout=="function"?clearTimeout:Q}catch{w=Q}})();var F,y=[],D=!1,O=-1;function L(){D&&F&&(D=!1,F.length?y=F.concat(y):O=-1,y.length&&C())}function C(){if(!D){var E=rt(L);D=!0;for(var N=y.length;N;){for(F=y,y=[];++O<N;)F&&F[O].run();O=-1,N=y.length}F=null,D=!1,function(_){if(w===clearTimeout)return clearTimeout(_);if((w===Q||!w)&&clearTimeout)return w=clearTimeout,clearTimeout(_);try{return w(_)}catch{try{return w.call(null,_)}catch{return w.call(this,_)}}}(E)}}function H(E,N){this.fun=E,this.array=N}function I(){}v.nextTick=function(E){var N=new Array(arguments.length-1);if(arguments.length>1)for(var _=1;_<arguments.length;_++)N[_-1]=arguments[_];y.push(new H(E,N)),y.length!==1||D||rt(C)},H.prototype.run=function(){this.fun.apply(null,this.array)},v.title="browser",v.browser=!0,v.env={},v.argv=[],v.version="",v.versions={},v.on=I,v.addListener=I,v.once=I,v.off=I,v.removeListener=I,v.removeAllListeners=I,v.emit=I,v.prependListener=I,v.prependOnceListener=I,v.listeners=function(E){return[]},v.binding=function(E){throw new Error("process.binding is not supported")},v.cwd=function(){return"/"},v.chdir=function(E){throw new Error("process.chdir is not supported")},v.umask=function(){return 0}}},ht={};function z(q){var m=ht[q];if(m!==void 0)return m.exports;var w=ht[q]={exports:{}};return lt[q](w,w.exports,z),w.exports}z.d=(q,m)=>{for(var w in m)z.o(m,w)&&!z.o(q,w)&&Object.defineProperty(q,w,{enumerable:!0,get:m[w]})},z.o=(q,m)=>Object.prototype.hasOwnProperty.call(q,m);var V={};return(()=>{z.d(V,{default:()=>te});var q="0123456789abcdefghijklmnopqrstuvwxyz";function m(i){return q.charAt(i)}function w(i,t){return i&t}function v(i,t){return i|t}function G(i,t){return i^t}function Q(i,t){return i&~t}function rt(i){if(i==0)return-1;var t=0;return!(65535&i)&&(i>>=16,t+=16),!(255&i)&&(i>>=8,t+=8),!(15&i)&&(i>>=4,t+=4),!(3&i)&&(i>>=2,t+=2),!(1&i)&&++t,t}function F(i){for(var t=0;i!=0;)i&=i-1,++t;return t}var y,D="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",O="=";function L(i){var t,e,r="";for(t=0;t+3<=i.length;t+=3)e=parseInt(i.substring(t,t+3),16),r+=D.charAt(e>>6)+D.charAt(63&e);for(t+1==i.length?(e=parseInt(i.substring(t,t+1),16),r+=D.charAt(e<<2)):t+2==i.length&&(e=parseInt(i.substring(t,t+2),16),r+=D.charAt(e>>2)+D.charAt((3&e)<<4));(3&r.length)>0;)r+=O;return r}function C(i){var t,e="",r=0,n=0;for(t=0;t<i.length&&i.charAt(t)!=O;++t){var s=D.indexOf(i.charAt(t));s<0||(r==0?(e+=m(s>>2),n=3&s,r=1):r==1?(e+=m(n<<2|s>>4),n=15&s,r=2):r==2?(e+=m(n),e+=m(s>>2),n=3&s,r=3):(e+=m(n<<2|s>>4),e+=m(15&s),r=0))}return r==1&&(e+=m(n<<2)),e}var H,I={decode:function(i){var t;if(H===void 0){var e=`= \f
\r	 \u2028\u2029`;for(H=Object.create(null),t=0;t<64;++t)H["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t)]=t;for(H["-"]=62,H._=63,t=0;t<e.length;++t)H[e.charAt(t)]=-1}var r=[],n=0,s=0;for(t=0;t<i.length;++t){var o=i.charAt(t);if(o=="=")break;if((o=H[o])!=-1){if(o===void 0)throw new Error("Illegal character at offset "+t);n|=o,++s>=4?(r[r.length]=n>>16,r[r.length]=n>>8&255,r[r.length]=255&n,n=0,s=0):n<<=6}}switch(s){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:r[r.length]=n>>10;break;case 3:r[r.length]=n>>16,r[r.length]=n>>8&255}return r},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(i){var t=I.re.exec(i);if(t)if(t[1])i=t[1];else{if(!t[2])throw new Error("RegExp out of sync");i=t[2]}return I.decode(i)}},E=1e13,N=function(){function i(t){this.buf=[+t||0]}return i.prototype.mulAdd=function(t,e){var r,n,s=this.buf,o=s.length;for(r=0;r<o;++r)(n=s[r]*t+e)<E?e=0:n-=(e=0|n/E)*E,s[r]=n;e>0&&(s[r]=e)},i.prototype.sub=function(t){var e,r,n=this.buf,s=n.length;for(e=0;e<s;++e)(r=n[e]-t)<0?(r+=E,t=1):t=0,n[e]=r;for(;n[n.length-1]===0;)n.pop()},i.prototype.toString=function(t){if((t||10)!=10)throw new Error("only base 10 is supported");for(var e=this.buf,r=e[e.length-1].toString(),n=e.length-2;n>=0;--n)r+=(E+e[n]).toString().substring(1);return r},i.prototype.valueOf=function(){for(var t=this.buf,e=0,r=t.length-1;r>=0;--r)e=e*E+t[r];return e},i.prototype.simplify=function(){var t=this.buf;return t.length==1?t[0]:this},i}(),_="…",Nt=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,Pt=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function at(i,t){return i.length>t&&(i=i.substring(0,t)+_),i}var X,xt=function(){function i(t,e){this.hexDigits="0123456789ABCDEF",t instanceof i?(this.enc=t.enc,this.pos=t.pos):(this.enc=t,this.pos=e)}return i.prototype.get=function(t){if(t===void 0&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset ".concat(t," on a stream of length ").concat(this.enc.length));return typeof this.enc=="string"?this.enc.charCodeAt(t):this.enc[t]},i.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(15&t)},i.prototype.hexDump=function(t,e,r){for(var n="",s=t;s<e;++s)if(n+=this.hexByte(this.get(s)),r!==!0)switch(15&s){case 7:n+="  ";break;case 15:n+=`
`;break;default:n+=" "}return n},i.prototype.isASCII=function(t,e){for(var r=t;r<e;++r){var n=this.get(r);if(n<32||n>176)return!1}return!0},i.prototype.parseStringISO=function(t,e){for(var r="",n=t;n<e;++n)r+=String.fromCharCode(this.get(n));return r},i.prototype.parseStringUTF=function(t,e){for(var r="",n=t;n<e;){var s=this.get(n++);r+=s<128?String.fromCharCode(s):s>191&&s<224?String.fromCharCode((31&s)<<6|63&this.get(n++)):String.fromCharCode((15&s)<<12|(63&this.get(n++))<<6|63&this.get(n++))}return r},i.prototype.parseStringBMP=function(t,e){for(var r,n,s="",o=t;o<e;)r=this.get(o++),n=this.get(o++),s+=String.fromCharCode(r<<8|n);return s},i.prototype.parseTime=function(t,e,r){var n=this.parseStringISO(t,e),s=(r?Nt:Pt).exec(n);return s?(r&&(s[1]=+s[1],s[1]+=+s[1]<70?2e3:1900),n=s[1]+"-"+s[2]+"-"+s[3]+" "+s[4],s[5]&&(n+=":"+s[5],s[6]&&(n+=":"+s[6],s[7]&&(n+="."+s[7]))),s[8]&&(n+=" UTC",s[8]!="Z"&&(n+=s[8],s[9]&&(n+=":"+s[9]))),n):"Unrecognized time: "+n},i.prototype.parseInteger=function(t,e){for(var r,n=this.get(t),s=n>127,o=s?255:0,h="";n==o&&++t<e;)n=this.get(t);if((r=e-t)==0)return s?-1:0;if(r>4){for(h=n,r<<=3;!(128&(+h^o));)h=+h<<1,--r;h="("+r+` bit)
`}s&&(n-=256);for(var a=new N(n),c=t+1;c<e;++c)a.mulAdd(256,this.get(c));return h+a.toString()},i.prototype.parseBitString=function(t,e,r){for(var n=this.get(t),s="("+((e-t-1<<3)-n)+` bit)
`,o="",h=t+1;h<e;++h){for(var a=this.get(h),c=h==e-1?n:0,f=7;f>=c;--f)o+=a>>f&1?"1":"0";if(o.length>r)return s+at(o,r)}return s+o},i.prototype.parseOctetString=function(t,e,r){if(this.isASCII(t,e))return at(this.parseStringISO(t,e),r);var n=e-t,s="("+n+` byte)
`;n>(r/=2)&&(e=t+r);for(var o=t;o<e;++o)s+=this.hexByte(this.get(o));return n>r&&(s+=_),s},i.prototype.parseOID=function(t,e,r){for(var n="",s=new N,o=0,h=t;h<e;++h){var a=this.get(h);if(s.mulAdd(128,127&a),o+=7,!(128&a)){if(n==="")if((s=s.simplify())instanceof N)s.sub(80),n="2."+s.toString();else{var c=s<80?s<40?0:1:2;n=c+"."+(s-40*c)}else n+="."+s.toString();if(n.length>r)return at(n,r);s=new N,o=0}}return o>0&&(n+=".incomplete"),n},i}(),Zt=function(){function i(t,e,r,n,s){if(!(n instanceof Mt))throw new Error("Invalid tag value.");this.stream=t,this.header=e,this.length=r,this.tag=n,this.sub=s}return i.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},i.prototype.content=function(t){if(this.tag===void 0)return null;t===void 0&&(t=1/0);var e=this.posContent(),r=Math.abs(this.length);if(!this.tag.isUniversal())return this.sub!==null?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);switch(this.tag.tagNumber){case 1:return this.stream.get(e)===0?"false":"true";case 2:return this.stream.parseInteger(e,e+r);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(e,e+r,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);case 6:return this.stream.parseOID(e,e+r,t);case 16:case 17:return this.sub!==null?"("+this.sub.length+" elem)":"(no elem)";case 12:return at(this.stream.parseStringUTF(e,e+r),t);case 18:case 19:case 20:case 21:case 22:case 26:return at(this.stream.parseStringISO(e,e+r),t);case 30:return at(this.stream.parseStringBMP(e,e+r),t);case 23:case 24:return this.stream.parseTime(e,e+r,this.tag.tagNumber==23)}return null},i.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(this.sub===null?"null":this.sub.length)+"]"},i.prototype.toPrettyString=function(t){t===void 0&&(t="");var e=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(e+="+"),e+=this.length,this.tag.tagConstructed?e+=" (constructed)":!this.tag.isUniversal()||this.tag.tagNumber!=3&&this.tag.tagNumber!=4||this.sub===null||(e+=" (encapsulates)"),e+=`
`,this.sub!==null){t+="  ";for(var r=0,n=this.sub.length;r<n;++r)e+=this.sub[r].toPrettyString(t)}return e},i.prototype.posStart=function(){return this.stream.pos},i.prototype.posContent=function(){return this.stream.pos+this.header},i.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},i.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},i.decodeLength=function(t){var e=t.get(),r=127&e;if(r==e)return r;if(r>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(r===0)return null;e=0;for(var n=0;n<r;++n)e=256*e+t.get();return e},i.prototype.getHexStringValue=function(){var t=this.toHexString(),e=2*this.header,r=2*this.length;return t.substr(e,r)},i.decode=function(t){var e;e=t instanceof xt?t:new xt(t,0);var r=new xt(e),n=new Mt(e),s=i.decodeLength(e),o=e.pos,h=o-r.pos,a=null,c=function(){var b=[];if(s!==null){for(var g=o+s;e.pos<g;)b[b.length]=i.decode(e);if(e.pos!=g)throw new Error("Content size is not correct for container starting at offset "+o)}else try{for(;;){var x=i.decode(e);if(x.tag.isEOC())break;b[b.length]=x}s=o-e.pos}catch(S){throw new Error("Exception while decoding undefined length content: "+S)}return b};if(n.tagConstructed)a=c();else if(n.isUniversal()&&(n.tagNumber==3||n.tagNumber==4))try{if(n.tagNumber==3&&e.get()!=0)throw new Error("BIT STRINGs with unused bits cannot encapsulate.");a=c();for(var f=0;f<a.length;++f)if(a[f].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch{a=null}if(a===null){if(s===null)throw new Error("We can't skip over an invalid tag with undefined length at offset "+o);e.pos=o+Math.abs(s)}return new i(r,h,s,n,a)},i}(),Mt=function(){function i(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=(32&e)!=0,this.tagNumber=31&e,this.tagNumber==31){var r=new N;do e=t.get(),r.mulAdd(128,127&e);while(128&e);this.tagNumber=r.simplify()}}return i.prototype.isUniversal=function(){return this.tagClass===0},i.prototype.isEOC=function(){return this.tagClass===0&&this.tagNumber===0},i}(),P=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],zt=(1<<26)/P[P.length-1],p=function(){function i(t,e,r){t!=null&&(typeof t=="number"?this.fromNumber(t,e,r):e==null&&typeof t!="string"?this.fromString(t,256):this.fromString(t,e))}return i.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(t==16)e=4;else if(t==8)e=3;else if(t==2)e=1;else if(t==32)e=5;else{if(t!=4)return this.toRadix(t);e=2}var r,n=(1<<e)-1,s=!1,o="",h=this.t,a=this.DB-h*this.DB%e;if(h-- >0)for(a<this.DB&&(r=this[h]>>a)>0&&(s=!0,o=m(r));h>=0;)a<e?(r=(this[h]&(1<<a)-1)<<e-a,r|=this[--h]>>(a+=this.DB-e)):(r=this[h]>>(a-=e)&n,a<=0&&(a+=this.DB,--h)),r>0&&(s=!0),s&&(o+=m(r));return s?o:"0"},i.prototype.negate=function(){var t=d();return i.ZERO.subTo(this,t),t},i.prototype.abs=function(){return this.s<0?this.negate():this},i.prototype.compareTo=function(t){var e=this.s-t.s;if(e!=0)return e;var r=this.t;if((e=r-t.t)!=0)return this.s<0?-e:e;for(;--r>=0;)if((e=this[r]-t[r])!=0)return e;return 0},i.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+yt(this[this.t-1]^this.s&this.DM)},i.prototype.mod=function(t){var e=d();return this.abs().divRemTo(t,null,e),this.s<0&&e.compareTo(i.ZERO)>0&&t.subTo(e,e),e},i.prototype.modPowInt=function(t,e){var r;return r=t<256||e.isEven()?new qt(e):new _t(e),this.exp(t,r)},i.prototype.clone=function(){var t=d();return this.copyTo(t),t},i.prototype.intValue=function(){if(this.s<0){if(this.t==1)return this[0]-this.DV;if(this.t==0)return-1}else{if(this.t==1)return this[0];if(this.t==0)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},i.prototype.byteValue=function(){return this.t==0?this.s:this[0]<<24>>24},i.prototype.shortValue=function(){return this.t==0?this.s:this[0]<<16>>16},i.prototype.signum=function(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1},i.prototype.toByteArray=function(){var t=this.t,e=[];e[0]=this.s;var r,n=this.DB-t*this.DB%8,s=0;if(t-- >0)for(n<this.DB&&(r=this[t]>>n)!=(this.s&this.DM)>>n&&(e[s++]=r|this.s<<this.DB-n);t>=0;)n<8?(r=(this[t]&(1<<n)-1)<<8-n,r|=this[--t]>>(n+=this.DB-8)):(r=this[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),128&r&&(r|=-256),s==0&&(128&this.s)!=(128&r)&&++s,(s>0||r!=this.s)&&(e[s++]=r);return e},i.prototype.equals=function(t){return this.compareTo(t)==0},i.prototype.min=function(t){return this.compareTo(t)<0?this:t},i.prototype.max=function(t){return this.compareTo(t)>0?this:t},i.prototype.and=function(t){var e=d();return this.bitwiseTo(t,w,e),e},i.prototype.or=function(t){var e=d();return this.bitwiseTo(t,v,e),e},i.prototype.xor=function(t){var e=d();return this.bitwiseTo(t,G,e),e},i.prototype.andNot=function(t){var e=d();return this.bitwiseTo(t,Q,e),e},i.prototype.not=function(){for(var t=d(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},i.prototype.shiftLeft=function(t){var e=d();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},i.prototype.shiftRight=function(t){var e=d();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},i.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(this[t]!=0)return t*this.DB+rt(this[t]);return this.s<0?this.t*this.DB:-1},i.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=F(this[r]^e);return t},i.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?this.s!=0:(this[e]&1<<t%this.DB)!=0},i.prototype.setBit=function(t){return this.changeBit(t,v)},i.prototype.clearBit=function(t){return this.changeBit(t,Q)},i.prototype.flipBit=function(t){return this.changeBit(t,G)},i.prototype.add=function(t){var e=d();return this.addTo(t,e),e},i.prototype.subtract=function(t){var e=d();return this.subTo(t,e),e},i.prototype.multiply=function(t){var e=d();return this.multiplyTo(t,e),e},i.prototype.divide=function(t){var e=d();return this.divRemTo(t,e,null),e},i.prototype.remainder=function(t){var e=d();return this.divRemTo(t,null,e),e},i.prototype.divideAndRemainder=function(t){var e=d(),r=d();return this.divRemTo(t,e,r),[e,r]},i.prototype.modPow=function(t,e){var r,n,s=t.bitLength(),o=tt(1);if(s<=0)return o;r=s<18?1:s<48?3:s<144?4:s<768?5:6,n=s<8?new qt(e):e.isEven()?new Qt(e):new _t(e);var h=[],a=3,c=r-1,f=(1<<r)-1;if(h[1]=n.convert(this),r>1){var b=d();for(n.sqrTo(h[1],b);a<=f;)h[a]=d(),n.mulTo(b,h[a-2],h[a]),a+=2}var g,x,S=t.t-1,T=!0,A=d();for(s=yt(t[S])-1;S>=0;){for(s>=c?g=t[S]>>s-c&f:(g=(t[S]&(1<<s+1)-1)<<c-s,S>0&&(g|=t[S-1]>>this.DB+s-c)),a=r;!(1&g);)g>>=1,--a;if((s-=a)<0&&(s+=this.DB,--S),T)h[g].copyTo(o),T=!1;else{for(;a>1;)n.sqrTo(o,A),n.sqrTo(A,o),a-=2;a>0?n.sqrTo(o,A):(x=o,o=A,A=x),n.mulTo(A,h[g],o)}for(;S>=0&&!(t[S]&1<<s);)n.sqrTo(o,A),x=o,o=A,A=x,--s<0&&(s=this.DB-1,--S)}return n.revert(o)},i.prototype.modInverse=function(t){var e=t.isEven();if(this.isEven()&&e||t.signum()==0)return i.ZERO;for(var r=t.clone(),n=this.clone(),s=tt(1),o=tt(0),h=tt(0),a=tt(1);r.signum()!=0;){for(;r.isEven();)r.rShiftTo(1,r),e?(s.isEven()&&o.isEven()||(s.addTo(this,s),o.subTo(t,o)),s.rShiftTo(1,s)):o.isEven()||o.subTo(t,o),o.rShiftTo(1,o);for(;n.isEven();)n.rShiftTo(1,n),e?(h.isEven()&&a.isEven()||(h.addTo(this,h),a.subTo(t,a)),h.rShiftTo(1,h)):a.isEven()||a.subTo(t,a),a.rShiftTo(1,a);r.compareTo(n)>=0?(r.subTo(n,r),e&&s.subTo(h,s),o.subTo(a,o)):(n.subTo(r,n),e&&h.subTo(s,h),a.subTo(o,a))}return n.compareTo(i.ONE)!=0?i.ZERO:a.compareTo(t)>=0?a.subtract(t):a.signum()<0?(a.addTo(t,a),a.signum()<0?a.add(t):a):a},i.prototype.pow=function(t){return this.exp(t,new Gt)},i.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var n=e;e=r,r=n}var s=e.getLowestSetBit(),o=r.getLowestSetBit();if(o<0)return e;for(s<o&&(o=s),o>0&&(e.rShiftTo(o,e),r.rShiftTo(o,r));e.signum()>0;)(s=e.getLowestSetBit())>0&&e.rShiftTo(s,e),(s=r.getLowestSetBit())>0&&r.rShiftTo(s,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return o>0&&r.lShiftTo(o,r),r},i.prototype.isProbablePrime=function(t){var e,r=this.abs();if(r.t==1&&r[0]<=P[P.length-1]){for(e=0;e<P.length;++e)if(r[0]==P[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<P.length;){for(var n=P[e],s=e+1;s<P.length&&n<zt;)n*=P[s++];for(n=r.modInt(n);e<s;)if(n%P[e++]==0)return!1}return r.millerRabin(t)},i.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},i.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},i.prototype.fromString=function(t,e){var r;if(e==16)r=4;else if(e==8)r=3;else if(e==256)r=8;else if(e==2)r=1;else if(e==32)r=5;else{if(e!=4)return void this.fromRadix(t,e);r=2}this.t=0,this.s=0;for(var n=t.length,s=!1,o=0;--n>=0;){var h=r==8?255&+t[n]:jt(t,n);h<0?t.charAt(n)=="-"&&(s=!0):(s=!1,o==0?this[this.t++]=h:o+r>this.DB?(this[this.t-1]|=(h&(1<<this.DB-o)-1)<<o,this[this.t++]=h>>this.DB-o):this[this.t-1]|=h<<o,(o+=r)>=this.DB&&(o-=this.DB))}r==8&&128&+t[0]&&(this.s=-1,o>0&&(this[this.t-1]|=(1<<this.DB-o)-1<<o)),this.clamp(),s&&i.ZERO.subTo(this,this)},i.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},i.prototype.dlShiftTo=function(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s},i.prototype.drShiftTo=function(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s},i.prototype.lShiftTo=function(t,e){for(var r=t%this.DB,n=this.DB-r,s=(1<<n)-1,o=Math.floor(t/this.DB),h=this.s<<r&this.DM,a=this.t-1;a>=0;--a)e[a+o+1]=this[a]>>n|h,h=(this[a]&s)<<r;for(a=o-1;a>=0;--a)e[a]=0;e[o]=h,e.t=this.t+o+1,e.s=this.s,e.clamp()},i.prototype.rShiftTo=function(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var n=t%this.DB,s=this.DB-n,o=(1<<n)-1;e[0]=this[r]>>n;for(var h=r+1;h<this.t;++h)e[h-r-1]|=(this[h]&o)<<s,e[h-r]=this[h]>>n;n>0&&(e[this.t-r-1]|=(this.s&o)<<s),e.t=this.t-r,e.clamp()}},i.prototype.subTo=function(t,e){for(var r=0,n=0,s=Math.min(t.t,this.t);r<s;)n+=this[r]-t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n-=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n-=t[r],e[r++]=n&this.DM,n>>=this.DB;n-=t.s}e.s=n<0?-1:0,n<-1?e[r++]=this.DV+n:n>0&&(e[r++]=n),e.t=r,e.clamp()},i.prototype.multiplyTo=function(t,e){var r=this.abs(),n=t.abs(),s=r.t;for(e.t=s+n.t;--s>=0;)e[s]=0;for(s=0;s<n.t;++s)e[s+r.t]=r.am(0,n[s],e,s,0,r.t);e.s=0,e.clamp(),this.s!=t.s&&i.ZERO.subTo(e,e)},i.prototype.squareTo=function(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var n=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,n,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()},i.prototype.divRemTo=function(t,e,r){var n=t.abs();if(!(n.t<=0)){var s=this.abs();if(s.t<n.t)return e!=null&&e.fromInt(0),void(r!=null&&this.copyTo(r));r==null&&(r=d());var o=d(),h=this.s,a=t.s,c=this.DB-yt(n[n.t-1]);c>0?(n.lShiftTo(c,o),s.lShiftTo(c,r)):(n.copyTo(o),s.copyTo(r));var f=o.t,b=o[f-1];if(b!=0){var g=b*(1<<this.F1)+(f>1?o[f-2]>>this.F2:0),x=this.FV/g,S=(1<<this.F1)/g,T=1<<this.F2,A=r.t,Y=A-f,M=e??d();for(o.dlShiftTo(Y,M),r.compareTo(M)>=0&&(r[r.t++]=1,r.subTo(M,r)),i.ONE.dlShiftTo(f,M),M.subTo(o,o);o.t<f;)o[o.t++]=0;for(;--Y>=0;){var j=r[--A]==b?this.DM:Math.floor(r[A]*x+(r[A-1]+T)*S);if((r[A]+=o.am(0,j,r,Y,0,f))<j)for(o.dlShiftTo(Y,M),r.subTo(M,r);r[A]<--j;)r.subTo(M,r)}e!=null&&(r.drShiftTo(f,e),h!=a&&i.ZERO.subTo(e,e)),r.t=f,r.clamp(),c>0&&r.rShiftTo(c,r),h<0&&i.ZERO.subTo(r,r)}}},i.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(!(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},i.prototype.isEven=function(){return(this.t>0?1&this[0]:this.s)==0},i.prototype.exp=function(t,e){if(t>4294967295||t<1)return i.ONE;var r=d(),n=d(),s=e.convert(this),o=yt(t)-1;for(s.copyTo(r);--o>=0;)if(e.sqrTo(r,n),(t&1<<o)>0)e.mulTo(n,s,r);else{var h=r;r=n,n=h}return e.revert(r)},i.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},i.prototype.toRadix=function(t){if(t==null&&(t=10),this.signum()==0||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),n=tt(r),s=d(),o=d(),h="";for(this.divRemTo(n,s,o);s.signum()>0;)h=(r+o.intValue()).toString(t).substr(1)+h,s.divRemTo(n,s,o);return o.intValue().toString(t)+h},i.prototype.fromRadix=function(t,e){this.fromInt(0),e==null&&(e=10);for(var r=this.chunkSize(e),n=Math.pow(e,r),s=!1,o=0,h=0,a=0;a<t.length;++a){var c=jt(t,a);c<0?t.charAt(a)=="-"&&this.signum()==0&&(s=!0):(h=e*h+c,++o>=r&&(this.dMultiply(n),this.dAddOffset(h,0),o=0,h=0))}o>0&&(this.dMultiply(Math.pow(e,o)),this.dAddOffset(h,0)),s&&i.ZERO.subTo(this,this)},i.prototype.fromNumber=function(t,e,r){if(typeof e=="number")if(t<2)this.fromInt(1);else for(this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(i.ONE.shiftLeft(t-1),v,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(e);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(i.ONE.shiftLeft(t-1),this);else{var n=[],s=7&t;n.length=1+(t>>3),e.nextBytes(n),s>0?n[0]&=(1<<s)-1:n[0]=0,this.fromString(n,256)}},i.prototype.bitwiseTo=function(t,e,r){var n,s,o=Math.min(t.t,this.t);for(n=0;n<o;++n)r[n]=e(this[n],t[n]);if(t.t<this.t){for(s=t.s&this.DM,n=o;n<this.t;++n)r[n]=e(this[n],s);r.t=this.t}else{for(s=this.s&this.DM,n=o;n<t.t;++n)r[n]=e(s,t[n]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()},i.prototype.changeBit=function(t,e){var r=i.ONE.shiftLeft(t);return this.bitwiseTo(r,e,r),r},i.prototype.addTo=function(t,e){for(var r=0,n=0,s=Math.min(t.t,this.t);r<s;)n+=this[r]+t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n+=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n+=t[r],e[r++]=n&this.DM,n>>=this.DB;n+=t.s}e.s=n<0?-1:0,n>0?e[r++]=n:n<-1&&(e[r++]=this.DV+n),e.t=r,e.clamp()},i.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},i.prototype.dAddOffset=function(t,e){if(t!=0){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},i.prototype.multiplyLowerTo=function(t,e,r){var n=Math.min(this.t+t.t,e);for(r.s=0,r.t=n;n>0;)r[--n]=0;for(var s=r.t-this.t;n<s;++n)r[n+this.t]=this.am(0,t[n],r,n,0,this.t);for(s=Math.min(t.t,e);n<s;++n)this.am(0,t[n],r,n,0,e-n);r.clamp()},i.prototype.multiplyUpperTo=function(t,e,r){--e;var n=r.t=this.t+t.t-e;for(r.s=0;--n>=0;)r[n]=0;for(n=Math.max(e-this.t,0);n<t.t;++n)r[this.t+n-e]=this.am(e-n,t[n],r,0,0,this.t+n-e);r.clamp(),r.drShiftTo(1,r)},i.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(e==0)r=this[0]%t;else for(var n=this.t-1;n>=0;--n)r=(e*r+this[n])%t;return r},i.prototype.millerRabin=function(t){var e=this.subtract(i.ONE),r=e.getLowestSetBit();if(r<=0)return!1;var n=e.shiftRight(r);(t=t+1>>1)>P.length&&(t=P.length);for(var s=d(),o=0;o<t;++o){s.fromInt(P[Math.floor(Math.random()*P.length)]);var h=s.modPow(n,this);if(h.compareTo(i.ONE)!=0&&h.compareTo(e)!=0){for(var a=1;a++<r&&h.compareTo(e)!=0;)if((h=h.modPowInt(2,this)).compareTo(i.ONE)==0)return!1;if(h.compareTo(e)!=0)return!1}}return!0},i.prototype.square=function(){var t=d();return this.squareTo(t),t},i.prototype.gcda=function(t,e){var r=this.s<0?this.negate():this.clone(),n=t.s<0?t.negate():t.clone();if(r.compareTo(n)<0){var s=r;r=n,n=s}var o=r.getLowestSetBit(),h=n.getLowestSetBit();if(h<0)e(r);else{o<h&&(h=o),h>0&&(r.rShiftTo(h,r),n.rShiftTo(h,n));var a=function(){(o=r.getLowestSetBit())>0&&r.rShiftTo(o,r),(o=n.getLowestSetBit())>0&&n.rShiftTo(o,n),r.compareTo(n)>=0?(r.subTo(n,r),r.rShiftTo(1,r)):(n.subTo(r,n),n.rShiftTo(1,n)),r.signum()>0?setTimeout(a,0):(h>0&&n.lShiftTo(h,n),setTimeout(function(){e(n)},0))};setTimeout(a,10)}},i.prototype.fromNumberAsync=function(t,e,r,n){if(typeof e=="number")if(t<2)this.fromInt(1);else{this.fromNumber(t,r),this.testBit(t-1)||this.bitwiseTo(i.ONE.shiftLeft(t-1),v,this),this.isEven()&&this.dAddOffset(1,0);var s=this,o=function(){s.dAddOffset(2,0),s.bitLength()>t&&s.subTo(i.ONE.shiftLeft(t-1),s),s.isProbablePrime(e)?setTimeout(function(){n()},0):setTimeout(o,0)};setTimeout(o,0)}else{var h=[],a=7&t;h.length=1+(t>>3),e.nextBytes(h),a>0?h[0]&=(1<<a)-1:h[0]=0,this.fromString(h,256)}},i}(),Gt=function(){function i(){}return i.prototype.convert=function(t){return t},i.prototype.revert=function(t){return t},i.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},i.prototype.sqrTo=function(t,e){t.squareTo(e)},i}(),qt=function(){function i(t){this.m=t}return i.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},i.prototype.revert=function(t){return t},i.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},i.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},i.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},i}(),_t=function(){function i(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return i.prototype.convert=function(t){var e=d();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(p.ZERO)>0&&this.m.subTo(e,e),e},i.prototype.revert=function(t){var e=d();return t.copyTo(e),this.reduce(e),e},i.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],n=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=e+this.m.t]+=this.m.am(0,n,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},i.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},i.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},i}(),Qt=function(){function i(t){this.m=t,this.r2=d(),this.q3=d(),p.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return i.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=d();return t.copyTo(e),this.reduce(e),e},i.prototype.revert=function(t){return t},i.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},i.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},i.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},i}();function d(){return new p(null)}function B(i,t){return new p(i,t)}var Ct=typeof navigator<"u";Ct&&navigator.appName=="Microsoft Internet Explorer"?(p.prototype.am=function(i,t,e,r,n,s){for(var o=32767&t,h=t>>15;--s>=0;){var a=32767&this[i],c=this[i++]>>15,f=h*a+c*o;n=((a=o*a+((32767&f)<<15)+e[r]+(1073741823&n))>>>30)+(f>>>15)+h*c+(n>>>30),e[r++]=1073741823&a}return n},X=30):Ct&&navigator.appName!="Netscape"?(p.prototype.am=function(i,t,e,r,n,s){for(;--s>=0;){var o=t*this[i++]+e[r]+n;n=Math.floor(o/67108864),e[r++]=67108863&o}return n},X=26):(p.prototype.am=function(i,t,e,r,n,s){for(var o=16383&t,h=t>>14;--s>=0;){var a=16383&this[i],c=this[i++]>>14,f=h*a+c*o;n=((a=o*a+((16383&f)<<14)+e[r]+n)>>28)+(f>>14)+h*c,e[r++]=268435455&a}return n},X=28),p.prototype.DB=X,p.prototype.DM=(1<<X)-1,p.prototype.DV=1<<X,p.prototype.FV=Math.pow(2,52),p.prototype.F1=52-X,p.prototype.F2=2*X-52;var ut,K,vt=[];for(ut=48,K=0;K<=9;++K)vt[ut++]=K;for(ut=97,K=10;K<36;++K)vt[ut++]=K;for(ut=65,K=10;K<36;++K)vt[ut++]=K;function jt(i,t){var e=vt[i.charCodeAt(t)];return e??-1}function tt(i){var t=d();return t.fromInt(i),t}function yt(i){var t,e=1;return(t=i>>>16)!=0&&(i=t,e+=16),(t=i>>8)!=0&&(i=t,e+=8),(t=i>>4)!=0&&(i=t,e+=4),(t=i>>2)!=0&&(i=t,e+=2),(t=i>>1)!=0&&(i=t,e+=1),e}p.ZERO=tt(0),p.ONE=tt(1);var bt,k,Yt=function(){function i(){this.i=0,this.j=0,this.S=[]}return i.prototype.init=function(t){var e,r,n;for(e=0;e<256;++e)this.S[e]=e;for(r=0,e=0;e<256;++e)r=r+this.S[e]+t[e%t.length]&255,n=this.S[e],this.S[e]=this.S[r],this.S[r]=n;this.i=0,this.j=0},i.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},i}(),Lt=256,et=null;if(et==null){et=[],k=0;var Tt=void 0;if(typeof window<"u"&&window.crypto&&window.crypto.getRandomValues){var At=new Uint32Array(256);for(window.crypto.getRandomValues(At),Tt=0;Tt<At.length;++Tt)et[k++]=255&At[Tt]}var Bt=0,St=function(i){if((Bt=Bt||0)>=256||k>=Lt)window.removeEventListener?window.removeEventListener("mousemove",St,!1):window.detachEvent&&window.detachEvent("onmousemove",St);else try{var t=i.x+i.y;et[k++]=255&t,Bt+=1}catch{}};typeof window<"u"&&(window.addEventListener?window.addEventListener("mousemove",St,!1):window.attachEvent&&window.attachEvent("onmousemove",St))}function Wt(){if(bt==null){for(bt=new Yt;k<Lt;){var i=Math.floor(65536*Math.random());et[k++]=255&i}for(bt.init(et),k=0;k<et.length;++k)et[k]=0;k=0}return bt.next()}var Rt=function(){function i(){}return i.prototype.nextBytes=function(t){for(var e=0;e<t.length;++e)t[e]=Wt()},i}(),$t=function(){function i(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return i.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},i.prototype.doPrivate=function(t){if(this.p==null||this.q==null)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),r=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(r)<0;)e=e.add(this.p);return e.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)},i.prototype.setPublic=function(t,e){t!=null&&e!=null&&t.length>0&&e.length>0?(this.n=B(t,16),this.e=parseInt(e,16)):console.error("Invalid RSA public key")},i.prototype.encrypt=function(t){var e=this.n.bitLength()+7>>3,r=function(a,c){if(c<a.length+11)return console.error("Message too long for RSA"),null;for(var f=[],b=a.length-1;b>=0&&c>0;){var g=a.charCodeAt(b--);g<128?f[--c]=g:g>127&&g<2048?(f[--c]=63&g|128,f[--c]=g>>6|192):(f[--c]=63&g|128,f[--c]=g>>6&63|128,f[--c]=g>>12|224)}f[--c]=0;for(var x=new Rt,S=[];c>2;){for(S[0]=0;S[0]==0;)x.nextBytes(S);f[--c]=S[0]}return f[--c]=2,f[--c]=0,new p(f)}(t,e);if(r==null)return null;var n=this.doPublic(r);if(n==null)return null;for(var s=n.toString(16),o=s.length,h=0;h<2*e-o;h++)s="0"+s;return s},i.prototype.setPrivate=function(t,e,r){t!=null&&e!=null&&t.length>0&&e.length>0?(this.n=B(t,16),this.e=parseInt(e,16),this.d=B(r,16)):console.error("Invalid RSA private key")},i.prototype.setPrivateEx=function(t,e,r,n,s,o,h,a){t!=null&&e!=null&&t.length>0&&e.length>0?(this.n=B(t,16),this.e=parseInt(e,16),this.d=B(r,16),this.p=B(n,16),this.q=B(s,16),this.dmp1=B(o,16),this.dmq1=B(h,16),this.coeff=B(a,16)):console.error("Invalid RSA private key")},i.prototype.generate=function(t,e){var r=new Rt,n=t>>1;this.e=parseInt(e,16);for(var s=new p(e,16);;){for(;this.p=new p(t-n,1,r),this.p.subtract(p.ONE).gcd(s).compareTo(p.ONE)!=0||!this.p.isProbablePrime(10););for(;this.q=new p(n,1,r),this.q.subtract(p.ONE).gcd(s).compareTo(p.ONE)!=0||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var o=this.p;this.p=this.q,this.q=o}var h=this.p.subtract(p.ONE),a=this.q.subtract(p.ONE),c=h.multiply(a);if(c.gcd(s).compareTo(p.ONE)==0){this.n=this.p.multiply(this.q),this.d=s.modInverse(c),this.dmp1=this.d.mod(h),this.dmq1=this.d.mod(a),this.coeff=this.q.modInverse(this.p);break}}},i.prototype.decrypt=function(t){var e=B(t,16),r=this.doPrivate(e);return r==null?null:function(n,s){for(var o=n.toByteArray(),h=0;h<o.length&&o[h]==0;)++h;if(o.length-h!=s-1||o[h]!=2)return null;for(++h;o[h]!=0;)if(++h>=o.length)return null;for(var a="";++h<o.length;){var c=255&o[h];c<128?a+=String.fromCharCode(c):c>191&&c<224?(a+=String.fromCharCode((31&c)<<6|63&o[h+1]),++h):(a+=String.fromCharCode((15&c)<<12|(63&o[h+1])<<6|63&o[h+2]),h+=2)}return a}(r,this.n.bitLength()+7>>3)},i.prototype.generateAsync=function(t,e,r){var n=new Rt,s=t>>1;this.e=parseInt(e,16);var o=new p(e,16),h=this,a=function(){var c=function(){if(h.p.compareTo(h.q)<=0){var g=h.p;h.p=h.q,h.q=g}var x=h.p.subtract(p.ONE),S=h.q.subtract(p.ONE),T=x.multiply(S);T.gcd(o).compareTo(p.ONE)==0?(h.n=h.p.multiply(h.q),h.d=o.modInverse(T),h.dmp1=h.d.mod(x),h.dmq1=h.d.mod(S),h.coeff=h.q.modInverse(h.p),setTimeout(function(){r()},0)):setTimeout(a,0)},f=function(){h.q=d(),h.q.fromNumberAsync(s,1,n,function(){h.q.subtract(p.ONE).gcda(o,function(g){g.compareTo(p.ONE)==0&&h.q.isProbablePrime(10)?setTimeout(c,0):setTimeout(f,0)})})},b=function(){h.p=d(),h.p.fromNumberAsync(t-s,1,n,function(){h.p.subtract(p.ONE).gcda(o,function(g){g.compareTo(p.ONE)==0&&h.p.isProbablePrime(10)?setTimeout(f,0):setTimeout(b,0)})})};setTimeout(b,0)};setTimeout(a,0)},i.prototype.sign=function(t,e,r){var n=function(h,a){if(a<h.length+22)return console.error("Message too long for RSA"),null;for(var c=a-h.length-6,f="",b=0;b<c;b+=2)f+="ff";return B("0001"+f+"00"+h,16)}((Et[r]||"")+e(t).toString(),this.n.bitLength()/4);if(n==null)return null;var s=this.doPrivate(n);if(s==null)return null;var o=s.toString(16);return 1&o.length?"0"+o:o},i.prototype.verify=function(t,e,r){var n=B(e,16),s=this.doPublic(n);return s==null?null:function(o){for(var h in Et)if(Et.hasOwnProperty(h)){var a=Et[h],c=a.length;if(o.substr(0,c)==a)return o.substr(c)}return o}(s.toString(16).replace(/^1f+00/,""))==r(t).toString()},i}(),Et={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"},R={};R.lang={extend:function(i,t,e){if(!t||!i)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var r=function(){};if(r.prototype=t.prototype,i.prototype=new r,i.prototype.constructor=i,i.superclass=t.prototype,t.prototype.constructor==Object.prototype.constructor&&(t.prototype.constructor=t),e){var n;for(n in e)i.prototype[n]=e[n];var s=function(){},o=["toString","valueOf"];try{/MSIE/.test(navigator.userAgent)&&(s=function(h,a){for(n=0;n<o.length;n+=1){var c=o[n],f=a[c];typeof f=="function"&&f!=Object.prototype[c]&&(h[c]=f)}})}catch{}s(i.prototype,e)}}};var u={};u.asn1!==void 0&&u.asn1||(u.asn1={}),u.asn1.ASN1Util=new function(){this.integerToByteHex=function(i){var t=i.toString(16);return t.length%2==1&&(t="0"+t),t},this.bigIntToMinTwosComplementsHex=function(i){var t=i.toString(16);if(t.substr(0,1)!="-")t.length%2==1?t="0"+t:t.match(/^[0-7]/)||(t="00"+t);else{var e=t.substr(1).length;e%2==1?e+=1:t.match(/^[0-7]/)||(e+=2);for(var r="",n=0;n<e;n++)r+="f";t=new p(r,16).xor(i).add(p.ONE).toString(16).replace(/^-/,"")}return t},this.getPEMStringFromHex=function(i,t){return hextopem(i,t)},this.newObject=function(i){var t=u.asn1,e=t.DERBoolean,r=t.DERInteger,n=t.DERBitString,s=t.DEROctetString,o=t.DERNull,h=t.DERObjectIdentifier,a=t.DEREnumerated,c=t.DERUTF8String,f=t.DERNumericString,b=t.DERPrintableString,g=t.DERTeletexString,x=t.DERIA5String,S=t.DERUTCTime,T=t.DERGeneralizedTime,A=t.DERSequence,Y=t.DERSet,M=t.DERTaggedObject,j=t.ASN1Util.newObject,ct=Object.keys(i);if(ct.length!=1)throw"key of param shall be only one.";var l=ct[0];if(":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+l+":")==-1)throw"undefined key: "+l;if(l=="bool")return new e(i[l]);if(l=="int")return new r(i[l]);if(l=="bitstr")return new n(i[l]);if(l=="octstr")return new s(i[l]);if(l=="null")return new o(i[l]);if(l=="oid")return new h(i[l]);if(l=="enum")return new a(i[l]);if(l=="utf8str")return new c(i[l]);if(l=="numstr")return new f(i[l]);if(l=="prnstr")return new b(i[l]);if(l=="telstr")return new g(i[l]);if(l=="ia5str")return new x(i[l]);if(l=="utctime")return new S(i[l]);if(l=="gentime")return new T(i[l]);if(l=="seq"){for(var pt=i[l],gt=[],it=0;it<pt.length;it++){var Vt=j(pt[it]);gt.push(Vt)}return new A({array:gt})}if(l=="set"){for(pt=i[l],gt=[],it=0;it<pt.length;it++)Vt=j(pt[it]),gt.push(Vt);return new Y({array:gt})}if(l=="tag"){var U=i[l];if(Object.prototype.toString.call(U)==="[object Array]"&&U.length==3){var ee=j(U[2]);return new M({tag:U[0],explicit:U[1],obj:ee})}var Dt={};if(U.explicit!==void 0&&(Dt.explicit=U.explicit),U.tag!==void 0&&(Dt.tag=U.tag),U.obj===void 0)throw"obj shall be specified for 'tag'.";return Dt.obj=j(U.obj),new M(Dt)}},this.jsonToASN1HEX=function(i){return this.newObject(i).getEncodedHex()}},u.asn1.ASN1Util.oidHexToInt=function(i){for(var t="",e=parseInt(i.substr(0,2),16),r=(t=Math.floor(e/40)+"."+e%40,""),n=2;n<i.length;n+=2){var s=("00000000"+parseInt(i.substr(n,2),16).toString(2)).slice(-8);r+=s.substr(1,7),s.substr(0,1)=="0"&&(t=t+"."+new p(r,2).toString(10),r="")}return t},u.asn1.ASN1Util.oidIntToHex=function(i){var t=function(h){var a=h.toString(16);return a.length==1&&(a="0"+a),a},e=function(h){var a="",c=new p(h,10).toString(2),f=7-c.length%7;f==7&&(f=0);for(var b="",g=0;g<f;g++)b+="0";for(c=b+c,g=0;g<c.length-1;g+=7){var x=c.substr(g,7);g!=c.length-7&&(x="1"+x),a+=t(parseInt(x,2))}return a};if(!i.match(/^[0-9.]+$/))throw"malformed oid string: "+i;var r="",n=i.split("."),s=40*parseInt(n[0])+parseInt(n[1]);r+=t(s),n.splice(0,2);for(var o=0;o<n.length;o++)r+=e(n[o]);return r},u.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if(this.hV===void 0||this.hV==null)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n=0,v="+this.hV;var i=this.hV.length/2,t=i.toString(16);if(t.length%2==1&&(t="0"+t),i<128)return t;var e=t.length/2;if(e>15)throw"ASN.1 length too long to represent by 8x: n = "+i.toString(16);return(128+e).toString(16)+t},this.getEncodedHex=function(){return(this.hTLV==null||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},u.asn1.DERAbstractString=function(i){u.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(typeof i=="string"?this.setString(i):i.str!==void 0?this.setString(i.str):i.hex!==void 0&&this.setStringHex(i.hex))},R.lang.extend(u.asn1.DERAbstractString,u.asn1.ASN1Object),u.asn1.DERAbstractTime=function(i){u.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(t){return utc=t.getTime()+6e4*t.getTimezoneOffset(),new Date(utc)},this.formatDate=function(t,e,r){var n=this.zeroPadding,s=this.localDateToUTC(t),o=String(s.getFullYear());e=="utc"&&(o=o.substr(2,2));var h=o+n(String(s.getMonth()+1),2)+n(String(s.getDate()),2)+n(String(s.getHours()),2)+n(String(s.getMinutes()),2)+n(String(s.getSeconds()),2);if(r===!0){var a=s.getMilliseconds();if(a!=0){var c=n(String(a),3);h=h+"."+(c=c.replace(/[0]+$/,""))}}return h+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,e,r,n,s,o){var h=new Date(Date.UTC(t,e-1,r,n,s,o,0));this.setByDate(h)},this.getFreshValueHex=function(){return this.hV}},R.lang.extend(u.asn1.DERAbstractTime,u.asn1.ASN1Object),u.asn1.DERAbstractStructured=function(i){u.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,i!==void 0&&i.array!==void 0&&(this.asn1Array=i.array)},R.lang.extend(u.asn1.DERAbstractStructured,u.asn1.ASN1Object),u.asn1.DERBoolean=function(){u.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},R.lang.extend(u.asn1.DERBoolean,u.asn1.ASN1Object),u.asn1.DERInteger=function(i){u.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=u.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new p(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(i.bigint!==void 0?this.setByBigInteger(i.bigint):i.int!==void 0?this.setByInteger(i.int):typeof i=="number"?this.setByInteger(i):i.hex!==void 0&&this.setValueHex(i.hex))},R.lang.extend(u.asn1.DERInteger,u.asn1.ASN1Object),u.asn1.DERBitString=function(i){if(i!==void 0&&i.obj!==void 0){var t=u.asn1.ASN1Util.newObject(i.obj);i.hex="00"+t.getEncodedHex()}u.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(e){this.hTLV=null,this.isModified=!0,this.hV=e},this.setUnusedBitsAndHexValue=function(e,r){if(e<0||7<e)throw"unused bits shall be from 0 to 7: u = "+e;var n="0"+e;this.hTLV=null,this.isModified=!0,this.hV=n+r},this.setByBinaryString=function(e){var r=8-(e=e.replace(/0+$/,"")).length%8;r==8&&(r=0);for(var n=0;n<=r;n++)e+="0";var s="";for(n=0;n<e.length-1;n+=8){var o=e.substr(n,8),h=parseInt(o,2).toString(16);h.length==1&&(h="0"+h),s+=h}this.hTLV=null,this.isModified=!0,this.hV="0"+r+s},this.setByBooleanArray=function(e){for(var r="",n=0;n<e.length;n++)e[n]==1?r+="1":r+="0";this.setByBinaryString(r)},this.newFalseArray=function(e){for(var r=new Array(e),n=0;n<e;n++)r[n]=!1;return r},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(typeof i=="string"&&i.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(i):i.hex!==void 0?this.setHexValueIncludingUnusedBits(i.hex):i.bin!==void 0?this.setByBinaryString(i.bin):i.array!==void 0&&this.setByBooleanArray(i.array))},R.lang.extend(u.asn1.DERBitString,u.asn1.ASN1Object),u.asn1.DEROctetString=function(i){if(i!==void 0&&i.obj!==void 0){var t=u.asn1.ASN1Util.newObject(i.obj);i.hex=t.getEncodedHex()}u.asn1.DEROctetString.superclass.constructor.call(this,i),this.hT="04"},R.lang.extend(u.asn1.DEROctetString,u.asn1.DERAbstractString),u.asn1.DERNull=function(){u.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},R.lang.extend(u.asn1.DERNull,u.asn1.ASN1Object),u.asn1.DERObjectIdentifier=function(i){var t=function(r){var n=r.toString(16);return n.length==1&&(n="0"+n),n},e=function(r){var n="",s=new p(r,10).toString(2),o=7-s.length%7;o==7&&(o=0);for(var h="",a=0;a<o;a++)h+="0";for(s=h+s,a=0;a<s.length-1;a+=7){var c=s.substr(a,7);a!=s.length-7&&(c="1"+c),n+=t(parseInt(c,2))}return n};u.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(r){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=r},this.setValueOidString=function(r){if(!r.match(/^[0-9.]+$/))throw"malformed oid string: "+r;var n="",s=r.split("."),o=40*parseInt(s[0])+parseInt(s[1]);n+=t(o),s.splice(0,2);for(var h=0;h<s.length;h++)n+=e(s[h]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=n},this.setValueName=function(r){var n=u.asn1.x509.OID.name2oid(r);if(n==="")throw"DERObjectIdentifier oidName undefined: "+r;this.setValueOidString(n)},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(typeof i=="string"?i.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(i):this.setValueName(i):i.oid!==void 0?this.setValueOidString(i.oid):i.hex!==void 0?this.setValueHex(i.hex):i.name!==void 0&&this.setValueName(i.name))},R.lang.extend(u.asn1.DERObjectIdentifier,u.asn1.ASN1Object),u.asn1.DEREnumerated=function(i){u.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=u.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new p(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(i.int!==void 0?this.setByInteger(i.int):typeof i=="number"?this.setByInteger(i):i.hex!==void 0&&this.setValueHex(i.hex))},R.lang.extend(u.asn1.DEREnumerated,u.asn1.ASN1Object),u.asn1.DERUTF8String=function(i){u.asn1.DERUTF8String.superclass.constructor.call(this,i),this.hT="0c"},R.lang.extend(u.asn1.DERUTF8String,u.asn1.DERAbstractString),u.asn1.DERNumericString=function(i){u.asn1.DERNumericString.superclass.constructor.call(this,i),this.hT="12"},R.lang.extend(u.asn1.DERNumericString,u.asn1.DERAbstractString),u.asn1.DERPrintableString=function(i){u.asn1.DERPrintableString.superclass.constructor.call(this,i),this.hT="13"},R.lang.extend(u.asn1.DERPrintableString,u.asn1.DERAbstractString),u.asn1.DERTeletexString=function(i){u.asn1.DERTeletexString.superclass.constructor.call(this,i),this.hT="14"},R.lang.extend(u.asn1.DERTeletexString,u.asn1.DERAbstractString),u.asn1.DERIA5String=function(i){u.asn1.DERIA5String.superclass.constructor.call(this,i),this.hT="16"},R.lang.extend(u.asn1.DERIA5String,u.asn1.DERAbstractString),u.asn1.DERUTCTime=function(i){u.asn1.DERUTCTime.superclass.constructor.call(this,i),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return this.date===void 0&&this.s===void 0&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},i!==void 0&&(i.str!==void 0?this.setString(i.str):typeof i=="string"&&i.match(/^[0-9]{12}Z$/)?this.setString(i):i.hex!==void 0?this.setStringHex(i.hex):i.date!==void 0&&this.setByDate(i.date))},R.lang.extend(u.asn1.DERUTCTime,u.asn1.DERAbstractTime),u.asn1.DERGeneralizedTime=function(i){u.asn1.DERGeneralizedTime.superclass.constructor.call(this,i),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return this.date===void 0&&this.s===void 0&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},i!==void 0&&(i.str!==void 0?this.setString(i.str):typeof i=="string"&&i.match(/^[0-9]{14}Z$/)?this.setString(i):i.hex!==void 0?this.setStringHex(i.hex):i.date!==void 0&&this.setByDate(i.date),i.millis===!0&&(this.withMillis=!0))},R.lang.extend(u.asn1.DERGeneralizedTime,u.asn1.DERAbstractTime),u.asn1.DERSequence=function(i){u.asn1.DERSequence.superclass.constructor.call(this,i),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++)t+=this.asn1Array[e].getEncodedHex();return this.hV=t,this.hV}},R.lang.extend(u.asn1.DERSequence,u.asn1.DERAbstractStructured),u.asn1.DERSet=function(i){u.asn1.DERSet.superclass.constructor.call(this,i),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var r=this.asn1Array[e];t.push(r.getEncodedHex())}return this.sortFlag==1&&t.sort(),this.hV=t.join(""),this.hV},i!==void 0&&i.sortflag!==void 0&&i.sortflag==0&&(this.sortFlag=!1)},R.lang.extend(u.asn1.DERSet,u.asn1.DERAbstractStructured),u.asn1.DERTaggedObject=function(i){u.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,r){this.hT=e,this.isExplicit=t,this.asn1Object=r,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=r.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},i!==void 0&&(i.tag!==void 0&&(this.hT=i.tag),i.explicit!==void 0&&(this.isExplicit=i.explicit),i.obj!==void 0&&(this.asn1Object=i.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},R.lang.extend(u.asn1.DERTaggedObject,u.asn1.ASN1Object);var wt,Ht,Jt=(wt=function(i,t){return wt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])},wt(i,t)},function(i,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function e(){this.constructor=i}wt(i,t),i.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}),Kt=function(i){function t(e){var r=i.call(this)||this;return e&&(typeof e=="string"?r.parseKey(e):(t.hasPrivateKeyProperty(e)||t.hasPublicKeyProperty(e))&&r.parsePropertiesFrom(e)),r}return Jt(t,i),t.prototype.parseKey=function(e){try{var r=0,n=0,s=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(e)?function(S){var T;if(y===void 0){var A="0123456789ABCDEF",Y=` \f
\r	 \u2028\u2029`;for(y={},T=0;T<16;++T)y[A.charAt(T)]=T;for(A=A.toLowerCase(),T=10;T<16;++T)y[A.charAt(T)]=T;for(T=0;T<Y.length;++T)y[Y.charAt(T)]=-1}var M=[],j=0,ct=0;for(T=0;T<S.length;++T){var l=S.charAt(T);if(l=="=")break;if((l=y[l])!=-1){if(l===void 0)throw new Error("Illegal character at offset "+T);j|=l,++ct>=2?(M[M.length]=j,j=0,ct=0):j<<=4}}if(ct)throw new Error("Hex encoding incomplete: 4 bits missing");return M}(e):I.unarmor(e),o=Zt.decode(s);if(o.sub.length===3&&(o=o.sub[2].sub[0]),o.sub.length===9){r=o.sub[1].getHexStringValue(),this.n=B(r,16),n=o.sub[2].getHexStringValue(),this.e=parseInt(n,16);var h=o.sub[3].getHexStringValue();this.d=B(h,16);var a=o.sub[4].getHexStringValue();this.p=B(a,16);var c=o.sub[5].getHexStringValue();this.q=B(c,16);var f=o.sub[6].getHexStringValue();this.dmp1=B(f,16);var b=o.sub[7].getHexStringValue();this.dmq1=B(b,16);var g=o.sub[8].getHexStringValue();this.coeff=B(g,16)}else{if(o.sub.length!==2)return!1;if(o.sub[0].sub){var x=o.sub[1].sub[0];r=x.sub[0].getHexStringValue(),this.n=B(r,16),n=x.sub[1].getHexStringValue(),this.e=parseInt(n,16)}else r=o.sub[0].getHexStringValue(),this.n=B(r,16),n=o.sub[1].getHexStringValue(),this.e=parseInt(n,16)}return!0}catch{return!1}},t.prototype.getPrivateBaseKey=function(){var e={array:[new u.asn1.DERInteger({int:0}),new u.asn1.DERInteger({bigint:this.n}),new u.asn1.DERInteger({int:this.e}),new u.asn1.DERInteger({bigint:this.d}),new u.asn1.DERInteger({bigint:this.p}),new u.asn1.DERInteger({bigint:this.q}),new u.asn1.DERInteger({bigint:this.dmp1}),new u.asn1.DERInteger({bigint:this.dmq1}),new u.asn1.DERInteger({bigint:this.coeff})]};return new u.asn1.DERSequence(e).getEncodedHex()},t.prototype.getPrivateBaseKeyB64=function(){return L(this.getPrivateBaseKey())},t.prototype.getPublicBaseKey=function(){var e=new u.asn1.DERSequence({array:[new u.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new u.asn1.DERNull]}),r=new u.asn1.DERSequence({array:[new u.asn1.DERInteger({bigint:this.n}),new u.asn1.DERInteger({int:this.e})]}),n=new u.asn1.DERBitString({hex:"00"+r.getEncodedHex()});return new u.asn1.DERSequence({array:[e,n]}).getEncodedHex()},t.prototype.getPublicBaseKeyB64=function(){return L(this.getPublicBaseKey())},t.wordwrap=function(e,r){if(!e)return e;var n="(.{1,"+(r=r||64)+`})( +|$
?)|(.{1,`+r+"})";return e.match(RegExp(n,"g")).join(`
`)},t.prototype.getPrivateKey=function(){var e=`-----BEGIN RSA PRIVATE KEY-----
`;return(e+=t.wordwrap(this.getPrivateBaseKeyB64())+`
`)+"-----END RSA PRIVATE KEY-----"},t.prototype.getPublicKey=function(){var e=`-----BEGIN PUBLIC KEY-----
`;return(e+=t.wordwrap(this.getPublicBaseKeyB64())+`
`)+"-----END PUBLIC KEY-----"},t.hasPublicKeyProperty=function(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")},t.hasPrivateKeyProperty=function(e){return(e=e||{}).hasOwnProperty("n")&&e.hasOwnProperty("e")&&e.hasOwnProperty("d")&&e.hasOwnProperty("p")&&e.hasOwnProperty("q")&&e.hasOwnProperty("dmp1")&&e.hasOwnProperty("dmq1")&&e.hasOwnProperty("coeff")},t.prototype.parsePropertiesFrom=function(e){this.n=e.n,this.e=e.e,e.hasOwnProperty("d")&&(this.d=e.d,this.p=e.p,this.q=e.q,this.dmp1=e.dmp1,this.dmq1=e.dmq1,this.coeff=e.coeff)},t}($t),Ft=z(155),Xt=Ft!==void 0?(Ht=Ft.env)===null||Ht===void 0?void 0:"3.3.2":void 0;const te=function(){function i(t){t===void 0&&(t={}),t=t||{},this.default_key_size=t.default_key_size?parseInt(t.default_key_size,10):1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}return i.prototype.setKey=function(t){this.log&&this.key&&console.warn("A key was already set, overriding existing."),this.key=new Kt(t)},i.prototype.setPrivateKey=function(t){this.setKey(t)},i.prototype.setPublicKey=function(t){this.setKey(t)},i.prototype.decrypt=function(t){try{return this.getKey().decrypt(C(t))}catch{return!1}},i.prototype.encrypt=function(t){try{return L(this.getKey().encrypt(t))}catch{return!1}},i.prototype.sign=function(t,e,r){try{return L(this.getKey().sign(t,e,r))}catch{return!1}},i.prototype.verify=function(t,e,r){try{return this.getKey().verify(t,C(e),r)}catch{return!1}},i.prototype.getKey=function(t){if(!this.key){if(this.key=new Kt,t&&{}.toString.call(t)==="[object Function]")return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},i.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},i.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},i.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},i.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},i.version=Xt,i}()})(),V.default})())})(kt);var ge=kt.exports;const Ut=re(ge),de=`MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdH
nzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==`,me=`MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqhHyZfSsYourNxaY
7Nt+PrgrxkiA50efORdI5U5lsW79MmFnusUA355oaSXcLhu5xxB38SMSyP2KvuKN
PuH3owIDAQABAkAfoiLyL+Z4lf4Myxk6xUDgLaWGximj20CUf+5BKKnlrK+Ed8gA
kM0HqoTt2UZwA5E2MzS4EI2gjfQhz5X28uqxAiEA3wNFxfrCZlSZHb0gn2zDpWow
cSxQAgiCstxGUoOqlW8CIQDDOerGKH5OmCJ4Z21v+F25WaHYPxCFMvwxpcw99Ecv
DQIgIdhDTIqD2jfYjPTY8Jj3EDGPbH2HHuffvflECt3Ek60CIQCFRlCkHpi7hthh
YhovyloRYsM+IS9h/0BzlEAuO0ktMQIgSPT3aFAgJYwKpqRYKlLDVcflZFCKY7u3
UP8iWi1Qw0Y=`;function ve(Z){const J=new Ut;return J.setPublicKey(de),J.encrypt(Z)}function ye(Z){const J=new Ut;return J.setPrivateKey(me),J.decrypt(Z)}const mt=Z=>(le("data-v-021c6038"),Z=Z(),pe(),Z),be={class:"login"},Te={class:"login-content"},Se={class:"form"},Ee=mt(()=>ot("div",{class:"login-logo"},null,-1)),we=mt(()=>ot("div",{class:"short-tips"},"欢迎回来",-1)),De=mt(()=>ot("h3",{class:"title"},"共享车位管理平台",-1)),xe={key:0},Ae={key:1},Be=mt(()=>ot("div",{class:"form-copy"},"Copyright © 2019 悦马科技技术部出品",-1)),Re=mt(()=>ot("div",{class:"form-info"},null,-1)),Ve={__name:"login",setup(Z){const J=ne(),lt=se(),ht=oe(),{proxy:z}=he(),V=ft({username:"admin",password:"",rememberMe:!1,code:"",uuid:""}),q={username:[{required:!0,trigger:"blur",message:"请输入您的账号"}],password:[{required:!0,trigger:"blur",message:"请输入您的密码"}],code:[{required:!0,trigger:"change",message:"请输入验证码"}]},m=ft(""),w=ft(!1),v=ft(!0);ft(!1);const G=ft(void 0);ae(lt,y=>{G.value=y.query&&y.query.redirect},{immediate:!0});function Q(){z.$refs.loginRef.validate(y=>{y&&(w.value=!0,V.value.rememberMe?($.set("username",V.value.username,{expires:30}),$.set("password",ve(V.value.password),{expires:30}),$.set("rememberMe",V.value.rememberMe,{expires:30})):($.remove("username"),$.remove("password"),$.remove("rememberMe")),J.login(V.value).then(()=>{const D=lt.query,O=Object.keys(D).reduce((L,C)=>(C!=="redirect"&&(L[C]=D[C]),L),{});ht.push({path:G.value||"/",query:O})}).catch(()=>{w.value=!1,v.value&&rt()}))})}function rt(){fe().then(y=>{v.value=y.captchaEnabled===void 0?!0:y.captchaEnabled,v.value&&(m.value="data:image/gif;base64,"+y.img,V.value.uuid=y.uuid)})}function F(){const y=$.get("username"),D=$.get("password"),O=$.get("rememberMe");V.value={username:y===void 0?V.value.username:y,password:D===void 0?V.value.password:ye(D),rememberMe:O===void 0?!1:!!O}}return rt(),F(),(y,D)=>{const O=dt("svg-icon"),L=dt("el-input"),C=dt("el-form-item"),H=dt("el-button"),I=dt("el-form");return It(),Ot("div",be,[ot("div",Te,[ot("div",Se,[Ee,W(I,{ref:"loginRef",model:st(V),rules:q,class:"login-form"},{default:nt(()=>[we,De,W(C,{prop:"username"},{default:nt(()=>[W(L,{modelValue:st(V).username,"onUpdate:modelValue":D[0]||(D[0]=E=>st(V).username=E),type:"text","auto-complete":"off",placeholder:"账号"},{prefix:nt(()=>[W(O,{"icon-class":"icon_zhanghao",class:"login-icon"})]),_:1},8,["modelValue"])]),_:1}),W(C,{prop:"password"},{default:nt(()=>[W(L,{modelValue:st(V).password,"onUpdate:modelValue":D[1]||(D[1]=E=>st(V).password=E),type:"password","auto-complete":"off",placeholder:"密码",onKeyup:ue(Q,["enter"])},{prefix:nt(()=>[W(O,{"icon-class":"icon_mima",class:"login-icon"})]),_:1},8,["modelValue"])]),_:1}),W(C,{style:{width:"100%"}},{default:nt(()=>[W(H,{loading:st(w),type:"primary",style:{width:"100%"},onClick:ce(Q,["prevent"])},{default:nt(()=>[st(w)?(It(),Ot("span",Ae,"登 录 中...")):(It(),Ot("span",xe,"登 录"))]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"]),Be]),Re])])}}},Ie=ie(Ve,[["__scopeId","data-v-021c6038"]]);export{Ie as default};
