import{T as t}from"./index-DDqcBwar.js";function r(e){return t({url:"/device/inventory/getInventoryList",method:"get",params:e})}function o(e){return t({url:"/device/inventory",method:"post",data:e})}function i(e){return t({url:"/device/inventory",method:"put",data:e})}function d(e){return t({url:"/device/inventory/"+e,method:"delete"})}function u(){return t({url:"/device/inventory",method:"get"})}export{o as a,d,u as g,r as l,i as u};
