import{_ as oe,B as re,d as ue,r as p,C as ie,F as de,e as i,G as D,c as P,o as c,H as m,h as e,I as B,j as t,i as a,k as T,J as pe,K as se,v as r,s as y,A as s,D as ce}from"./index-DDqcBwar.js";import{a as me,g as fe}from"./statements-Nia77kYU.js";import{_ as _e}from"./oderDetails-Dn2_t_eq.js";const ge={class:"app-container"},ve=re({name:"Notice"}),be=Object.assign(ve,{setup(ye){const{proxy:N}=ue(),F=p([]),w=p(!1),V=p(!0),K=p(!0),M=p([]),E=p(!0),Y=p(!0),R=p(0);p("");const I=p({}),g=p({bindingCount:0,unbindingCount:0,totalInventories:0}),z=ie({form:{},queryParams:{pageNum:1,pageSize:10,orderNo:void 0,phone:void 0,parkName:void 0,plateNo:void 0,status:void 0,dataRangeTime:[]},rules:{lockNo:[{required:!0,message:"设备编号不能为空",trigger:"blur"}],phone:[{required:!0,message:"手机号不能为空",trigger:"blur"}]}}),{queryParams:n,form:L,rules:we}=de(z);function C(){var d;V.value=!0,((d=n.value.dataRangeTime)==null?void 0:d.length)>0?(n.value.orderBeginTime=n.value.dataRangeTime[0],n.value.orderEndTime=n.value.dataRangeTime[1]):(n.value.orderBeginTime=void 0,n.value.orderEndTime=void 0),me(n.value).then(o=>{F.value=o.rows,R.value=o.total,V.value=!1})}function $(){L.value={noticeId:void 0,noticeTitle:void 0,noticeType:void 0,noticeContent:void 0,status:"0"},N.resetForm("tagRef")}function f(){n.value.pageNum=1,C()}function j(){N.resetForm("queryRef"),f()}function Q(d){M.value=d.map(o=>o.noticeId),E.value=d.length!=1,Y.value=!d.length}function A(d){$(),w.value=!0,I.value=d}function G(){N.download("finance/export",{...n.value},"财务信息.xlsx")}function H(){fe().then(d=>{g.value=d.data})}C(),H();const J=[{value:"1",label:"普通计费"},{value:"2",label:"特殊计费"}],U=[{label:"待交押金",value:"-1"},{label:"已交押金待入场",value:"0"},{label:"已入场",value:"3"},{label:"已完成",value:"1"},{label:"已取消(主动)",value:"2"},{label:"已取消(自动取消)",value:"4"},{label:"待补缴",value:"5"}];return(d,o)=>{const S=i("el-input"),_=i("el-form-item"),O=i("el-date-picker"),W=i("el-option"),X=i("el-select"),v=i("el-button"),Z=i("el-form"),h=i("el-statistic"),x=i("el-card"),b=i("el-col"),ee=i("el-row"),u=i("el-table-column"),q=i("dict-tag"),le=i("el-table"),ae=i("pagination"),k=D("hasPermi"),te=D("loading");return c(),P("div",ge,[m(e(Z,{model:t(n),ref:"queryRef",inline:!0},{default:a(()=>[e(_,{label:"订单号",prop:"orderNo"},{default:a(()=>[e(S,{modelValue:t(n).orderNo,"onUpdate:modelValue":o[0]||(o[0]=l=>t(n).orderNo=l),placeholder:"请输入订单号",clearable:"",style:{width:"200px"},onKeyup:T(f,["enter"])},null,8,["modelValue"])]),_:1}),e(_,{label:"手机号",prop:"phone"},{default:a(()=>[e(S,{modelValue:t(n).phone,"onUpdate:modelValue":o[1]||(o[1]=l=>t(n).phone=l),placeholder:"请输入订单号",clearable:"",style:{width:"200px"},onKeyup:T(f,["enter"])},null,8,["modelValue"])]),_:1}),e(_,{label:"车场",prop:"parkName"},{default:a(()=>[e(S,{modelValue:t(n).parkName,"onUpdate:modelValue":o[2]||(o[2]=l=>t(n).parkName=l),placeholder:"请输入车场名称",clearable:"",style:{width:"200px"},onKeyup:T(f,["enter"])},null,8,["modelValue"])]),_:1}),e(_,{label:"下单时间",prop:"dataRangeTime"},{default:a(()=>[e(O,{modelValue:t(n).dataRangeTime,"onUpdate:modelValue":o[3]||(o[3]=l=>t(n).dataRangeTime=l),"value-format":"YYYY-MM-DD ",type:"daterange","range-separator":"To","start-placeholder":"开始时间","end-placeholder":"结束时间"},null,8,["modelValue"])]),_:1}),e(_,{label:"状态",prop:"status"},{default:a(()=>[e(X,{modelValue:t(n).status,"onUpdate:modelValue":o[4]||(o[4]=l=>t(n).status=l),placeholder:"请选择订单状态",style:{width:"200px"},onKeyup:T(f,["enter"])},{default:a(()=>[(c(),P(pe,null,se(U,l=>e(W,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(_,null,{default:a(()=>[e(v,{type:"primary",icon:"Search",onClick:f},{default:a(()=>[r("搜索")]),_:1}),e(v,{icon:"Refresh",onClick:j},{default:a(()=>[r("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[B,t(K)]]),m((c(),y(ee,{gutter:10,class:"mb8"},{default:a(()=>[e(b,{span:3},{default:a(()=>[e(x,{class:"dis-play"},{default:a(()=>[e(h,{title:"实收金额",value:t(g).totalActualFee},{suffix:a(()=>[r("元")]),_:1},8,["value"])]),_:1})]),_:1}),e(b,{span:3},{default:a(()=>[e(x,null,{default:a(()=>[e(h,{title:"账户支出",value:t(g).totalManualRefund},{suffix:a(()=>[r("元")]),_:1},8,["value"])]),_:1})]),_:1}),e(b,{span:3},{default:a(()=>[e(x,null,{default:a(()=>[e(h,{title:"账户收入",value:t(g).totalActualIncome},{suffix:a(()=>[r("元")]),_:1},8,["value"])]),_:1})]),_:1}),e(b,{span:3},{default:a(()=>[e(x,null,{default:a(()=>[e(h,{title:"净利润",value:t(g).totalPropertyFee},{suffix:a(()=>[r("元")]),_:1},8,["value"])]),_:1})]),_:1}),e(b,{span:12,class:"right-grid"},{default:a(()=>[m((c(),y(v,{type:"primary",plain:"",icon:"Plus",onClick:G},{default:a(()=>[r("导出")]),_:1})),[[k,["finance:report:export"]]])]),_:1})]),_:1})),[[k,["device:order:query"]]]),m((c(),y(le,{data:t(F),onSelectionChange:Q},{default:a(()=>[e(u,{label:"序号",align:"center",type:"index",width:"100"}),e(u,{label:"订单号",align:"center",prop:"orderNo",width:"300px"}),e(u,{label:"订单类型",align:"center",prop:"orderType"},{default:a(l=>[e(q,{value:l.row.orderType,options:J},null,8,["value"])]),_:1}),e(u,{label:"用户手机号",align:"center",prop:"phone",width:"200px"}),e(u,{label:"车牌号",align:"center",prop:"plateNo",width:"200px"}),e(u,{label:"车场",align:"center",prop:"parkName",width:"200px"}),e(u,{label:"下单时间",align:"center",prop:"orderTime",width:"200px"},{default:a(l=>[r(s(d.parseTime(l.row.orderTime)),1)]),_:1}),e(u,{label:"结束时间",align:"center",prop:"finishTime",width:"200px"},{default:a(l=>[r(s(d.parseTime(l.row.finishTime)),1)]),_:1}),e(u,{label:"正常消费",align:"center",prop:"parkAmount"},{default:a(l=>[r(s(l.row.parkAmount||0)+" 元 ",1)]),_:1}),e(u,{label:"额外费用",align:"center",prop:"timeoutAmount"},{default:a(l=>[r(s(l.row.timeoutAmount||0)+" 元 ",1)]),_:1}),e(u,{label:"应收金额",align:"center",prop:"payableAmount"},{default:a(l=>[r(s(l.row.payableAmount||0)+"元 ",1)]),_:1}),e(u,{label:"实收金额",align:"center",prop:"actualFee"},{default:a(l=>[r(s(l.row.actualFee||0)+" 元 ",1)]),_:1}),e(u,{label:"需退费金额",align:"center",prop:"needRefund",width:"100px"},{default:a(l=>[r(s(l.row.needRefund||0)+" 元 ",1)]),_:1}),e(u,{label:"待补缴金额",align:"center",prop:"conscienceMoney",width:"100px"},{default:a(l=>[r(s(l.row.conscienceMoney||0)+" 元 ",1)]),_:1}),e(u,{label:"状态",align:"center",width:"200"},{default:a(l=>[e(q,{value:l.row.status,options:U},null,8,["value"])]),_:1}),e(u,{label:"人工退费",align:"center",prop:"manualRefund"},{default:a(l=>[r(s(l.row.manualRefund||0)+" 元 ",1)]),_:1}),e(u,{label:"分成扣除",align:"center",prop:"propertyFee"},{default:a(l=>[r(s(l.row.propertyFee||0)+" 元 ",1)]),_:1}),e(u,{label:"实际收入",align:"center",prop:"actualIncome"},{default:a(l=>[r(s(l.row.actualIncome||0)+" 元 ",1)]),_:1}),e(u,{label:"操作",align:"center","class-name":"small-padding fixed-width",fixed:"right",width:"200"},{default:a(l=>[m((c(),y(v,{link:"",type:"primary",icon:"Tickets",onClick:ne=>A(l.row)},{default:a(()=>[r("订单详情")]),_:2},1032,["onClick"])),[[k,["finance:report:view"]]]),m((c(),y(v,{link:"",type:"primary",onClick:ne=>A(l.row)},{default:a(()=>[r("退款")]),_:2},1032,["onClick"])),[[k,["finance:report:refund"]]])]),_:1})]),_:1},8,["data"])),[[te,t(V)]]),m(e(ae,{total:t(R),page:t(n).pageNum,"onUpdate:page":o[5]||(o[5]=l=>t(n).pageNum=l),limit:t(n).pageSize,"onUpdate:limit":o[6]||(o[6]=l=>t(n).pageSize=l),onPagination:C},null,8,["total","page","limit"]),[[B,t(R)>0]]),e(_e,{modelValue:t(w),"onUpdate:modelValue":o[7]||(o[7]=l=>ce(w)?w.value=l:null),item:t(I)},null,8,["modelValue","item"])])}}}),Te=oe(be,[["__scopeId","data-v-845199bd"]]);export{Te as default};
