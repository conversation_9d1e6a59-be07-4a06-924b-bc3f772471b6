<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Back"
          @click="handleBack"
        
          >返回</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:park:user:add']"
          >新增</el-button
        >
      </el-col>

      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="peopleList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column
        label="名称"
        align="center"
        prop="username"
        :show-overflow-tooltip="true"
      />

      <!-- <el-table-column label="标签" align="center" prop="tagNames" /> -->
      <el-table-column label="所属车场" align="center" prop="parkName" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
             v-hasPermi="['system:park:user:update']"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
              v-hasPermi="['system:park:user:delete']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" v-model="open" width="880px" append-to-body>
      <el-form ref="parkRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="手机号" prop="phone">
              <el-select
                v-model="form.phone"
                placeholder="请选择"
                clearable
                reserve-keyword
                remote
                filterable
                :remote-method="queryWxUserByPhone"
                @change="queryWxUserChange"
              >
                <el-option
                  v-for="item in userData"
                  :key="item.phone"
                  :label="item.phone"
                  :value="item.phone"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="名称" prop="nickname">
              <el-input
                v-model="form.nickname"
                placeholder="请输入名称"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-for="(domain, index) in form.parkTags" :key="index">
          <el-col :span="10">
            <el-form-item
              label="所属车场"
              :prop="'parkTags.' + index + '.parkId'"
              :rules="{
                required: true,
                message: '请选择车场',
                trigger: 'blur',
              }"
            >
              <el-select v-model="domain.parkId" placeholder="请选择" clearable>
                <el-option
                  v-for="item in parkList"
                  :key="item.parkId"
                  :label="item.parkName"
                  :value="item.parkId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" :offset="1">
            <el-form-item
              label=""
              :prop="'parkTags.' + index + '.tagIds'"
              label-width="0"
              :rules="{
                required: true,
                message: '请选择标签',
                trigger: 'blur',
              }"
            >
              <el-select
                v-model="domain.tagIds"
                multiple
                placeholder="请选择标签"
                clearable
              >
                <el-option
                  v-for="item in tagList"
                  :key="item.id"
                  :label="item.tagName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" :offset="1">
            <el-button type="danger" plain icon="Delete" @click="del(index)"
              >删除</el-button
            >
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10" :offset="0">
            <el-button type="primary" plain icon="Plus" @click="add"
              >新增</el-button
            >
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Notice">
import { getParkStaff, getParkStaffBind,bindStaff,updateStaff,delStaff } from "@/api/system/park";
import { getTagList } from "@/api/common/tag";
import { listPark } from "@/api/system/park";
import { getWxUserByPhone } from "@/api/system/user";
const { proxy } = getCurrentInstance();
const route = useRoute();

const peopleList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const tagList = ref([]); // 标签列表
const parkList = ref([]); // 车场列表
const userData = ref([]); // 用户列表

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
  rules: {
    phone: [{ required: true, message: "请输入手机号", trigger: "blur" }],
    nickname: [{ required: true, message: "请输入名称", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  getParkStaff(route.query.parkId).then((response) => {
    peopleList.value = response.rows;
    total.value = parseInt(response.total);
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    tagName: undefined,
    orderBy: undefined,
    parkTags: [
      {
        parkId: "",
        tagIds: [],
      },
    ],
  };
  proxy.resetForm("parkRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "人员绑定";
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset();

  getParkStaffBind({
    phone: row.phone,
  }).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改人员";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["parkRef"].validate((valid) => {
    if (valid) {
      
      
      if (form.value.phone != undefined) {
        updateStaff(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        bindStaff(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
 
  proxy.$modal
    .confirm('是否确认删除标签编号为"' + row.id + '"的数据项？')
    .then(function () {
      return delStaff({
        parkId: route.query.parkId,
        phone: row.phone,
      });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
// 返回上一级
function handleBack() {
  proxy.$router.go(-1);
}
// 查询所有的标签列表
function queryTagList() {
  getTagList().then((response) => {
    tagList.value = response.data;
  });
}
// 查询所有的车场列表
function queryParkList() {
  listPark().then((response) => {
    parkList.value = response.rows;
  });
}
// 添加所属车场
function add() {
  form.value.parkTags.push({
    parkId: "",
    tagIds: [],
  });
}
// 删除所属车场
function del(index) {
  form.value.parkTags.splice(index, 1);
}

// 根据手机号查询微信用户
function queryWxUserByPhone(e) {
  getWxUserByPhone({
    phone: e,
  }).then((response) => {
    userData.value = response.rows;
  });
}
// 姓名的选择
function queryWxUserChange(e) {
  let item = userData.value.find((item) => item.phone == e);
  form.value.nickname = item.wechatNickName;
}
getList();
queryParkList();
queryTagList();
queryWxUserByPhone();
</script>
