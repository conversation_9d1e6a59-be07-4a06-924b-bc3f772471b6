import request from '@/utils/request'

// 查询标签
export function listTag(query) {
  return request({
    url: '/system/tag/list',
    method: 'get',
    params: query
  })
}

// 查询标签
export function getTag(noticeId) {
  return request({
    url: '/system/tag/' + noticeId,
    method: 'get'
  })
}

// 新增标签
export function addTag(data) {
  return request({
    url: '/system/tag',
    method: 'post',
    data: data
  })
}

// 修改标签
export function updateTag(data) {
  return request({
    url: '/system/tag',
    method: 'put',
    data: data
  })
}

// 删除公告
export function delTag(noticeId) {
  return request({
    url: '/system/tag/' + noticeId,
    method: 'delete'
  })
}
// 获取标签列表(不分页)
export function getTagList() {
  return request({
    url: '/system/tag/getList',
    method: 'get'
  })
}
