import{B as fe,d as me,u as _e,r as i,C as ge,F as ke,e as u,G as j,c as v,h as e,H as b,i as a,v as m,o as s,s as _,D as E,j as o,I as he,J as I,K as S,f as ve}from"./index-DDqcBwar.js";import{g as be,l as ye,a as we,d as Ce,u as Ve,b as Ie}from"./park-DZi9DkwV.js";import{g as Se}from"./tag-DOE35K0D.js";import{a as Ue}from"./user-C3QdTmTM.js";const Ne={class:"app-container"},Pe={class:"dialog-footer"},qe=fe({name:"Notice"}),De=Object.assign(qe,{setup(Be){const{proxy:g}=me(),D=_e(),R=i([]),c=i(!1),U=i(!0),N=i(!0),A=i([]),G=i(!0),H=i(!0),P=i(0),q=i(""),F=i([]),z=i([]),B=i([]),J=ge({form:{},queryParams:{pageNum:1,pageSize:10},rules:{phone:[{required:!0,message:"请输入手机号",trigger:"blur"}],nickname:[{required:!0,message:"请输入名称",trigger:"blur"}]}}),{queryParams:y,form:r,rules:K}=ke(J);function k(){U.value=!0,be(D.query.parkId).then(l=>{R.value=l.rows,P.value=l.total,U.value=!1})}function O(){c.value=!1,T()}function T(){r.value={id:void 0,tagName:void 0,orderBy:void 0,parkTags:[{parkId:"",tagIds:[]}]},g.resetForm("parkRef")}function Q(l){A.value=l.map(n=>n.noticeId),G.value=l.length!=1,H.value=!l.length}function M(){T(),c.value=!0,q.value="人员绑定"}function X(l){T(),we({phone:l.phone}).then(n=>{r.value=n.data,c.value=!0,q.value="修改人员"})}function Y(){g.$refs.parkRef.validate(l=>{l&&(r.value.phone!=null?Ve(r.value).then(n=>{g.$modal.msgSuccess("修改成功"),c.value=!1,k()}):Ie(r.value).then(n=>{g.$modal.msgSuccess("新增成功"),c.value=!1,k()}))})}function Z(l){g.$modal.confirm('是否确认删除标签编号为"'+l.id+'"的数据项？').then(function(){return Ce({parkId:D.query.parkId,phone:l.phone})}).then(()=>{k(),g.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ee(){g.$router.go(-1)}function ae(){Se().then(l=>{F.value=l.data})}function le(){ye().then(l=>{z.value=l.rows})}function te(){r.value.parkTags.push({parkId:"",tagIds:[]})}function ne(l){r.value.parkTags.splice(l,1)}function W(l){Ue({phone:l}).then(n=>{B.value=n.rows})}function oe(l){let n=B.value.find(p=>p.phone==l);r.value.nickname=n.wechatNickName}return k(),le(),ae(),W(),(l,n)=>{const p=u("el-button"),f=u("el-col"),re=u("right-toolbar"),w=u("el-row"),C=u("el-table-column"),ue=u("el-table"),se=u("pagination"),x=u("el-option"),$=u("el-select"),V=u("el-form-item"),de=u("el-input"),ie=u("el-form"),pe=u("el-dialog"),L=j("hasPermi"),ce=j("loading");return s(),v("div",Ne,[e(w,{gutter:10,class:"mb8"},{default:a(()=>[e(f,{span:1.5},{default:a(()=>[e(p,{type:"primary",plain:"",icon:"Back",onClick:ee},{default:a(()=>[m("返回")]),_:1})]),_:1}),e(f,{span:1.5},{default:a(()=>[b((s(),_(p,{type:"primary",plain:"",icon:"Plus",onClick:M},{default:a(()=>[m("新增")]),_:1})),[[L,["system:park:user:add"]]])]),_:1}),e(re,{showSearch:o(N),"onUpdate:showSearch":n[0]||(n[0]=t=>E(N)?N.value=t:null),onQueryTable:k},null,8,["showSearch"])]),_:1}),b((s(),_(ue,{data:o(R),onSelectionChange:Q},{default:a(()=>[e(C,{label:"序号",align:"center",type:"index",width:"100"}),e(C,{label:"名称",align:"center",prop:"username","show-overflow-tooltip":!0}),e(C,{label:"所属车场",align:"center",prop:"parkName"}),e(C,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:a(t=>[b((s(),_(p,{link:"",type:"primary",icon:"Edit",onClick:h=>X(t.row)},{default:a(()=>[m("修改")]),_:2},1032,["onClick"])),[[L,["system:park:user:update"]]]),b((s(),_(p,{link:"",type:"primary",icon:"Delete",onClick:h=>Z(t.row)},{default:a(()=>[m("删除")]),_:2},1032,["onClick"])),[[L,["system:park:user:delete"]]])]),_:1})]),_:1},8,["data"])),[[ce,o(U)]]),b(e(se,{total:o(P),page:o(y).pageNum,"onUpdate:page":n[1]||(n[1]=t=>o(y).pageNum=t),limit:o(y).pageSize,"onUpdate:limit":n[2]||(n[2]=t=>o(y).pageSize=t),onPagination:k},null,8,["total","page","limit"]),[[he,o(P)>0]]),e(pe,{title:o(q),modelValue:o(c),"onUpdate:modelValue":n[5]||(n[5]=t=>E(c)?c.value=t:null),width:"880px","append-to-body":""},{footer:a(()=>[ve("div",Pe,[e(p,{type:"primary",onClick:Y},{default:a(()=>[m("确 定")]),_:1}),e(p,{onClick:O},{default:a(()=>[m("取 消")]),_:1})])]),default:a(()=>[e(ie,{ref:"parkRef",model:o(r),rules:o(K),"label-width":"80px"},{default:a(()=>[e(w,null,{default:a(()=>[e(f,{span:24},{default:a(()=>[e(V,{label:"手机号",prop:"phone"},{default:a(()=>[e($,{modelValue:o(r).phone,"onUpdate:modelValue":n[3]||(n[3]=t=>o(r).phone=t),placeholder:"请选择",clearable:"","reserve-keyword":"",remote:"",filterable:"","remote-method":W,onChange:oe},{default:a(()=>[(s(!0),v(I,null,S(o(B),t=>(s(),_(x,{key:t.phone,label:t.phone,value:t.phone},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),e(f,{span:24},{default:a(()=>[e(V,{label:"名称",prop:"nickname"},{default:a(()=>[e(de,{modelValue:o(r).nickname,"onUpdate:modelValue":n[4]||(n[4]=t=>o(r).nickname=t),placeholder:"请输入名称",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),(s(!0),v(I,null,S(o(r).parkTags,(t,h)=>(s(),_(w,{key:h},{default:a(()=>[e(f,{span:10},{default:a(()=>[e(V,{label:"所属车场",prop:"parkTags."+h+".parkId",rules:{required:!0,message:"请选择车场",trigger:"blur"}},{default:a(()=>[e($,{modelValue:t.parkId,"onUpdate:modelValue":d=>t.parkId=d,placeholder:"请选择",clearable:""},{default:a(()=>[(s(!0),v(I,null,S(o(z),d=>(s(),_(x,{key:d.parkId,label:d.parkName,value:d.parkId},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:2},1024),e(f,{span:7,offset:1},{default:a(()=>[e(V,{label:"",prop:"parkTags."+h+".tagIds","label-width":"0",rules:{required:!0,message:"请选择标签",trigger:"blur"}},{default:a(()=>[e($,{modelValue:t.tagIds,"onUpdate:modelValue":d=>t.tagIds=d,multiple:"",placeholder:"请选择标签",clearable:""},{default:a(()=>[(s(!0),v(I,null,S(o(F),d=>(s(),_(x,{key:d.id,label:d.tagName,value:d.id},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["prop"])]),_:2},1024),e(f,{span:5,offset:1},{default:a(()=>[e(p,{type:"danger",plain:"",icon:"Delete",onClick:d=>ne(h)},{default:a(()=>[m("删除")]),_:2},1032,["onClick"])]),_:2},1024)]),_:2},1024))),128)),e(w,null,{default:a(()=>[e(f,{span:10,offset:0},{default:a(()=>[e(p,{type:"primary",plain:"",icon:"Plus",onClick:te},{default:a(()=>[m("新增")]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{De as default};
