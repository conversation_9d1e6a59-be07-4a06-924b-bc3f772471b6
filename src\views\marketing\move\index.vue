<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="logList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column label="手机号" align="center" prop="phone" />

      <el-table-column label="生成时间" align="center" prop="createdTime" />

      <!-- 操作 -->
      <el-table-column label="操作" align="center" width="150">
        <template #default="scope">
          <el-button type="text" size="small" @click="handleQrcode(scope.row)"
          v-hasPermi="['marketing:move:load']"
            >二维码</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog title="二维码" v-model="open" width="600px" append-to-body>
      <el-image
        style="width: 100%; height: 100%"
        :src="imageUrl"
        fit="contain"
        v-if="imageUrl"
      ></el-image>
      <div class="right-grid">
        <el-button type="primary" @click="downloadQrcode">下载二维码</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="Notice">
import { listMove } from "@/api/marketing/move";
import { saveAs } from "file-saver";
import request from "@/utils/request";
import { tansParams, blobValidate } from "@/utils/ruoyi";
const { proxy } = getCurrentInstance();

const logList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const imageUrl = ref(""); //二维码链接

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    tagName: undefined,
  },
  rules: {
    tagName: [{ required: true, message: "标签类型不能为空", trigger: "blur" }],
    orderBy: [{ required: true, message: "排序不能为空", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  listMove(queryParams.value).then((response) => {
    logList.value = response.rows;
    total.value = parseInt(response.total);
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    noticeId: undefined,
    noticeTitle: undefined,
    noticeType: undefined,
    noticeContent: undefined,
    status: "0",
  };
  proxy.resetForm("tagRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加标签";
}

//查询设备生成的二维码
function handleQrcode(row) {
  request
    .get(
      "/system/customer/qrCode/" + row.phone,
      {
        params: {
          // lockNo: row.lockNo,
        },
        responseType: "blob",
      },
      { isToken: true }
    )
    .then((res) => {
      const isBlob = blobValidate(res);
      if (isBlob) {
        let blob = new Blob([res], { type: "image/png" });
        imageUrl.value = URL.createObjectURL(blob);
        open.value = true;
      }
    })
    .catch(() => {});
}
// 下载二维码
function downloadQrcode() {
  if (imageUrl.value) {
    saveAs(imageUrl.value, "二维码.png");
  }
}

getList();
// 静态数据
//操作类型 value = "操作类型", allowableValues = "1, 2, 3"
const operate_type = [
  { label: "4G", value: "1" },
  { label: "蓝牙", value: "2" },
];
//设备状态 value = "设备状态", allowableValues = "0, 1, 2"
const status = [
  { label: "升锁", value: "1" },
  { label: "降锁", value: "2" },
];
</script>
