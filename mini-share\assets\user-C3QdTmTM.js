import{T as e,Y as u}from"./index-DDqcBwar.js";function n(t){return e({url:"/system/user/list",method:"get",params:t})}function o(t){return e({url:"/system/user/"+u(t),method:"get"})}function d(t){return e({url:"/system/user",method:"post",data:t})}function m(t){return e({url:"/system/user",method:"put",data:t})}function l(t){return e({url:"/system/user/"+t,method:"delete"})}function p(t,r){return e({url:"/system/user/resetPwd",method:"put",data:{userId:t,password:r}})}function i(t,r){return e({url:"/system/user/changeStatus",method:"put",data:{userId:t,status:r}})}function c(){return e({url:"/system/user/profile",method:"get"})}function f(t){return e({url:"/system/user/profile",method:"put",data:t})}function h(t,r){return e({url:"/system/user/profile/updatePwd",method:"put",data:{oldPassword:t,newPassword:r}})}function y(t){return e({url:"/system/user/profile/avatar",method:"post",headers:{"Content-Type":"application/x-www-form-urlencoded"},data:t})}function g(t){return e({url:"/system/user/authRole/"+t,method:"get"})}function P(t){return e({url:"/system/user/authRole",method:"put",params:t})}function U(){return e({url:"/system/user/deptTree",method:"get"})}function w(t){return e({url:"/system/customer/getCustomerList",method:"get",params:t})}function R(t,r){return e({url:"/system/user/profile/updatePayPwd",method:"put",data:{oldPassword:t,newPassword:r}})}export{w as a,g as b,y as c,f as d,h as e,R as f,c as g,U as h,o as i,i as j,l as k,n as l,m,d as n,p as r,P as u};
