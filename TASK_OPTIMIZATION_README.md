# 工单页面优化说明

## 优化内容

对 `src/views/task/task/index.vue` 页面进行了优化，实现了在提交工单时自动携带施工人员的 `userId` 字段。

## 问题背景

- **原问题**：施工人员选择器只提交 `phonenumber`（手机号），但后端需要同时获取对应的 `userId`
- **数据源**：`userData` 中包含 `userId`、`phonenumber`、`nickName` 等字段
- **需求**：在提交表单时，除了 `executor`（手机号）外，还要自动提交对应的 `executorUserId`

## 实现方案

### 1. 添加选择变化监听器

在施工人员选择器上添加了 `@change="handleExecutorChange"` 事件监听：

```vue
<el-select
  v-model="form.executor"
  placeholder="请选择"
  clearable
  @change="handleExecutorChange"
>
```

### 2. 实现自动设置 userId 的处理函数

```javascript
/** 施工人员选择变化处理 - 自动设置对应的userId */
function handleExecutorChange(phonenumber) {
  if (phonenumber) {
    // 根据选择的手机号找到对应的用户信息，自动设置userId
    const selectedUser = userData.value.find(user => user.phonenumber === phonenumber);
    if (selectedUser) {
      form.value.executorUserId = selectedUser.userId;
      console.log('自动设置施工人员userId:', selectedUser.userId, '手机号:', phonenumber);
    }
  } else {
    // 清空选择时也清空userId
    form.value.executorUserId = undefined;
  }
}
```

### 3. 更新表单重置逻辑

在 `reset()` 函数中添加了新字段的重置：

```javascript
form.value = {
  // ... 其他字段
  // 重置施工人员相关字段
  executor: undefined,
  executorUserId: undefined,
};
```

### 4. 提交时的数据结构

提交时 `form.value` 将包含：
- `executor`: 施工人员手机号
- `executorUserId`: 施工人员的用户ID

## 优化特点

1. **最小改动**：只修改了必要的部分，保持原有代码结构
2. **自动化**：用户选择施工人员时自动设置对应的 userId，无需手动操作
3. **容错性**：处理了清空选择的情况
4. **可维护性**：
   - 添加了详细的注释说明
   - 使用了清晰的函数命名
   - 保留了 console.log 用于调试
5. **向后兼容**：不影响现有的表单验证和其他功能

## 测试建议

1. **功能测试**：
   - 选择不同的施工人员，检查控制台输出的 userId 是否正确
   - 清空选择，确认 userId 也被清空
   - 提交表单，验证后端是否收到了 `executorUserId` 字段

2. **边界测试**：
   - 测试 userData 为空的情况
   - 测试选择不存在的用户的情况

## 注意事项

- 确保后端 API 能够正确处理新增的 `executorUserId` 字段
- 如果后端字段名不同，请相应修改 `form.value.executorUserId` 中的字段名
- 建议在生产环境中移除或调整 console.log 语句
