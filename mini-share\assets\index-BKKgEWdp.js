import{B as H,d as J,r as s,C as O,F as W,e as u,G as N,c as v,o as m,H as g,I as R,j as l,h as e,i as o,k as b,J as U,K as q,v as h,s as B,t as X,E as Z}from"./index-DDqcBwar.js";import{l as ee,p as ae}from"./statements-Nia77kYU.js";const te={class:"app-container"},le=H({name:"Notice"}),ie=Object.assign(le,{setup(ne){const{proxy:w}=J(),T=s([]);s(!1);const _=s(!0),I=s(!0),K=s([]),P=s(!0),z=s(!0),f=s(0);s("");const A=O({form:{},queryParams:{pageNum:1,pageSize:10,bankNum:void 0,paymentStatus:void 0,userType:void 0,dataRangeTime:void 0},rules:{tagName:[{required:!0,message:"标签类型不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序不能为空",trigger:"blur"}]}}),{queryParams:t,form:oe,rules:ue}=W(A);function c(){_.value=!0,t.value.dataRangeTime?(t.value.startApplyTime=t.value.dataRangeTime[0],t.value.endApplyTime=t.value.dataRangeTime[1]):(t.value.startApplyTime=void 0,t.value.endApplyTime=void 0),ee(t.value).then(p=>{T.value=p.rows,f.value=p.total,_.value=!1})}function i(){t.value.pageNum=1,c()}function D(){w.resetForm("queryRef"),i()}function M(p){K.value=p.map(n=>n.noticeId),P.value=p.length!=1,z.value=!p.length}function Y(p){Z.confirm("请核对打款账户是否正，且打款已完成，更改后状态不可更变。","提示",{type:"warning"}).then(()=>{ae({withdrawalId:p.withdrawalId,paymentStatus:"1"}).then(n=>{w.$modal.msgSuccess("打款成功"),c()})}).catch(()=>{})}c();const S=[{label:"用户",value:"1"},{label:"代理商",value:"2"}],k=[{label:"已打款",value:"1"},{label:"待打款",value:"2"}];return(p,n)=>{const E=u("el-input"),d=u("el-form-item"),V=u("el-option"),C=u("el-select"),F=u("el-date-picker"),y=u("el-button"),L=u("el-form"),r=u("el-table-column"),x=u("dict-tag"),j=u("el-table"),Q=u("pagination"),$=N("hasPermi"),G=N("loading");return m(),v("div",te,[g(e(L,{model:l(t),ref:"queryRef",inline:!0},{default:o(()=>[e(d,{label:"账号编号",prop:"phone"},{default:o(()=>[e(E,{modelValue:l(t).phone,"onUpdate:modelValue":n[0]||(n[0]=a=>l(t).phone=a),placeholder:"请输入账号编号",clearable:"",style:{width:"200px"},onKeyup:b(i,["enter"])},null,8,["modelValue"])]),_:1}),e(d,{label:"状态",prop:"paymentStatus"},{default:o(()=>[e(C,{modelValue:l(t).paymentStatus,"onUpdate:modelValue":n[1]||(n[1]=a=>l(t).paymentStatus=a),placeholder:"请选择提现状态",style:{width:"200px"},onKeyup:b(i,["enter"])},{default:o(()=>[(m(),v(U,null,q(k,a=>e(V,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(d,{label:"申请时间",prop:"dataRangeTime"},{default:o(()=>[e(F,{modelValue:l(t).dataRangeTime,"onUpdate:modelValue":n[2]||(n[2]=a=>l(t).dataRangeTime=a),"value-format":"YYYY-MM-DD ",type:"daterange","range-separator":"To","start-placeholder":"开始时间","end-placeholder":"结束时间"},null,8,["modelValue"])]),_:1}),e(d,{label:"用户类型",prop:"userType"},{default:o(()=>[e(C,{modelValue:l(t).userType,"onUpdate:modelValue":n[3]||(n[3]=a=>l(t).userType=a),placeholder:"请选择用户类型",style:{width:"200px"},onKeyup:b(i,["enter"])},{default:o(()=>[(m(),v(U,null,q(S,a=>e(V,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(d,null,{default:o(()=>[e(y,{type:"primary",icon:"Search",onClick:i},{default:o(()=>[h("搜索")]),_:1}),e(y,{icon:"Refresh",onClick:D},{default:o(()=>[h("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[R,l(I)]]),g((m(),B(j,{data:l(T),onSelectionChange:M},{default:o(()=>[e(r,{label:"序号",align:"center",type:"index",width:"100"}),e(r,{label:"账号",align:"center",prop:"phone"}),e(r,{label:"姓名",align:"center",prop:"username"}),e(r,{label:"类型",align:"center",prop:"userType"},{default:o(a=>[e(x,{value:a.row.userType,options:S},null,8,["value"])]),_:1}),e(r,{label:"申请时间",align:"center",prop:"operateTime"}),e(r,{label:"提现账户",align:"center",prop:"bankNum"}),e(r,{label:"提现银行",align:"center",prop:"bank"}),e(r,{label:"提现金额",align:"center",prop:"amount"}),e(r,{label:"状态",align:"center",prop:"paymentStatus"},{default:o(a=>[e(x,{value:a.row.paymentStatus,options:k},null,8,["value"])]),_:1}),e(r,{label:"操作",align:"center",width:"150"},{default:o(a=>[a.row.paymentStatus=="2"?g((m(),B(y,{key:0,type:"text",size:"small",onClick:re=>Y(a.row)},{default:o(()=>[h("打款完成")]),_:2},1032,["onClick"])),[[$,["finance:withdrawal:update"]]]):X("",!0)]),_:1})]),_:1},8,["data"])),[[G,l(_)]]),g(e(Q,{total:l(f),page:l(t).pageNum,"onUpdate:page":n[4]||(n[4]=a=>l(t).pageNum=a),limit:l(t).pageSize,"onUpdate:limit":n[5]||(n[5]=a=>l(t).pageSize=a),onPagination:c},null,8,["total","page","limit"]),[[R,l(f)>0]])])}}});export{ie as default};
