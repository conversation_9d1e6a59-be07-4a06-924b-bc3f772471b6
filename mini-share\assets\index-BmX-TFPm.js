import{B as X,d as Z,u as ee,r as u,C as le,F as ae,e as d,G as S,c as U,H as g,h as e,I,j as t,i as o,k as _,o as v,J as te,K as ne,v as c,s as q,A as T,D as oe}from"./index-DDqcBwar.js";import{_ as re,l as de,a as ie}from"./oderDetails-Dn2_t_eq.js";import"./statements-Nia77kYU.js";const ue={class:"app-container"},pe=X({name:"Notice"}),ve=Object.assign(pe,{setup(se){const{proxy:V}=Z(),k=ee(),N=u([]),f=u(!1),b=u(!0),B=u(!0),D=u([]),K=u(!0),P=u(!0),h=u(0),x=u({}),A=le({form:{},queryParams:{pageNum:1,pageSize:10,orderNo:void 0,phone:void 0,parkName:void 0,plateNo:void 0,status:void 0,dataRangeTime:void 0},rules:{}}),{queryParams:a,form:F,rules:me}=ae(A);function y(){var n;b.value=!0;const i=k.query.lotId?de:ie;((n=a.value.dataRangeTime)==null?void 0:n.length)>0?(a.value.orderBeginTime=a.value.dataRangeTime[0],a.value.orderEndTime=a.value.dataRangeTime[1]):(a.value.orderBeginTime=void 0,a.value.orderEndTime=void 0),i({...a.value,lotId:k.query.lotId}).then(s=>{N.value=s.rows,h.value=s.total,b.value=!1})}function L(){F.value={noticeId:void 0,noticeTitle:void 0,noticeType:void 0,noticeContent:void 0,status:"0"},V.resetForm("tagRef")}function p(){a.value.pageNum=1,y()}function Y(){V.resetForm("queryRef"),a.value.dataRangeTime=void 0,p()}function z(i){D.value=i.map(n=>n.noticeId),K.value=i.length!=1,P.value=!i.length}function E(i){L(),x.value=i,f.value=!0}y();const O=[{label:"普通计费",value:"1"},{label:"特殊计费",value:"2"}],R=[{label:"待交押金",value:"-1"},{label:"已交押金待入场",value:"0"},{label:"已入场",value:"3"},{label:"已完成",value:"1"},{label:"已取消(主动)",value:"2"},{label:"已取消(自动取消)",value:"4"},{label:"待补缴",value:"5"}];return(i,n)=>{const s=d("el-input"),m=d("el-form-item"),j=d("el-date-picker"),M=d("el-option"),Q=d("el-select"),w=d("el-button"),$=d("el-form"),r=d("el-table-column"),C=d("dict-tag"),G=d("el-table"),H=d("pagination"),J=S("hasPermi"),W=S("loading");return v(),U("div",ue,[g(e($,{model:t(a),ref:"queryRef",inline:!0},{default:o(()=>[e(m,{label:"订单号",prop:"orderNo"},{default:o(()=>[e(s,{modelValue:t(a).orderNo,"onUpdate:modelValue":n[0]||(n[0]=l=>t(a).orderNo=l),placeholder:"请输入订单号",clearable:"",style:{width:"200px"},onKeyup:_(p,["enter"])},null,8,["modelValue"])]),_:1}),e(m,{label:"手机号",prop:"phone"},{default:o(()=>[e(s,{modelValue:t(a).phone,"onUpdate:modelValue":n[1]||(n[1]=l=>t(a).phone=l),placeholder:"请输入手机号",clearable:"",style:{width:"200px"},onKeyup:_(p,["enter"])},null,8,["modelValue"])]),_:1}),e(m,{label:"车场",prop:"parkName"},{default:o(()=>[e(s,{modelValue:t(a).parkName,"onUpdate:modelValue":n[2]||(n[2]=l=>t(a).parkName=l),placeholder:"请输入车场名称",clearable:"",style:{width:"200px"},onKeyup:_(p,["enter"])},null,8,["modelValue"])]),_:1}),e(m,{label:"下单时间",prop:"dataRangeTime"},{default:o(()=>[e(j,{modelValue:t(a).dataRangeTime,"onUpdate:modelValue":n[3]||(n[3]=l=>t(a).dataRangeTime=l),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"To","start-placeholder":"开始时间","end-placeholder":"结束时间"},null,8,["modelValue"])]),_:1}),e(m,{label:"状态",prop:"status"},{default:o(()=>[e(Q,{modelValue:t(a).status,"onUpdate:modelValue":n[4]||(n[4]=l=>t(a).status=l),placeholder:"请选择订单状态",style:{width:"200px"},onKeyup:_(p,["enter"])},{default:o(()=>[(v(),U(te,null,ne(R,l=>e(M,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(m,null,{default:o(()=>[e(w,{type:"primary",icon:"Search",onClick:p},{default:o(()=>[c("搜索")]),_:1}),e(w,{icon:"Refresh",onClick:Y},{default:o(()=>[c("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[I,t(B)]]),g((v(),q(G,{data:t(N),onSelectionChange:z},{default:o(()=>[e(r,{label:"序号",align:"center",type:"index",width:"100"}),e(r,{label:"订单号",align:"center",prop:"orderNo",width:"300px"}),e(r,{label:"订单类型",align:"center",prop:"orderType"},{default:o(l=>[e(C,{options:O,value:l.row.orderType},null,8,["value"])]),_:1}),e(r,{label:"手机号",align:"center",prop:"phone",width:"200px"}),e(r,{label:"车牌号",align:"center",prop:"plateNo",width:"100px"}),e(r,{label:"车场",align:"center",prop:"parkName",width:"200px"}),e(r,{label:"地址",align:"center",prop:"parkAddress"}),e(r,{label:"区域",align:"center",prop:"location"}),e(r,{label:"车位号",align:"center",prop:"code"}),e(r,{label:"下单时间",align:"center",prop:"orderTime",width:"200px"},{default:o(l=>[c(T(i.parseTime(l.row.orderTime)),1)]),_:1}),e(r,{label:"结束时间",align:"center",prop:"finishTime",width:"200px"},{default:o(l=>[c(T(i.parseTime(l.row.finishTime)),1)]),_:1}),e(r,{label:"应收金额",align:"center",prop:"payableAmount",width:"120"},{default:o(l=>[c(T(l.row.payableAmount||0)+"元 ",1)]),_:1}),e(r,{label:"状态",align:"center",prop:"status",width:"120"},{default:o(l=>[e(C,{options:R,value:l.row.status+""},null,8,["value"])]),_:1}),e(r,{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"150",fixed:"right"},{default:o(l=>[g((v(),q(w,{link:"",type:"primary",onClick:ce=>E(l.row)},{default:o(()=>[c("查看详情")]),_:2},1032,["onClick"])),[[J,["device:order:query"]]])]),_:1})]),_:1},8,["data"])),[[W,t(b)]]),g(e(H,{total:t(h),page:t(a).pageNum,"onUpdate:page":n[5]||(n[5]=l=>t(a).pageNum=l),limit:t(a).pageSize,"onUpdate:limit":n[6]||(n[6]=l=>t(a).pageSize=l),onPagination:y},null,8,["total","page","limit"]),[[I,t(h)>0]]),e(re,{modelValue:t(f),"onUpdate:modelValue":n[7]||(n[7]=l=>oe(f)?f.value=l:null),item:t(x),type:"2"},null,8,["modelValue","item"])])}}});export{ve as default};
