import{T as b,B as me,d as pe,r,C as ge,F as fe,e as s,G as A,c as V,o as i,H as h,h as t,I as E,j as n,i as a,k as _e,v,s as y,D as G,J as L,K as T,f as ve}from"./index-DDqcBwar.js";function he(d){return b({url:"/system/alert/list",method:"get",params:d})}function ye(d){return b({url:"/system/alert/get",method:"get",params:d})}function be(){return b({url:"/system/tag/getList",method:"get"})}function we(d){return b({url:"/system/alert/getBy/"+d,method:"get"})}function Ie(d){return b({url:"/system/alert/add",method:"post",data:d})}function ke(d){return b({url:"/system/alert/update",method:"put",data:d})}const Ce={class:"app-container"},Ve={class:"dialog-footer"},Ne=me({name:"Notice"}),xe=Object.assign(Ne,{setup(d){const{proxy:p}=pe(),W=r([]),D=r([]),F=r([]),m=r(!1),N=r(!0),k=r(!0),S=r([]),H=r(!0),J=r(!0),x=r(0),U=r(""),O=ge({form:{},queryParams:{pageNum:1,pageSize:10,tagName:void 0},rules:{tagId:[{required:!0,message:"标签类型不能为空",trigger:"change"}],userIds:[{required:!0,message:"人员不能为空",trigger:"change"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}),{queryParams:c,form:u,rules:M}=fe(O);function g(){N.value=!0,he(c.value).then(o=>{D.value=o.rows,x.value=o.total,N.value=!1})}function X(){m.value=!1,R()}function R(){u.value={status:void 0,tagId:void 0,userIds:[]},p.resetForm("tagRef")}function q(){c.value.pageNum=1,g()}function Y(){p.resetForm("queryRef"),q()}function Z(o){S.value=o.map(l=>l.noticeId),H.value=o.length!=1,J.value=!o.length}function ee(){R(),m.value=!0,U.value="添加预警人员"}function te(o){R();const l=o.id||S.value;we(l).then(async w=>{u.value=w.data,m.value=!0,U.value="修改预警人员",u.value.userIds=[],await z(w.data.tagId);for(var f in w.data.sysUsers)u.value.userIds.push(f);console.log(u.value.userIds)})}function ae(){p.$refs.tagRef.validate(o=>{o&&(u.value.id!=null?ke(u.value).then(l=>{p.$modal.msgSuccess("修改成功"),m.value=!1,g()}):Ie(u.value).then(l=>{p.$modal.msgSuccess("新增成功"),m.value=!1,g()}))})}function le(o){const l=o.id||S.value;p.$modal.confirm('是否确认删预警编号为"'+l+'"的数据项？').then(function(){return delWarning(l)}).then(()=>{g(),p.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ne(){be().then(o=>{W.value=o.data})}async function z(o){return ye({tagId:o}).then(l=>{F.value=l.data})}ne();const K=[{label:"正常",value:"0"},{label:"异常",value:"1"}];return g(),(o,l)=>{const w=s("el-input"),f=s("el-form-item"),_=s("el-button"),Q=s("el-form"),C=s("el-col"),oe=s("right-toolbar"),j=s("el-row"),I=s("el-table-column"),ue=s("dict-tag"),se=s("el-table"),re=s("pagination"),B=s("el-option"),P=s("el-select"),de=s("el-dialog"),$=A("hasPermi"),ie=A("loading");return i(),V("div",Ce,[h(t(Q,{model:n(c),ref:"queryRef",inline:!0},{default:a(()=>[t(f,{label:"标签名称",prop:"tagName"},{default:a(()=>[t(w,{modelValue:n(c).tagName,"onUpdate:modelValue":l[0]||(l[0]=e=>n(c).tagName=e),placeholder:"请输入标签名称",clearable:"",style:{width:"200px"},onKeyup:_e(q,["enter"])},null,8,["modelValue"])]),_:1}),t(f,null,{default:a(()=>[t(_,{type:"primary",icon:"Search",onClick:q},{default:a(()=>[v("搜索")]),_:1}),t(_,{icon:"Refresh",onClick:Y},{default:a(()=>[v("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[E,n(k)]]),t(j,{gutter:10,class:"mb8"},{default:a(()=>[t(C,{span:1.5},{default:a(()=>[h((i(),y(_,{type:"primary",plain:"",icon:"Plus",onClick:ee},{default:a(()=>[v("新增")]),_:1})),[[$,["system:alert:add"]]])]),_:1}),t(oe,{showSearch:n(k),"onUpdate:showSearch":l[1]||(l[1]=e=>G(k)?k.value=e:null),onQueryTable:g},null,8,["showSearch"])]),_:1}),h((i(),y(se,{data:n(D),onSelectionChange:Z},{default:a(()=>[t(I,{label:"序号",align:"center",type:"index",width:"100"}),t(I,{label:"标签名称",align:"center",prop:"tagName","show-overflow-tooltip":!0}),t(I,{label:"用户昵称",align:"center",prop:"nicknames"}),t(I,{label:"状态",align:"center",prop:"status"},{default:a(e=>[t(ue,{value:e.row.status,options:K},null,8,["value"])]),_:1}),t(I,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:a(e=>[h((i(),y(_,{link:"",type:"primary",icon:"Edit",onClick:ce=>te(e.row)},{default:a(()=>[v("修改")]),_:2},1032,["onClick"])),[[$,["system:alert:update"]]]),h((i(),y(_,{link:"",type:"primary",icon:"Delete",onClick:ce=>le(e.row)},{default:a(()=>[v("删除")]),_:2},1032,["onClick"])),[[$,["system:alert:delete"]]])]),_:1})]),_:1},8,["data"])),[[ie,n(N)]]),h(t(re,{total:n(x),page:n(c).pageNum,"onUpdate:page":l[2]||(l[2]=e=>n(c).pageNum=e),limit:n(c).pageSize,"onUpdate:limit":l[3]||(l[3]=e=>n(c).pageSize=e),onPagination:g},null,8,["total","page","limit"]),[[E,n(x)>0]]),t(de,{title:n(U),modelValue:n(m),"onUpdate:modelValue":l[7]||(l[7]=e=>G(m)?m.value=e:null),width:"580px","append-to-body":""},{footer:a(()=>[ve("div",Ve,[t(_,{type:"primary",onClick:ae},{default:a(()=>[v("确 定")]),_:1}),t(_,{onClick:X},{default:a(()=>[v("取 消")]),_:1})])]),default:a(()=>[t(Q,{ref:"tagRef",model:n(u),rules:n(M),"label-width":"80px"},{default:a(()=>[t(j,null,{default:a(()=>[t(C,{span:24},{default:a(()=>[t(f,{label:"部门标签",prop:"tagId"},{default:a(()=>[t(P,{modelValue:n(u).tagId,"onUpdate:modelValue":l[4]||(l[4]=e=>n(u).tagId=e),placeholder:"请选择部门标签",clearable:"",onChange:z},{default:a(()=>[(i(!0),V(L,null,T(n(W),e=>(i(),y(B,{key:e.tagId,label:e.tagName,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(C,{span:24},{default:a(()=>[t(f,{label:"人员",prop:"userIds"},{default:a(()=>[t(P,{modelValue:n(u).userIds,"onUpdate:modelValue":l[5]||(l[5]=e=>n(u).userIds=e),placeholder:"请选择人员",clearable:"",multiple:""},{default:a(()=>[(i(!0),V(L,null,T(n(F),e=>(i(),y(B,{key:e.userId,label:e.nickName,value:e.userId+""},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),t(C,{span:24},{default:a(()=>[t(f,{label:"状态",prop:"status"},{default:a(()=>[t(P,{modelValue:n(u).status,"onUpdate:modelValue":l[6]||(l[6]=e=>n(u).status=e),placeholder:"请选择状态"},{default:a(()=>[(i(),V(L,null,T(K,e=>t(B,{key:e.value,label:e.label,value:Number(e.value)},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{xe as default};
