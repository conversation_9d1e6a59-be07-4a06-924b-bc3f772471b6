<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Back" @click="handleBack"
          >返回</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:park:bind:add']"
          >新增</el-button
        >
      </el-col>

      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="peopleList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column
        label="名称"
        align="center"
        prop="username"
        :show-overflow-tooltip="true"
      />

      <!-- <el-table-column label="标签" align="center" prop="tagNames" /> -->
      <el-table-column label="所属车场" align="center" prop="parkName" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:park:bind:update']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:park:bind:delete']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" v-model="open" width="880px" append-to-body>
      <el-form ref="parkRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="手机号" prop="phone">
              <el-select
                v-model="form.phone"
                placeholder="请选择"
                clearable
                @change="queryWxUserChange"
              >
                <el-option
                  v-for="item in userData"
                  :key="item.phonenumber"
                  :label="item.phonenumber"
                  :value="item.phonenumber"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="名称" prop="nickname">
              <el-input
                v-model="form.nickname"
                placeholder="请输入名称"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Notice">
import {
  getAdminStaff,
  getAdminStaffBind,
  addAdminStaff,
  updateAdminStaff,
  getParkStaffBind,

  listParkAdmin,
  delParkAdmin
} from "@/api/system/park";
import { getTagList } from "@/api/common/tag";
import { listPark } from "@/api/system/park";

import { listUser } from "@/api/system/user";
const { proxy } = getCurrentInstance();
const route = useRoute();

const peopleList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const tagList = ref([]); // 标签列表
const parkList = ref([]); // 车场列表
const userData = ref([]); // 用户列表

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
  rules: {
    phone: [{ required: true, message: "请输入手机号", trigger: "blur" }],
    nickname: [{ required: true, message: "请输入名称", trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  getAdminStaff(route.query.parkId).then((response) => {
    peopleList.value = response.data;
    total.value = response.data.length;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    tagName: undefined,
    orderBy: undefined,
    parkTags: [
      {
        parkId: "",
        tagIds: [],
      },
    ],
  };
  proxy.resetForm("parkRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "人员绑定";
}

/**修改按钮操作 */
function handleUpdate(row) {
  reset();

  getParkStaffBind({
    phone: row.phone,
  }).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改人员";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["parkRef"].validate((valid) => {
    if (valid) {
      if (form.value.parkId != undefined) {
        updateAdminStaff({
          parkId: route.query.parkId,

          userId: form.value.userId,
        }).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addAdminStaff({
          parkId: route.query.parkId,
          userId: form.value.userId,
        }).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除为"' + row.username + '"的数据项？')
    .then(function () {
      return delParkAdmin({
        parkId: row.parkId,
        userId: row.userId,
      });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
// 返回上一级
function handleBack() {
  proxy.$router.go(-1);
}

// 根据手机号查询微信用户
function queryWxUserByPhone(e) {
  listParkAdmin({
    phonenumber: e,
  }).then((response) => {
    userData.value = response.data;
  });
}
// 姓名的选择
function queryWxUserChange(e) {
  let item = userData.value.find((item) => item.phonenumber == e);
  form.value.nickname = item.nickName;
  form.value.userId = item.userId;
}
getList();

queryWxUserByPhone();
</script>
