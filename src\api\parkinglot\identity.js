import request from '@/utils/request'
// 身份审核列表
export function listIdentity(query) {
  return request({
    url: '/park/verify/list',
    method: 'get',
    params: query
  })
}
// 身份审核详情
export function getIdentity(id) {
  return request({
    url: '/park/verify/' + id,
    method: 'get'
  })
}
// 身份审核通过
export function passIdentity(data) {
  return request({
    url: '/park/verify/identity' ,
    method: 'put',
    data
  })
}