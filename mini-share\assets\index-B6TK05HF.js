import{T as k,B as ce,d as fe,r as f,C as _e,F as he,e as o,G as K,c as R,o as p,H as b,h as e,t as q,I as ve,j as a,i as t,k as ye,J as Q,K as j,s as _,v as i,D as O,f as G,A as H,L as ge}from"./index-DDqcBwar.js";function J(u){return k({url:"/system/dept/list",method:"get",params:u})}function be(u){return k({url:"/system/dept/list/exclude/"+u,method:"get"})}function ke(u){return k({url:"/system/dept/"+u,method:"get"})}function Ve(u){return k({url:"/system/dept",method:"post",data:u})}function we(u){return k({url:"/system/dept",method:"put",data:u})}function Ie(u){return k({url:"/system/dept/"+u,method:"delete"})}const Ne={class:"app-container"},Ce={class:"dialog-footer"},xe=ce({name:"Dept"}),Se=Object.assign(xe,{setup(u){const{proxy:s}=fe(),{sys_normal_disable:N}=s.useDict("sys_normal_disable"),P=f([]),c=f(!1),C=f(!0),w=f(!0),x=f(""),D=f([]),S=f(!0),T=f(!0),z=_e({form:{},queryParams:{deptName:void 0,status:void 0},rules:{parentId:[{required:!0,message:"上级部门不能为空",trigger:"blur"}],deptName:[{required:!0,message:"部门名称不能为空",trigger:"blur"}],orderNum:[{required:!0,message:"显示排序不能为空",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:["blur","change"]}],phone:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}}),{queryParams:v,form:d,rules:M}=he(z);function y(){C.value=!0,J(v.value).then(r=>{P.value=s.handleTree(r.data,"deptId"),C.value=!1})}function W(){c.value=!1,U()}function U(){d.value={deptId:void 0,parentId:void 0,deptName:void 0,orderNum:0,leader:void 0,phone:void 0,email:void 0,status:"0"},s.resetForm("deptRef")}function $(){y()}function X(){s.resetForm("queryRef"),$()}function B(r){U(),J().then(n=>{D.value=s.handleTree(n.data,"deptId")}),r!=null&&(d.value.parentId=r.deptId),c.value=!0,x.value="添加部门"}function Y(){T.value=!1,S.value=!S.value,ge(()=>{T.value=!0})}function Z(r){U(),be(r.deptId).then(n=>{D.value=s.handleTree(n.data,"deptId")}),ke(r.deptId).then(n=>{d.value=n.data,c.value=!0,x.value="修改部门"})}function ee(){s.$refs.deptRef.validate(r=>{r&&(d.value.deptId!=null?we(d.value).then(n=>{s.$modal.msgSuccess("修改成功"),c.value=!1,y()}):Ve(d.value).then(n=>{s.$modal.msgSuccess("新增成功"),c.value=!1,y()}))})}function te(r){s.$modal.confirm('是否确认删除名称为"'+r.deptName+'"的数据项?').then(function(){return Ie(r.deptId)}).then(()=>{y(),s.$modal.msgSuccess("删除成功")}).catch(()=>{})}return y(),(r,n)=>{const E=o("el-input"),h=o("el-form-item"),le=o("el-option"),ae=o("el-select"),m=o("el-button"),F=o("el-form"),g=o("el-col"),ne=o("right-toolbar"),A=o("el-row"),V=o("el-table-column"),oe=o("dict-tag"),de=o("el-table"),re=o("el-tree-select"),ue=o("el-input-number"),se=o("el-radio"),pe=o("el-radio-group"),ie=o("el-dialog"),I=K("hasPermi"),me=K("loading");return p(),R("div",Ne,[b(e(F,{model:a(v),ref:"queryRef",inline:!0},{default:t(()=>[e(h,{label:"部门名称",prop:"deptName"},{default:t(()=>[e(E,{modelValue:a(v).deptName,"onUpdate:modelValue":n[0]||(n[0]=l=>a(v).deptName=l),placeholder:"请输入部门名称",clearable:"",style:{width:"200px"},onKeyup:ye($,["enter"])},null,8,["modelValue"])]),_:1}),e(h,{label:"状态",prop:"status"},{default:t(()=>[e(ae,{modelValue:a(v).status,"onUpdate:modelValue":n[1]||(n[1]=l=>a(v).status=l),placeholder:"部门状态",clearable:"",style:{width:"200px"}},{default:t(()=>[(p(!0),R(Q,null,j(a(N),l=>(p(),_(le,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(h,null,{default:t(()=>[e(m,{type:"primary",icon:"Search",onClick:$},{default:t(()=>[i("搜索")]),_:1}),e(m,{icon:"Refresh",onClick:X},{default:t(()=>[i("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ve,a(w)]]),e(A,{gutter:10,class:"mb8"},{default:t(()=>[e(g,{span:1.5},{default:t(()=>[b((p(),_(m,{type:"primary",plain:"",icon:"Plus",onClick:B},{default:t(()=>[i("新增")]),_:1})),[[I,["system:dept:add"]]])]),_:1}),e(g,{span:1.5},{default:t(()=>[e(m,{type:"info",plain:"",icon:"Sort",onClick:Y},{default:t(()=>[i("展开/折叠")]),_:1})]),_:1}),e(ne,{showSearch:a(w),"onUpdate:showSearch":n[2]||(n[2]=l=>O(w)?w.value=l:null),onQueryTable:y},null,8,["showSearch"])]),_:1}),a(T)?b((p(),_(de,{key:0,data:a(P),"row-key":"deptId","default-expand-all":a(S),"tree-props":{children:"children",hasChildren:"hasChildren"}},{default:t(()=>[e(V,{prop:"deptName",label:"部门名称",width:"260"}),e(V,{prop:"orderNum",label:"排序",width:"200"}),e(V,{prop:"status",label:"状态",width:"100"},{default:t(l=>[e(oe,{options:a(N),value:l.row.status},null,8,["options","value"])]),_:1}),e(V,{label:"创建时间",align:"center",prop:"createTime",width:"200"},{default:t(l=>[G("span",null,H(r.parseTime(l.row.createTime)),1)]),_:1}),e(V,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:t(l=>[b((p(),_(m,{link:"",type:"primary",icon:"Edit",onClick:L=>Z(l.row)},{default:t(()=>[i("修改")]),_:2},1032,["onClick"])),[[I,["system:dept:edit"]]]),b((p(),_(m,{link:"",type:"primary",icon:"Plus",onClick:L=>B(l.row)},{default:t(()=>[i("新增")]),_:2},1032,["onClick"])),[[I,["system:dept:add"]]]),l.row.parentId!=0?b((p(),_(m,{key:0,link:"",type:"primary",icon:"Delete",onClick:L=>te(l.row)},{default:t(()=>[i("删除")]),_:2},1032,["onClick"])),[[I,["system:dept:remove"]]]):q("",!0)]),_:1})]),_:1},8,["data","default-expand-all"])),[[me,a(C)]]):q("",!0),e(ie,{title:a(x),modelValue:a(c),"onUpdate:modelValue":n[7]||(n[7]=l=>O(c)?c.value=l:null),width:"600px","append-to-body":""},{footer:t(()=>[G("div",Ce,[e(m,{type:"primary",onClick:ee},{default:t(()=>[i("确 定")]),_:1}),e(m,{onClick:W},{default:t(()=>[i("取 消")]),_:1})])]),default:t(()=>[e(F,{ref:"deptRef",model:a(d),rules:a(M),"label-width":"80px"},{default:t(()=>[e(A,null,{default:t(()=>[a(d).parentId!==0?(p(),_(g,{key:0,span:24},{default:t(()=>[e(h,{label:"上级部门",prop:"parentId"},{default:t(()=>[e(re,{modelValue:a(d).parentId,"onUpdate:modelValue":n[3]||(n[3]=l=>a(d).parentId=l),data:a(D),props:{value:"deptId",label:"deptName",children:"children"},"value-key":"deptId",placeholder:"选择上级部门","check-strictly":""},null,8,["modelValue","data"])]),_:1})]),_:1})):q("",!0),e(g,{span:24},{default:t(()=>[e(h,{label:"部门名称",prop:"deptName"},{default:t(()=>[e(E,{modelValue:a(d).deptName,"onUpdate:modelValue":n[4]||(n[4]=l=>a(d).deptName=l),placeholder:"请输入部门名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:t(()=>[e(h,{label:"显示排序",prop:"orderNum"},{default:t(()=>[e(ue,{modelValue:a(d).orderNum,"onUpdate:modelValue":n[5]||(n[5]=l=>a(d).orderNum=l),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1})]),_:1}),e(g,{span:12},{default:t(()=>[e(h,{label:"部门状态"},{default:t(()=>[e(pe,{modelValue:a(d).status,"onUpdate:modelValue":n[6]||(n[6]=l=>a(d).status=l)},{default:t(()=>[(p(!0),R(Q,null,j(a(N),l=>(p(),_(se,{key:l.value,value:l.value},{default:t(()=>[i(H(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Se as default};
