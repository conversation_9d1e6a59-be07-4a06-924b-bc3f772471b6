<template>
  <div class="app-container">




    <el-table
      v-loading="loading"
      :data="deviceList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column
        label="设备名称"
        align="center"
        prop="address"
        :show-overflow-tooltip="true"
      />

      <el-table-column label="状态" align="center" prop="deviceStatus">
        <template #default="scope">
          <el-tag v-if="scope.row.deviceStatus" type="success">正常</el-tag>
          <el-tag v-else type="danger">异常</el-tag>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Notice">
import { findDevice } from "@/api/system/park";
import { useRoute } from "vue-router";

const { proxy } = getCurrentInstance();

const route = useRoute();
const deviceList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    tagName: undefined,
  },
  rules: {

  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询设备列表 */
function getList() {
  loading.value = true;
  findDevice(route.query.parkNo).then((response) => {
    deviceList.value = response.data;
    total.value = response.data.length;
    loading.value = false;
  });
}



getList();
</script>
