import{T as B,B as A,d as G,r as a,C as H,F as $,e as s,G as N,c as J,o as g,H as _,h as o,I as x,j as e,i as n,k as W,v as f,s as C,t as X,f as Y,D as Z,U as ee,V as te}from"./index-DDqcBwar.js";function oe(w){return B({url:"/system/customer/getMoveCarList",method:"get",params:w})}const ae={class:"app-container"},ne={class:"right-grid"},le=A({name:"Notice"}),ce=Object.assign(le,{setup(w){const{proxy:S}=G(),k=a([]),c=a(!1),v=a(!0),U=a(!0),q=a([]),R=a(!0),L=a(!0),h=a(0);a("");const u=a(""),P=H({form:{},queryParams:{pageNum:1,pageSize:10,tagName:void 0},rules:{tagName:[{required:!0,message:"标签类型不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序不能为空",trigger:"blur"}]}}),{queryParams:l,form:ie,rules:re}=$(P);function b(){v.value=!0,oe(l.value).then(i=>{k.value=i.rows,h.value=i.total,v.value=!1})}function y(){l.value.pageNum=1,b()}function T(){S.resetForm("queryRef"),y()}function z(i){q.value=i.map(t=>t.noticeId),R.value=i.length!=1,L.value=!i.length}function Q(i){B.get("/system/customer/qrCode/"+i.phone,{params:{},responseType:"blob"},{isToken:!0}).then(t=>{if(ee(t)){let d=new Blob([t],{type:"image/png"});u.value=URL.createObjectURL(d),c.value=!0}}).catch(()=>{})}function j(){u.value&&te.saveAs(u.value,"二维码.png")}return b(),(i,t)=>{const V=s("el-input"),d=s("el-form-item"),m=s("el-button"),D=s("el-form"),p=s("el-table-column"),F=s("el-table"),I=s("pagination"),E=s("el-image"),K=s("el-dialog"),M=N("hasPermi"),O=N("loading");return g(),J("div",ae,[_(o(D,{model:e(l),ref:"queryRef",inline:!0},{default:n(()=>[o(d,{label:"手机号",prop:"phone"},{default:n(()=>[o(V,{modelValue:e(l).phone,"onUpdate:modelValue":t[0]||(t[0]=r=>e(l).phone=r),placeholder:"请输入手机号",clearable:"",style:{width:"200px"},onKeyup:W(y,["enter"])},null,8,["modelValue"])]),_:1}),o(d,null,{default:n(()=>[o(m,{type:"primary",icon:"Search",onClick:y},{default:n(()=>[f("搜索")]),_:1}),o(m,{icon:"Refresh",onClick:T},{default:n(()=>[f("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[x,e(U)]]),_((g(),C(F,{data:e(k),onSelectionChange:z},{default:n(()=>[o(p,{label:"序号",align:"center",type:"index",width:"100"}),o(p,{label:"手机号",align:"center",prop:"phone"}),o(p,{label:"生成时间",align:"center",prop:"createdTime"}),o(p,{label:"操作",align:"center",width:"150"},{default:n(r=>[_((g(),C(m,{type:"text",size:"small",onClick:se=>Q(r.row)},{default:n(()=>[f("二维码")]),_:2},1032,["onClick"])),[[M,["marketing:move:load"]]])]),_:1})]),_:1},8,["data"])),[[O,e(v)]]),_(o(I,{total:e(h),page:e(l).pageNum,"onUpdate:page":t[1]||(t[1]=r=>e(l).pageNum=r),limit:e(l).pageSize,"onUpdate:limit":t[2]||(t[2]=r=>e(l).pageSize=r),onPagination:b},null,8,["total","page","limit"]),[[x,e(h)>0]]),o(K,{title:"二维码",modelValue:e(c),"onUpdate:modelValue":t[3]||(t[3]=r=>Z(c)?c.value=r:null),width:"600px","append-to-body":""},{default:n(()=>[e(u)?(g(),C(E,{key:0,style:{width:"100%",height:"100%"},src:e(u),fit:"contain"},null,8,["src"])):X("",!0),Y("div",ne,[o(m,{type:"primary",onClick:j},{default:n(()=>[f("下载二维码")]),_:1})])]),_:1},8,["modelValue"])])}}});export{ce as default};
