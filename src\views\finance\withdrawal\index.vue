<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8">
      <el-col :span="3">
        <el-card>
          <el-statistic
            title="待提现金额(已完成订单金额总合-分成)"
            :value="bankDetails.fundBalance"
          >
            <template #suffix>元</template>
          </el-statistic>
        </el-card>
      </el-col>
      <el-col :span="21" class="right-grid">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['finance:withdrawal:apply']"
          >提现</el-button
        >
      </el-col>
    </el-row>

    <el-table
      v-loading="loading"
      :data="logList"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="序号" align="center" type="index" width="100" />
      <el-table-column label="申请时间" align="center" prop="applyTime" />

      <el-table-column label="提现账户" align="center" prop="bankNum" />
      <el-table-column label="姓名" align="center" prop="username" />
      <el-table-column label="提现银行" align="center" prop="bank" />

      <el-table-column label="提现金额" align="center" prop="amount" >
        <template #default="scope">
          <span>{{ scope.row.amount||0 }} 元</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="paymentStatus">
        <template #default="scope">
          <dict-tag
            :value="scope.row.paymentStatus"
            :options="payment_status"
          />
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" v-model="open" width="580px" append-to-body>
      <el-form ref="tagRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="姓名" prop="username">
              <el-input v-model="form.username" placeholder="请输入提现卡号" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="提现卡号" prop="bankNum">
              <el-input v-model="form.bankNum" placeholder="请输入提现卡号" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="提现银行" prop="bank">
              <el-select
                v-model="form.bank"
                placeholder="请选择提现银行"
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="item in bankList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="提现金额" prop="amount">
              <el-input
                v-model="form.amount"
                placeholder="请输入提现金额"
                type="number"
                :min="0"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="支付密码" prop="payPassword">
              <el-input
                v-model="form.payPassword"
                placeholder="请输入支付密码"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确认提现</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Notice">
import {
  getWithdraw,
  getBalance,
  withdraw,
  getBankCardList,
} from "@/api/finance/statements";

const { proxy } = getCurrentInstance();

const logList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const bankDetails = ref({});
const bankList = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    customerId: null,
  },
  rules: {
    bankNum: [{ required: true, message: "提现卡号不能为空", trigger: "blur" }],
    bank: [{ required: true, message: "提现银行不能为空", trigger: "blur" }],
    amount: [
      { required: true, message: "提现金额不能为空", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (value > bankDetails.value.fundBalance) {
            callback(new Error("提现金额不能大于账户余额"));
          } else {
            callback();
          }
        },
      },
    ],
    payPassword: [
      { required: true, message: "支付密码不能为空", trigger: "blur" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询公告列表 */
function getList() {
  loading.value = true;
  getWithdraw(queryParams.value).then((response) => {
    logList.value = response.rows;
    total.value = parseInt(response.total);
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {};
  proxy.resetForm("tagRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.noticeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "申请提现";
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["tagRef"].validate((valid) => {
    if (valid) {
      withdraw({
        ...form.value,
        accountId: bankDetails.value.accountId,
      }).then((response) => {
        proxy.$modal.msgSuccess("申请成功");
        open.value = false;
        queryBalance();
      });
    }
  });
}

// 查看当前用户的余额
function queryBalance() {
  getBalance().then((response) => {
    bankDetails.value = response.data;
    queryParams.value.customerId = response.data.customerId;
    getList();
  });
}
queryBalance();
// 静态数据
//操作类型 value = "操作类型", allowableValues = "1, 2, 3"

//设备状态 value = "设备状态", allowableValues = "0, 1, 2"
const payment_status = [
  { label: "打款中", value: "2" },
  { label: "已打款", value: "1" },
];
// 获取银行卡列表
function getBankCard() {
  getBankCardList().then((response) => {
    bankList.value = response.data;
  });
}
getBankCard();
</script>
<style lang="scss" scoped>
.right-grid {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
}
</style>
