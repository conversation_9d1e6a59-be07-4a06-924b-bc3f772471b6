import{T as P,B as me,d as ce,r as f,C as fe,F as _e,e as d,G as Q,c as k,o as m,H as y,h as e,I as A,j as t,i as l,k as $,J as E,K as G,s as v,D as q,v as p,f as B,A as i,t as J}from"./index-DDqcBwar.js";function ge(C){return P({url:"/monitor/operlog/list",method:"get",params:C})}function ve(C){return P({url:"/monitor/operlog/"+C,method:"delete"})}function be(){return P({url:"/monitor/operlog/clean",method:"delete"})}const he={class:"app-container"},ye={key:0},we={key:1},Ve={class:"dialog-footer"},ke=me({name:"Operlog"}),xe=Object.assign(ke,{setup(C){const{proxy:_}=ce(),{sys_oper_type:N,sys_common_status:K}=_.useDict("sys_oper_type","sys_common_status"),L=f([]),w=f(!1),I=f(!0),T=f(!0),F=f([]);f(!0);const M=f(!0),U=f(0);f("");const V=f([]),R=f({prop:"operTime",order:"descending"}),W=fe({form:{},queryParams:{pageNum:1,pageSize:10,operIp:void 0,title:void 0,operName:void 0,operateType:void 0,status:void 0}}),{queryParams:n,form:r}=_e(W);function b(){I.value=!0,ge(_.addDateRange(n.value,V.value)).then(s=>{L.value=s.rows,U.value=s.total,I.value=!1})}function X(s,a){return _.selectDictLabel(N.value,s.operateType)}function x(){n.value.pageNum=1,b()}function Z(){V.value=[],_.resetForm("queryRef"),n.value.pageNum=1,_.$refs.operlogRef.sort(R.value.prop,R.value.order)}function ee(s){F.value=s.map(a=>a.operId),M.value=!s.length}function le(s,a,S){n.value.orderByColumn=s.prop,n.value.isAsc=s.order,b()}function te(s){w.value=!0,r.value=s}function oe(s){const a=s.operId||F.value;_.$modal.confirm('是否确认删除日志编号为"'+a+'"的数据项?').then(function(){return ve(a)}).then(()=>{b(),_.$modal.msgSuccess("删除成功")}).catch(()=>{})}function ae(){_.$modal.confirm("是否确认清空所有操作日志数据项?").then(function(){return be()}).then(()=>{b(),_.$modal.msgSuccess("清空成功")}).catch(()=>{})}function ne(){_.download("monitor/operlog/export",{...n.value},`config_${new Date().getTime()}.xlsx`)}return b(),(s,a)=>{const S=d("el-input"),u=d("el-form-item"),O=d("el-option"),Y=d("el-select"),re=d("el-date-picker"),h=d("el-button"),j=d("el-form"),c=d("el-col"),ue=d("right-toolbar"),z=d("el-row"),g=d("el-table-column"),H=d("dict-tag"),se=d("el-table"),pe=d("pagination"),de=d("el-dialog"),D=Q("hasPermi"),ie=Q("loading");return m(),k("div",he,[y(e(j,{model:t(n),ref:"queryRef",inline:!0,"label-width":"68px"},{default:l(()=>[e(u,{label:"操作地址",prop:"operIp"},{default:l(()=>[e(S,{modelValue:t(n).operIp,"onUpdate:modelValue":a[0]||(a[0]=o=>t(n).operIp=o),placeholder:"请输入操作地址",clearable:"",style:{width:"240px"},onKeyup:$(x,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"系统模块",prop:"title"},{default:l(()=>[e(S,{modelValue:t(n).title,"onUpdate:modelValue":a[1]||(a[1]=o=>t(n).title=o),placeholder:"请输入系统模块",clearable:"",style:{width:"240px"},onKeyup:$(x,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"操作人员",prop:"operName"},{default:l(()=>[e(S,{modelValue:t(n).operName,"onUpdate:modelValue":a[2]||(a[2]=o=>t(n).operName=o),placeholder:"请输入操作人员",clearable:"",style:{width:"240px"},onKeyup:$(x,["enter"])},null,8,["modelValue"])]),_:1}),e(u,{label:"类型",prop:"operateType"},{default:l(()=>[e(Y,{modelValue:t(n).operateType,"onUpdate:modelValue":a[3]||(a[3]=o=>t(n).operateType=o),placeholder:"操作类型",clearable:"",style:{width:"240px"}},{default:l(()=>[(m(!0),k(E,null,G(t(N),o=>(m(),v(O,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"状态",prop:"status"},{default:l(()=>[e(Y,{modelValue:t(n).status,"onUpdate:modelValue":a[4]||(a[4]=o=>t(n).status=o),placeholder:"操作状态",clearable:"",style:{width:"240px"}},{default:l(()=>[(m(!0),k(E,null,G(t(K),o=>(m(),v(O,{key:o.value,label:o.label,value:o.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(u,{label:"操作时间",style:{width:"308px"}},{default:l(()=>[e(re,{modelValue:t(V),"onUpdate:modelValue":a[5]||(a[5]=o=>q(V)?V.value=o:null),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","default-time":[new Date(2e3,1,1,0,0,0),new Date(2e3,1,1,23,59,59)]},null,8,["modelValue","default-time"])]),_:1}),e(u,null,{default:l(()=>[e(h,{type:"primary",icon:"Search",onClick:x},{default:l(()=>[p("搜索")]),_:1}),e(h,{icon:"Refresh",onClick:Z},{default:l(()=>[p("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[A,t(T)]]),e(z,{gutter:10,class:"mb8"},{default:l(()=>[e(c,{span:1.5},{default:l(()=>[y((m(),v(h,{type:"danger",plain:"",icon:"Delete",disabled:t(M),onClick:oe},{default:l(()=>[p("删除")]),_:1},8,["disabled"])),[[D,["monitor:operlog:remove"]]])]),_:1}),e(c,{span:1.5},{default:l(()=>[y((m(),v(h,{type:"danger",plain:"",icon:"Delete",onClick:ae},{default:l(()=>[p("清空")]),_:1})),[[D,["monitor:operlog:remove"]]])]),_:1}),e(c,{span:1.5},{default:l(()=>[y((m(),v(h,{type:"warning",plain:"",icon:"Download",onClick:ne},{default:l(()=>[p("导出")]),_:1})),[[D,["monitor:operlog:export"]]])]),_:1}),e(ue,{showSearch:t(T),"onUpdate:showSearch":a[6]||(a[6]=o=>q(T)?T.value=o:null),onQueryTable:b},null,8,["showSearch"])]),_:1}),y((m(),v(se,{ref:"operlogRef",data:t(L),onSelectionChange:ee,"default-sort":t(R),onSortChange:le},{default:l(()=>[e(g,{type:"selection",width:"50",align:"center"}),e(g,{label:"日志编号",align:"center",prop:"operId"}),e(g,{label:"系统模块",align:"center",prop:"title","show-overflow-tooltip":!0}),e(g,{label:"操作类型",align:"center",prop:"operateType"},{default:l(o=>[e(H,{options:t(N),value:o.row.operateType},null,8,["options","value"])]),_:1}),e(g,{label:"操作人员",align:"center",width:"110",prop:"operName","show-overflow-tooltip":!0,sortable:"custom","sort-orders":["descending","ascending"]}),e(g,{label:"操作地址",align:"center",prop:"operIp",width:"130","show-overflow-tooltip":!0}),e(g,{label:"操作状态",align:"center",prop:"status"},{default:l(o=>[e(H,{options:t(K),value:o.row.status},null,8,["options","value"])]),_:1}),e(g,{label:"操作日期",align:"center",prop:"operTime",width:"180",sortable:"custom","sort-orders":["descending","ascending"]},{default:l(o=>[B("span",null,i(s.parseTime(o.row.operTime)),1)]),_:1}),e(g,{label:"消耗时间",align:"center",prop:"costTime",width:"110","show-overflow-tooltip":!0,sortable:"custom","sort-orders":["descending","ascending"]},{default:l(o=>[B("span",null,i(o.row.costTime)+"毫秒",1)]),_:1}),e(g,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:l(o=>[y((m(),v(h,{link:"",type:"primary",icon:"View",onClick:Ce=>te(o.row,o.index)},{default:l(()=>[p("详细")]),_:2},1032,["onClick"])),[[D,["monitor:operlog:query"]]])]),_:1})]),_:1},8,["data","default-sort"])),[[ie,t(I)]]),y(e(pe,{total:t(U),page:t(n).pageNum,"onUpdate:page":a[7]||(a[7]=o=>t(n).pageNum=o),limit:t(n).pageSize,"onUpdate:limit":a[8]||(a[8]=o=>t(n).pageSize=o),onPagination:b},null,8,["total","page","limit"]),[[A,t(U)>0]]),e(de,{title:"操作日志详细",modelValue:t(w),"onUpdate:modelValue":a[10]||(a[10]=o=>q(w)?w.value=o:null),width:"800px","append-to-body":""},{footer:l(()=>[B("div",Ve,[e(h,{onClick:a[9]||(a[9]=o=>w.value=!1)},{default:l(()=>[p("关 闭")]),_:1})])]),default:l(()=>[e(j,{model:t(r),"label-width":"100px"},{default:l(()=>[e(z,null,{default:l(()=>[e(c,{span:12},{default:l(()=>[e(u,{label:"操作模块："},{default:l(()=>[p(i(t(r).title)+" / "+i(X(t(r))),1)]),_:1}),e(u,{label:"登录信息："},{default:l(()=>[p(i(t(r).operName)+" / "+i(t(r).operIp)+" / "+i(t(r).operLocation),1)]),_:1})]),_:1}),e(c,{span:12},{default:l(()=>[e(u,{label:"请求地址："},{default:l(()=>[p(i(t(r).operUrl),1)]),_:1}),e(u,{label:"请求方式："},{default:l(()=>[p(i(t(r).requestMethod),1)]),_:1})]),_:1}),e(c,{span:24},{default:l(()=>[e(u,{label:"操作方法："},{default:l(()=>[p(i(t(r).method),1)]),_:1})]),_:1}),e(c,{span:24},{default:l(()=>[e(u,{label:"请求参数："},{default:l(()=>[p(i(t(r).operParam),1)]),_:1})]),_:1}),e(c,{span:24},{default:l(()=>[e(u,{label:"返回参数："},{default:l(()=>[p(i(t(r).jsonResult),1)]),_:1})]),_:1}),e(c,{span:8},{default:l(()=>[e(u,{label:"操作状态："},{default:l(()=>[t(r).status===0?(m(),k("div",ye,"正常")):t(r).status===1?(m(),k("div",we,"失败")):J("",!0)]),_:1})]),_:1}),e(c,{span:8},{default:l(()=>[e(u,{label:"消耗时间："},{default:l(()=>[p(i(t(r).costTime)+"毫秒",1)]),_:1})]),_:1}),e(c,{span:8},{default:l(()=>[e(u,{label:"操作时间："},{default:l(()=>[p(i(s.parseTime(t(r).operTime)),1)]),_:1})]),_:1}),e(c,{span:24},{default:l(()=>[t(r).status===1?(m(),v(u,{key:0,label:"异常信息："},{default:l(()=>[p(i(t(r).errorMsg),1)]),_:1})):J("",!0)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});export{xe as default};
