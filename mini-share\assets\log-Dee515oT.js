import{T as G,B as de,d as pe,r as b,u as ce,C as me,F as _e,e as r,G as z,c as k,o as d,H as w,h as e,I as F,j as t,i as o,k as fe,J as K,K as Q,s as g,D as T,v as s,f as E,A as v,t as O}from"./index-DDqcBwar.js";import{g as be}from"./job-DreYwwOx.js";function ge(V){return G({url:"/monitor/jobLog/list",method:"get",params:V})}function ve(V){return G({url:"/monitor/jobLog/"+V,method:"delete"})}function he(){return G({url:"/monitor/jobLog/clean",method:"delete"})}const we={class:"app-container"},je={key:0},ye={key:1},ke={class:"dialog-footer"},Ve=de({name:"JobLog"}),Se=Object.assign(Ve,{setup(V){const{proxy:m}=pe(),{sys_common_status:I,sys_job_group:R}=m.useDict("sys_common_status","sys_job_group"),U=b([]),j=b(!1),N=b(!0),C=b(!0),L=b([]),$=b(!0),S=b(0),y=b([]),J=ce(),A=me({form:{},queryParams:{pageNum:1,pageSize:10,dictName:void 0,dictType:void 0,status:void 0}}),{queryParams:n,form:p,rules:Ce}=_e(A);function h(){N.value=!0,ge(m.addDateRange(n.value,y.value)).then(u=>{U.value=u.rows,S.value=u.total,N.value=!1})}function H(){const u={path:"/monitor/job"};m.$tab.closeOpenPage(u)}function D(){n.value.pageNum=1,h()}function W(){y.value=[],m.resetForm("queryRef"),D()}function X(u){L.value=u.map(a=>a.jobLogId),$.value=!u.length}function Z(u){j.value=!0,p.value=u}function ee(u){m.$modal.confirm('是否确认删除调度日志编号为"'+L.value+'"的数据项?').then(function(){return ve(L.value)}).then(()=>{h(),m.$modal.msgSuccess("删除成功")}).catch(()=>{})}function oe(){m.$modal.confirm("是否确认清空所有调度日志数据项?").then(function(){return he()}).then(()=>{h(),m.$modal.msgSuccess("清空成功")}).catch(()=>{})}function te(){m.download("monitor/jobLog/export",{...n.value},`job_log_${new Date().getTime()}.xlsx`)}return(()=>{const u=J.params&&J.params.jobId;u!==void 0&&u!=0?be(u).then(a=>{n.value.jobName=a.data.jobName,n.value.jobGroup=a.data.jobGroup,h()}):h()})(),(u,a)=>{const le=r("el-input"),i=r("el-form-item"),P=r("el-option"),q=r("el-select"),ae=r("el-date-picker"),f=r("el-button"),B=r("el-form"),c=r("el-col"),ne=r("right-toolbar"),M=r("el-row"),_=r("el-table-column"),Y=r("dict-tag"),ue=r("el-table"),re=r("pagination"),se=r("el-dialog"),x=z("hasPermi"),ie=z("loading");return d(),k("div",we,[w(e(B,{model:t(n),ref:"queryRef",inline:!0,"label-width":"68px"},{default:o(()=>[e(i,{label:"任务名称",prop:"jobName"},{default:o(()=>[e(le,{modelValue:t(n).jobName,"onUpdate:modelValue":a[0]||(a[0]=l=>t(n).jobName=l),placeholder:"请输入任务名称",clearable:"",style:{width:"240px"},onKeyup:fe(D,["enter"])},null,8,["modelValue"])]),_:1}),e(i,{label:"任务组名",prop:"jobGroup"},{default:o(()=>[e(q,{modelValue:t(n).jobGroup,"onUpdate:modelValue":a[1]||(a[1]=l=>t(n).jobGroup=l),placeholder:"请选择任务组名",clearable:"",style:{width:"240px"}},{default:o(()=>[(d(!0),k(K,null,Q(t(R),l=>(d(),g(P,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"执行状态",prop:"status"},{default:o(()=>[e(q,{modelValue:t(n).status,"onUpdate:modelValue":a[2]||(a[2]=l=>t(n).status=l),placeholder:"请选择执行状态",clearable:"",style:{width:"240px"}},{default:o(()=>[(d(!0),k(K,null,Q(t(I),l=>(d(),g(P,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(i,{label:"执行时间",style:{width:"308px"}},{default:o(()=>[e(ae,{modelValue:t(y),"onUpdate:modelValue":a[3]||(a[3]=l=>T(y)?y.value=l:null),"value-format":"YYYY-MM-DD",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},null,8,["modelValue"])]),_:1}),e(i,null,{default:o(()=>[e(f,{type:"primary",icon:"Search",onClick:D},{default:o(()=>[s("搜索")]),_:1}),e(f,{icon:"Refresh",onClick:W},{default:o(()=>[s("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[F,t(C)]]),e(M,{gutter:10,class:"mb8"},{default:o(()=>[e(c,{span:1.5},{default:o(()=>[w((d(),g(f,{type:"danger",plain:"",icon:"Delete",disabled:t($),onClick:ee},{default:o(()=>[s("删除")]),_:1},8,["disabled"])),[[x,["monitor:job:remove"]]])]),_:1}),e(c,{span:1.5},{default:o(()=>[w((d(),g(f,{type:"danger",plain:"",icon:"Delete",onClick:oe},{default:o(()=>[s("清空")]),_:1})),[[x,["monitor:job:remove"]]])]),_:1}),e(c,{span:1.5},{default:o(()=>[w((d(),g(f,{type:"warning",plain:"",icon:"Download",onClick:te},{default:o(()=>[s("导出")]),_:1})),[[x,["monitor:job:export"]]])]),_:1}),e(c,{span:1.5},{default:o(()=>[e(f,{type:"warning",plain:"",icon:"Close",onClick:H},{default:o(()=>[s("关闭")]),_:1})]),_:1}),e(ne,{showSearch:t(C),"onUpdate:showSearch":a[4]||(a[4]=l=>T(C)?C.value=l:null),onQueryTable:h},null,8,["showSearch"])]),_:1}),w((d(),g(ue,{data:t(U),onSelectionChange:X},{default:o(()=>[e(_,{type:"selection",width:"55",align:"center"}),e(_,{label:"日志编号",width:"80",align:"center",prop:"jobLogId"}),e(_,{label:"任务名称",align:"center",prop:"jobName","show-overflow-tooltip":!0}),e(_,{label:"任务组名",align:"center",prop:"jobGroup","show-overflow-tooltip":!0},{default:o(l=>[e(Y,{options:t(R),value:l.row.jobGroup},null,8,["options","value"])]),_:1}),e(_,{label:"调用目标字符串",align:"center",prop:"invokeTarget","show-overflow-tooltip":!0}),e(_,{label:"日志信息",align:"center",prop:"jobMessage","show-overflow-tooltip":!0}),e(_,{label:"执行状态",align:"center",prop:"status"},{default:o(l=>[e(Y,{options:t(I),value:l.row.status},null,8,["options","value"])]),_:1}),e(_,{label:"执行时间",align:"center",prop:"createTime",width:"180"},{default:o(l=>[E("span",null,v(u.parseTime(l.row.createTime)),1)]),_:1}),e(_,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:o(l=>[w((d(),g(f,{link:"",type:"primary",icon:"View",onClick:xe=>Z(l.row)},{default:o(()=>[s("详细")]),_:2},1032,["onClick"])),[[x,["monitor:job:query"]]])]),_:1})]),_:1},8,["data"])),[[ie,t(N)]]),w(e(re,{total:t(S),page:t(n).pageNum,"onUpdate:page":a[5]||(a[5]=l=>t(n).pageNum=l),limit:t(n).pageSize,"onUpdate:limit":a[6]||(a[6]=l=>t(n).pageSize=l),onPagination:h},null,8,["total","page","limit"]),[[F,t(S)>0]]),e(se,{title:"调度日志详细",modelValue:t(j),"onUpdate:modelValue":a[8]||(a[8]=l=>T(j)?j.value=l:null),width:"700px","append-to-body":""},{footer:o(()=>[E("div",ke,[e(f,{onClick:a[7]||(a[7]=l=>j.value=!1)},{default:o(()=>[s("关 闭")]),_:1})])]),default:o(()=>[e(B,{model:t(p),"label-width":"100px"},{default:o(()=>[e(M,null,{default:o(()=>[e(c,{span:12},{default:o(()=>[e(i,{label:"日志序号："},{default:o(()=>[s(v(t(p).jobLogId),1)]),_:1}),e(i,{label:"任务名称："},{default:o(()=>[s(v(t(p).jobName),1)]),_:1})]),_:1}),e(c,{span:12},{default:o(()=>[e(i,{label:"任务分组："},{default:o(()=>[s(v(t(p).jobGroup),1)]),_:1}),e(i,{label:"执行时间："},{default:o(()=>[s(v(t(p).createTime),1)]),_:1})]),_:1}),e(c,{span:24},{default:o(()=>[e(i,{label:"调用方法："},{default:o(()=>[s(v(t(p).invokeTarget),1)]),_:1})]),_:1}),e(c,{span:24},{default:o(()=>[e(i,{label:"日志信息："},{default:o(()=>[s(v(t(p).jobMessage),1)]),_:1})]),_:1}),e(c,{span:24},{default:o(()=>[e(i,{label:"执行状态："},{default:o(()=>[t(p).status==0?(d(),k("div",je,"正常")):t(p).status==1?(d(),k("div",ye,"失败")):O("",!0)]),_:1})]),_:1}),e(c,{span:24},{default:o(()=>[t(p).status==1?(d(),g(i,{key:0,label:"异常信息："},{default:o(()=>[s(v(t(p).exceptionInfo),1)]),_:1})):O("",!0)]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}});export{Se as default};
