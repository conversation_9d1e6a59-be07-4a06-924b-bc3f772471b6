import{T as Y,B as Z,d as ee,r as d,C as le,F as te,e as u,G as ae,c as U,o as V,H as N,h as e,I as q,j as l,i as a,k as w,J as oe,K as ne,v as b,s as ue,f as se,D as re}from"./index-DDqcBwar.js";function ie(k){return Y({url:"/device/log",method:"get",params:k})}const de={class:"app-container"},pe={class:"dialog-footer"},ce=Z({name:"Notice"}),ge=Object.assign(ce,{setup(k){const{proxy:p}=ee(),{sys_notice_status:me,sys_notice_type:fe}=p.useDict("sys_notice_status","sys_notice_type"),C=d([]),c=d(!1),y=d(!0),R=d(!0),I=d([]),F=d(!0),K=d(!0),h=d(0),D=d(""),L=le({form:{},queryParams:{pageNum:1,pageSize:10,tagName:void 0},rules:{tagName:[{required:!0,message:"标签类型不能为空",trigger:"blur"}],orderBy:[{required:!0,message:"排序不能为空",trigger:"blur"}]}}),{queryParams:n,form:r,rules:z}=te(L);function f(){y.value=!0,ie(n.value).then(i=>{C.value=i.rows,h.value=i.total,y.value=!1})}function P(){c.value=!1,$()}function $(){r.value={noticeId:void 0,noticeTitle:void 0,noticeType:void 0,noticeContent:void 0,status:"0"},p.resetForm("tagRef")}function _(){n.value.pageNum=1,f()}function j(){p.resetForm("queryRef"),_()}function G(i){I.value=i.map(o=>o.noticeId),F.value=i.length!=1,K.value=!i.length}function Q(){p.$refs.tagRef.validate(i=>{i&&(r.value.parkId!=null?updateTag(r.value).then(o=>{p.$modal.msgSuccess("修改成功"),c.value=!1,f()}):addTag(r.value).then(o=>{p.$modal.msgSuccess("新增成功"),c.value=!1,f()}))})}f();const E=[{label:"4G",value:"1"},{label:"蓝牙",value:"2"}],x=[{label:"升锁",value:"1"},{label:"降锁",value:"2"}];return(i,o)=>{const g=u("el-input"),m=u("el-form-item"),H=u("el-option"),J=u("el-select"),v=u("el-button"),B=u("el-form"),s=u("el-table-column"),S=u("dict-tag"),O=u("el-table"),A=u("pagination"),T=u("el-col"),M=u("el-row"),W=u("el-dialog"),X=ae("loading");return V(),U("div",de,[N(e(B,{model:l(n),ref:"queryRef",inline:!0},{default:a(()=>[e(m,{label:"设备编号",prop:"lockNo"},{default:a(()=>[e(g,{modelValue:l(n).lockNo,"onUpdate:modelValue":o[0]||(o[0]=t=>l(n).lockNo=t),placeholder:"请输入设备编号",clearable:"",style:{width:"200px"},onKeyup:w(_,["enter"])},null,8,["modelValue"])]),_:1}),e(m,{label:"手机号",prop:"phone"},{default:a(()=>[e(g,{modelValue:l(n).phone,"onUpdate:modelValue":o[1]||(o[1]=t=>l(n).phone=t),placeholder:"请输入设备编号",clearable:"",style:{width:"200px"},onKeyup:w(_,["enter"])},null,8,["modelValue"])]),_:1}),e(m,{label:"设备状态",prop:"status"},{default:a(()=>[e(J,{modelValue:l(n).status,"onUpdate:modelValue":o[2]||(o[2]=t=>l(n).status=t),placeholder:"请选择设备状态",style:{width:"200px"},onKeyup:w(_,["enter"])},{default:a(()=>[(V(),U(oe,null,ne(x,t=>e(H,{key:t.value,label:t.label,value:t.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),e(m,null,{default:a(()=>[e(v,{type:"primary",icon:"Search",onClick:_},{default:a(()=>[b("搜索")]),_:1}),e(v,{icon:"Refresh",onClick:j},{default:a(()=>[b("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[q,l(R)]]),N((V(),ue(O,{data:l(C),onSelectionChange:G},{default:a(()=>[e(s,{label:"序号",align:"center",type:"index",width:"100"}),e(s,{label:"设备编号",align:"center",prop:"lockNo"}),e(s,{label:"设备状态",align:"center",prop:"status"},{default:a(t=>[e(S,{value:t.row.status,options:x},null,8,["value"])]),_:1}),e(s,{label:"操作类型",align:"center",prop:"operateType"},{default:a(t=>[e(S,{value:t.row.operateType,options:E},null,8,["value"])]),_:1}),e(s,{label:"操作用户",align:"center",prop:"phone"}),e(s,{label:"请求接口地址",align:"center",prop:"requestUrl"}),e(s,{label:"请求参数",align:"center",prop:"reqBody"}),e(s,{label:"响应内容",align:"center",prop:"respBody"}),e(s,{label:"错误信息",align:"center",prop:"errorInfo"}),e(s,{label:"操作时间",align:"center",prop:"operateTime"})]),_:1},8,["data"])),[[X,l(y)]]),N(e(A,{total:l(h),page:l(n).pageNum,"onUpdate:page":o[3]||(o[3]=t=>l(n).pageNum=t),limit:l(n).pageSize,"onUpdate:limit":o[4]||(o[4]=t=>l(n).pageSize=t),onPagination:f},null,8,["total","page","limit"]),[[q,l(h)>0]]),e(W,{title:l(D),modelValue:l(c),"onUpdate:modelValue":o[7]||(o[7]=t=>re(c)?c.value=t:null),width:"580px","append-to-body":""},{footer:a(()=>[se("div",pe,[e(v,{type:"primary",onClick:Q},{default:a(()=>[b("确 定")]),_:1}),e(v,{onClick:P},{default:a(()=>[b("取 消")]),_:1})])]),default:a(()=>[e(B,{ref:"tagRef",model:l(r),rules:l(z),"label-width":"80px"},{default:a(()=>[e(M,null,{default:a(()=>[e(T,{span:24},{default:a(()=>[e(m,{label:"标签标题",prop:"tagName"},{default:a(()=>[e(g,{modelValue:l(r).tagName,"onUpdate:modelValue":o[5]||(o[5]=t=>l(r).tagName=t),placeholder:"请输入标签标题"},null,8,["modelValue"])]),_:1})]),_:1}),e(T,{span:24},{default:a(()=>[e(m,{label:"排序",prop:"orderBy"},{default:a(()=>[e(g,{modelValue:l(r).orderBy,"onUpdate:modelValue":o[6]||(o[6]=t=>l(r).orderBy=t),placeholder:"请输入排序",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{ge as default};
