<template>
    <div class="app-container">
   
    
      <el-table
        v-loading="loading"
        :data="tagList"
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <el-table-column label="序号" align="center" type="index" width="100" />
        <el-table-column
          label="车牌号码"
          align="center"
          prop="tagName"
          :show-overflow-tooltip="true"
        />
  
        <el-table-column label="入场时间" align="center" prop="orderBy" />
        <el-table-column label="开闸设备" align="center" prop="orderBy" />
        <el-table-column label="出场时间" align="center" prop="orderBy" />
      </el-table>
  
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
  

    </div>
  </template>
  
  <script setup name="Notice">
  import {findCarRecord } from "@/api/system/park";
  
  const { proxy } = getCurrentInstance();
  const { sys_notice_status, sys_notice_type } = proxy.useDict(
    "sys_notice_status",
    "sys_notice_type"
  );
  
  const tagList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");
  
  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      tagName: undefined,
    },
    rules: {
      tagName: [{ required: true, message: "标签类型不能为空", trigger: "blur" }],
      orderBy: [{ required: true, message: "排序不能为空", trigger: "blur" }],
    },
  });
  
  const { queryParams, form, rules } = toRefs(data);
  
  /** 查询公告列表 */
  function getList() {
    loading.value = true;
    findCarRecord(queryParams.value).then((response) => {
      tagList.value = response.rows;
      total.value = parseInt(response.total);
      loading.value = false;
    });
  }
  
  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }
  
  /** 表单重置 */
  function reset() {
    form.value = {
      noticeId: undefined,
      noticeTitle: undefined,
      noticeType: undefined,
      noticeContent: undefined,
      status: "0",
    };
    proxy.resetForm("tagRef");
  }
  
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }
  
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }
  
  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map((item) => item.noticeId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }
  
  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加标签";
  }
  
  /**修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const tagId = row.id || ids.value;
    getTag(tagId).then((response) => {
      form.value = response.data;
      open.value = true;
      title.value = "修改标签";
    });
  }
  
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["tagRef"].validate((valid) => {
      if (valid) {
        if (form.value.parkId != undefined) {
          updateTag(form.value).then((response) => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addTag(form.value).then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }
  
  /** 删除按钮操作 */
  function handleDelete(row) {
    const tagIds = row.id || ids.value;
    proxy.$modal
      .confirm('是否确认删除标签编号为"' + tagIds + '"的数据项？')
      .then(function () {
        return delTag(tagIds);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
      .catch(() => {});
  }
  
  getList();
  </script>
  