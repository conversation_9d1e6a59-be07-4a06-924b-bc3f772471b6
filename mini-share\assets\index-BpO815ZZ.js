import{T as k,B as ce,d as fe,r as f,C as _e,F as he,e as o,G as K,c as R,o as i,H as g,h as e,t as $,I as ve,j as t,i as a,k as ye,J as Q,K as j,s as _,v as m,D as O,f as G,A as H,L as be}from"./index-DDqcBwar.js";function J(d){return k({url:"/system/area/list",method:"get",params:d})}function ge(d){return k({url:"/system/area/list/exclude/"+d,method:"get"})}function ke(d){return k({url:"/system/area/"+d,method:"get"})}function Ve(d){return k({url:"/system/area",method:"post",data:d})}function we(d){return k({url:"/system/area",method:"put",data:d})}function Ie(d){return k({url:"/system/area/"+d,method:"delete"})}const Ne={class:"app-container"},Ce={class:"dialog-footer"},xe=ce({name:"Dept"}),Te=Object.assign(xe,{setup(d){const{proxy:s}=fe(),{sys_normal_disable:N}=s.useDict("sys_normal_disable"),q=f([]),c=f(!1),C=f(!0),w=f(!0),x=f(""),S=f([]),T=f(!0),A=f(!0),z=_e({form:{},queryParams:{deptName:void 0,status:void 0},rules:{parentId:[{required:!0,message:"上级区域不能为空",trigger:"blur"}],areaName:[{required:!0,message:"区域名称不能为空",trigger:"blur"}],orderNum:[{required:!0,message:"显示排序不能为空",trigger:"blur"}]}}),{queryParams:v,form:r,rules:M}=he(z);function y(){C.value=!0,J(v.value).then(u=>{q.value=s.handleTree(u.data,"areaId"),C.value=!1})}function W(){c.value=!1,U()}function U(){r.value={deptId:void 0,parentId:void 0,deptName:void 0,orderNum:0,leader:void 0,phone:void 0,email:void 0,status:"0"},s.resetForm("deptRef")}function D(){y()}function X(){s.resetForm("queryRef"),D()}function P(u){U(),J().then(n=>{S.value=s.handleTree(n.data,"areaId")}),u!=null&&(r.value.parentId=u.areaId),c.value=!0,x.value="添加区域"}function Y(){A.value=!1,T.value=!T.value,be(()=>{A.value=!0})}function Z(u){U(),ge(u.areaId).then(n=>{S.value=s.handleTree(n.data,"areaId")}),ke(u.areaId).then(n=>{r.value=n.data,c.value=!0,x.value="修改区域"})}function ee(){s.$refs.deptRef.validate(u=>{u&&(r.value.areaId!=null?we(r.value).then(n=>{s.$modal.msgSuccess("修改成功"),c.value=!1,y()}):Ve(r.value).then(n=>{s.$modal.msgSuccess("新增成功"),c.value=!1,y()}))})}function ae(u){s.$modal.confirm('是否确认删除名称为"'+u.areaName+'"的数据项?').then(function(){return Ie(u.areaId)}).then(()=>{y(),s.$modal.msgSuccess("删除成功")}).catch(()=>{})}return y(),(u,n)=>{const B=o("el-input"),h=o("el-form-item"),le=o("el-option"),te=o("el-select"),p=o("el-button"),E=o("el-form"),b=o("el-col"),ne=o("right-toolbar"),F=o("el-row"),V=o("el-table-column"),oe=o("dict-tag"),re=o("el-table"),ue=o("el-tree-select"),de=o("el-input-number"),se=o("el-radio"),ie=o("el-radio-group"),me=o("el-dialog"),I=K("hasPermi"),pe=K("loading");return i(),R("div",Ne,[g(e(E,{model:t(v),ref:"queryRef",inline:!0},{default:a(()=>[e(h,{label:"地区名称",prop:"areaName"},{default:a(()=>[e(B,{modelValue:t(v).areaName,"onUpdate:modelValue":n[0]||(n[0]=l=>t(v).areaName=l),placeholder:"请输入区域名称",clearable:"",style:{width:"200px"},onKeyup:ye(D,["enter"])},null,8,["modelValue"])]),_:1}),e(h,{label:"状态",prop:"status"},{default:a(()=>[e(te,{modelValue:t(v).status,"onUpdate:modelValue":n[1]||(n[1]=l=>t(v).status=l),placeholder:"区域状态",clearable:"",style:{width:"200px"}},{default:a(()=>[(i(!0),R(Q,null,j(t(N),l=>(i(),_(le,{key:l.value,label:l.label,value:l.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(h,null,{default:a(()=>[e(p,{type:"primary",icon:"Search",onClick:D},{default:a(()=>[m("搜索")]),_:1}),e(p,{icon:"Refresh",onClick:X},{default:a(()=>[m("重置")]),_:1})]),_:1})]),_:1},8,["model"]),[[ve,t(w)]]),e(F,{gutter:10,class:"mb8"},{default:a(()=>[e(b,{span:1.5},{default:a(()=>[g((i(),_(p,{type:"primary",plain:"",icon:"Plus",onClick:P},{default:a(()=>[m("新增")]),_:1})),[[I,["system:area:add"]]])]),_:1}),e(b,{span:1.5},{default:a(()=>[e(p,{type:"info",plain:"",icon:"Sort",onClick:Y},{default:a(()=>[m("展开/折叠")]),_:1})]),_:1}),e(ne,{showSearch:t(w),"onUpdate:showSearch":n[2]||(n[2]=l=>O(w)?w.value=l:null),onQueryTable:y},null,8,["showSearch"])]),_:1}),t(A)?g((i(),_(re,{key:0,data:t(q),"row-key":"areaId","default-expand-all":t(T),"tree-props":{children:"children",hasChildren:"hasChildren"}},{default:a(()=>[e(V,{prop:"areaName",label:"地区名称",width:"260"}),e(V,{prop:"orderNum",label:"排序",width:"200"}),e(V,{prop:"status",label:"状态",width:"100"},{default:a(l=>[e(oe,{options:t(N),value:l.row.status},null,8,["options","value"])]),_:1}),e(V,{label:"创建时间",align:"center",prop:"createTime",width:"200"},{default:a(l=>[G("span",null,H(u.parseTime(l.row.createTime)),1)]),_:1}),e(V,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:a(l=>[g((i(),_(p,{link:"",type:"primary",icon:"Edit",onClick:L=>Z(l.row)},{default:a(()=>[m("修改")]),_:2},1032,["onClick"])),[[I,["system:area:edit"]]]),g((i(),_(p,{link:"",type:"primary",icon:"Plus",onClick:L=>P(l.row)},{default:a(()=>[m("新增")]),_:2},1032,["onClick"])),[[I,["system:area:add"]]]),l.row.parentId!=0?g((i(),_(p,{key:0,link:"",type:"primary",icon:"Delete",onClick:L=>ae(l.row)},{default:a(()=>[m("删除")]),_:2},1032,["onClick"])),[[I,["system:area:remove"]]]):$("",!0)]),_:1})]),_:1},8,["data","default-expand-all"])),[[pe,t(C)]]):$("",!0),e(me,{title:t(x),modelValue:t(c),"onUpdate:modelValue":n[7]||(n[7]=l=>O(c)?c.value=l:null),width:"600px","append-to-body":""},{footer:a(()=>[G("div",Ce,[e(p,{type:"primary",onClick:ee},{default:a(()=>[m("确 定")]),_:1}),e(p,{onClick:W},{default:a(()=>[m("取 消")]),_:1})])]),default:a(()=>[e(E,{ref:"deptRef",model:t(r),rules:t(M),"label-width":"80px"},{default:a(()=>[e(F,null,{default:a(()=>[t(r).parentId!==0?(i(),_(b,{key:0,span:24},{default:a(()=>[e(h,{label:"上级地区",prop:"parentId"},{default:a(()=>[e(ue,{modelValue:t(r).parentId,"onUpdate:modelValue":n[3]||(n[3]=l=>t(r).parentId=l),data:t(S),props:{value:"areaId",label:"areaName",children:"children"},"value-key":"areaId",placeholder:"选择上级地区","check-strictly":""},null,8,["modelValue","data"])]),_:1})]),_:1})):$("",!0),e(b,{span:24},{default:a(()=>[e(h,{label:"区域名称",prop:"areaName"},{default:a(()=>[e(B,{modelValue:t(r).areaName,"onUpdate:modelValue":n[4]||(n[4]=l=>t(r).areaName=l),placeholder:"请输入区域名称"},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{span:12},{default:a(()=>[e(h,{label:"显示排序",prop:"orderNum"},{default:a(()=>[e(de,{modelValue:t(r).orderNum,"onUpdate:modelValue":n[5]||(n[5]=l=>t(r).orderNum=l),"controls-position":"right",min:0},null,8,["modelValue"])]),_:1})]),_:1}),e(b,{span:12},{default:a(()=>[e(h,{label:"区域状态"},{default:a(()=>[e(ie,{modelValue:t(r).status,"onUpdate:modelValue":n[6]||(n[6]=l=>t(r).status=l)},{default:a(()=>[(i(!0),R(Q,null,j(t(N),l=>(i(),_(se,{key:l.value,value:l.value},{default:a(()=>[m(H(l.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{Te as default};
