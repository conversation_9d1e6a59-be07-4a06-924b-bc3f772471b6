import{B as le,d as oe,u as re,r as u,C as se,F as ue,e as r,G as q,c as U,h as a,H as _,i as n,v as f,o as c,s as g,D as $,j as o,I as ie,J as de,K as pe,f as ce}from"./index-DDqcBwar.js";import{c as me,e as fe,a as _e,f as ge,h as he,i as ke}from"./park-DZi9DkwV.js";const ve={class:"app-container"},be={class:"dialog-footer"},ye=le({name:"Notice"}),we=Object.assign(ye,{setup(Ie){const{proxy:p}=oe(),b=re(),B=u([]),i=u(!1),y=u(!0),I=u(!0),A=u([]),D=u(!0),R=u(!0),C=u(0),S=u("");u([]),u([]);const w=u([]),F=se({form:{},queryParams:{pageNum:1,pageSize:10},rules:{phone:[{required:!0,message:"请输入手机号",trigger:"blur"}],nickname:[{required:!0,message:"请输入名称",trigger:"blur"}]}}),{queryParams:h,form:s,rules:z}=ue(F);function m(){y.value=!0,me(b.query.parkId).then(t=>{B.value=t.data,C.value=t.data.length,y.value=!1})}function L(){i.value=!1,V()}function V(){s.value={id:void 0,tagName:void 0,orderBy:void 0,parkTags:[{parkId:"",tagIds:[]}]},p.resetForm("parkRef")}function T(t){A.value=t.map(e=>e.noticeId),D.value=t.length!=1,R.value=!t.length}function j(){V(),i.value=!0,S.value="人员绑定"}function E(t){V(),_e({phone:t.phone}).then(e=>{s.value=e.data,i.value=!0,S.value="修改人员"})}function W(){p.$refs.parkRef.validate(t=>{t&&(s.value.parkId!=null?he({parkId:b.query.parkId,userId:s.value.userId}).then(e=>{p.$modal.msgSuccess("修改成功"),i.value=!1,m()}):ke({parkId:b.query.parkId,userId:s.value.userId}).then(e=>{p.$modal.msgSuccess("新增成功"),i.value=!1,m()}))})}function G(t){p.$modal.confirm('是否确认删除为"'+t.username+'"的数据项？').then(function(){return ge({parkId:t.parkId,userId:t.userId})}).then(()=>{m(),p.$modal.msgSuccess("删除成功")}).catch(()=>{})}function H(){p.$router.go(-1)}function J(t){fe({phonenumber:t}).then(e=>{w.value=e.data})}function K(t){let e=w.value.find(d=>d.phonenumber==t);s.value.nickname=e.nickName,s.value.userId=e.userId}return m(),J(),(t,e)=>{const d=r("el-button"),k=r("el-col"),O=r("right-toolbar"),N=r("el-row"),v=r("el-table-column"),Q=r("el-table"),M=r("pagination"),X=r("el-option"),Y=r("el-select"),P=r("el-form-item"),Z=r("el-input"),ee=r("el-form"),ae=r("el-dialog"),x=q("hasPermi"),ne=q("loading");return c(),U("div",ve,[a(N,{gutter:10,class:"mb8"},{default:n(()=>[a(k,{span:1.5},{default:n(()=>[a(d,{type:"primary",plain:"",icon:"Back",onClick:H},{default:n(()=>[f("返回")]),_:1})]),_:1}),a(k,{span:1.5},{default:n(()=>[_((c(),g(d,{type:"primary",plain:"",icon:"Plus",onClick:j},{default:n(()=>[f("新增")]),_:1})),[[x,["system:park:bind:add"]]])]),_:1}),a(O,{showSearch:o(I),"onUpdate:showSearch":e[0]||(e[0]=l=>$(I)?I.value=l:null),onQueryTable:m},null,8,["showSearch"])]),_:1}),_((c(),g(Q,{data:o(B),onSelectionChange:T},{default:n(()=>[a(v,{label:"序号",align:"center",type:"index",width:"100"}),a(v,{label:"名称",align:"center",prop:"username","show-overflow-tooltip":!0}),a(v,{label:"所属车场",align:"center",prop:"parkName"}),a(v,{label:"操作",align:"center","class-name":"small-padding fixed-width"},{default:n(l=>[_((c(),g(d,{link:"",type:"primary",icon:"Edit",onClick:te=>E(l.row)},{default:n(()=>[f("修改")]),_:2},1032,["onClick"])),[[x,["system:park:bind:update"]]]),_((c(),g(d,{link:"",type:"primary",icon:"Delete",onClick:te=>G(l.row)},{default:n(()=>[f("删除")]),_:2},1032,["onClick"])),[[x,["system:park:bind:delete"]]])]),_:1})]),_:1},8,["data"])),[[ne,o(y)]]),_(a(M,{total:o(C),page:o(h).pageNum,"onUpdate:page":e[1]||(e[1]=l=>o(h).pageNum=l),limit:o(h).pageSize,"onUpdate:limit":e[2]||(e[2]=l=>o(h).pageSize=l),onPagination:m},null,8,["total","page","limit"]),[[ie,o(C)>0]]),a(ae,{title:o(S),modelValue:o(i),"onUpdate:modelValue":e[5]||(e[5]=l=>$(i)?i.value=l:null),width:"880px","append-to-body":""},{footer:n(()=>[ce("div",be,[a(d,{type:"primary",onClick:W},{default:n(()=>[f("确 定")]),_:1}),a(d,{onClick:L},{default:n(()=>[f("取 消")]),_:1})])]),default:n(()=>[a(ee,{ref:"parkRef",model:o(s),rules:o(z),"label-width":"80px"},{default:n(()=>[a(N,null,{default:n(()=>[a(k,{span:24},{default:n(()=>[a(P,{label:"手机号",prop:"phone"},{default:n(()=>[a(Y,{modelValue:o(s).phone,"onUpdate:modelValue":e[3]||(e[3]=l=>o(s).phone=l),placeholder:"请选择",clearable:"",onChange:K},{default:n(()=>[(c(!0),U(de,null,pe(o(w),l=>(c(),g(X,{key:l.phonenumber,label:l.phonenumber,value:l.phonenumber},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1}),a(k,{span:24},{default:n(()=>[a(P,{label:"名称",prop:"nickname"},{default:n(()=>[a(Z,{modelValue:o(s).nickname,"onUpdate:modelValue":e[4]||(e[4]=l=>o(s).nickname=l),placeholder:"请输入名称",clearable:""},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["title","modelValue"])])}}});export{we as default};
