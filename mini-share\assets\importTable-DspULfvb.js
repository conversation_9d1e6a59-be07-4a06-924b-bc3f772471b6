import{r as c,d as D,C as I,e as m,s as K,o as j,i as l,h as e,j as o,k as h,v as f,H as L,I as P,f as Q,D as E}from"./index-DDqcBwar.js";import{l as F,i as H}from"./gen-Bvb-qkvl.js";const A={class:"dialog-footer"},O={__name:"importTable",emits:["ok"],setup(G,{expose:C,emit:N}){const b=c(0),s=c(!1),v=c([]),w=c([]),{proxy:p}=D(),a=I({pageNum:1,pageSize:10,tableName:void 0,tableComment:void 0}),V=N;function k(){_(),s.value=!0}function x(n){p.$refs.table.toggleRowSelection(n)}function S(n){v.value=n.map(t=>t.tableName)}function _(){F(a).then(n=>{w.value=n.rows,b.value=n.total})}function u(){a.pageNum=1,_()}function T(){p.resetForm("queryRef"),u()}function R(){const n=v.value.join(",");if(n==""){p.$modal.msgError("请选择要导入的表");return}H({tables:n}).then(t=>{p.$modal.msgSuccess(t.msg),t.code===200&&(s.value=!1,V("ok"))})}return C({show:k}),(n,t)=>{const y=m("el-input"),g=m("el-form-item"),d=m("el-button"),U=m("el-form"),r=m("el-table-column"),$=m("el-table"),q=m("pagination"),z=m("el-row"),B=m("el-dialog");return j(),K(B,{title:"导入表",modelValue:o(s),"onUpdate:modelValue":t[5]||(t[5]=i=>E(s)?s.value=i:null),width:"800px",top:"5vh","append-to-body":""},{footer:l(()=>[Q("div",A,[e(d,{type:"primary",onClick:R},{default:l(()=>[f("确 定")]),_:1}),e(d,{onClick:t[4]||(t[4]=i=>s.value=!1)},{default:l(()=>[f("取 消")]),_:1})])]),default:l(()=>[e(U,{model:o(a),ref:"queryRef",inline:!0},{default:l(()=>[e(g,{label:"表名称",prop:"tableName"},{default:l(()=>[e(y,{modelValue:o(a).tableName,"onUpdate:modelValue":t[0]||(t[0]=i=>o(a).tableName=i),placeholder:"请输入表名称",clearable:"",style:{width:"180px"},onKeyup:h(u,["enter"])},null,8,["modelValue"])]),_:1}),e(g,{label:"表描述",prop:"tableComment"},{default:l(()=>[e(y,{modelValue:o(a).tableComment,"onUpdate:modelValue":t[1]||(t[1]=i=>o(a).tableComment=i),placeholder:"请输入表描述",clearable:"",style:{width:"180px"},onKeyup:h(u,["enter"])},null,8,["modelValue"])]),_:1}),e(g,null,{default:l(()=>[e(d,{type:"primary",icon:"Search",onClick:u},{default:l(()=>[f("搜索")]),_:1}),e(d,{icon:"Refresh",onClick:T},{default:l(()=>[f("重置")]),_:1})]),_:1})]),_:1},8,["model"]),e(z,null,{default:l(()=>[e($,{onRowClick:x,ref:"table",data:o(w),onSelectionChange:S,height:"260px"},{default:l(()=>[e(r,{type:"selection",width:"55"}),e(r,{prop:"tableName",label:"表名称","show-overflow-tooltip":!0}),e(r,{prop:"tableComment",label:"表描述","show-overflow-tooltip":!0}),e(r,{prop:"createTime",label:"创建时间"}),e(r,{prop:"updateTime",label:"更新时间"})]),_:1},8,["data"]),L(e(q,{total:o(b),page:o(a).pageNum,"onUpdate:page":t[2]||(t[2]=i=>o(a).pageNum=i),limit:o(a).pageSize,"onUpdate:limit":t[3]||(t[3]=i=>o(a).pageSize=i),onPagination:_},null,8,["total","page","limit"]),[[P,o(b)>0]])]),_:1})]),_:1},8,["modelValue"])}}};export{O as default};
