import{_ as R,a as I,d as U,r as f,e as i,c as w,h as e,i as t,s as z,t as B,j as o,k as y,o as h,f as d,l as K,v as N,m as S,p as T,q as M,x as $,E as j}from"./index-DDqcBwar.js";const x=c=>(T("data-v-3ff66167"),c=c(),M(),c),A={class:"register"},F=x(()=>d("h3",{class:"title"},"共享车位管理平台",-1)),H={class:"register-code"},L=["src"],D={key:0},G={key:1},J={style:{float:"right"}},O=x(()=>d("div",{class:"el-register-footer"},[d("span",null,"Copyright © 2018-2025 ruoyi.vip All Rights Reserved.")],-1)),Q={__name:"register",setup(c){const b=I(),{proxy:k}=U(),s=f({username:"",password:"",confirmPassword:"",code:"",uuid:""}),C={username:[{required:!0,trigger:"blur",message:"请输入您的账号"},{min:2,max:20,message:"用户账号长度必须介于 2 和 20 之间",trigger:"blur"}],password:[{required:!0,trigger:"blur",message:"请输入您的密码"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"},{pattern:/^[^<>"'|\\]+$/,message:`不能包含非法字符：< > " ' \\ |`,trigger:"blur"}],confirmPassword:[{required:!0,trigger:"blur",message:"请再次输入您的密码"},{required:!0,validator:(l,r,a)=>{s.value.password!==r?a(new Error("两次输入的密码不一致")):a()},trigger:"blur"}],code:[{required:!0,trigger:"change",message:"请输入验证码"}]},V=f(""),p=f(!1),m=f(!0);function g(){k.$refs.registerRef.validate(l=>{l&&(p.value=!0,$(s.value).then(r=>{const a=s.value.username;j.alert("<font color='red'>恭喜你，您的账号 "+a+" 注册成功！</font>","系统提示",{dangerouslyUseHTMLString:!0,type:"success"}).then(()=>{b.push("/login")}).catch(()=>{})}).catch(()=>{p.value=!1,m&&v()}))})}function v(){S().then(l=>{m.value=l.captchaEnabled===void 0?!0:l.captchaEnabled,m.value&&(V.value="data:image/gif;base64,"+l.img,s.value.uuid=l.uuid)})}return v(),(l,r)=>{const a=i("svg-icon"),_=i("el-input"),u=i("el-form-item"),q=i("el-button"),E=i("router-link"),P=i("el-form");return h(),w("div",A,[e(P,{ref:"registerRef",model:o(s),rules:C,class:"register-form"},{default:t(()=>[F,e(u,{prop:"username"},{default:t(()=>[e(_,{modelValue:o(s).username,"onUpdate:modelValue":r[0]||(r[0]=n=>o(s).username=n),type:"text",size:"large","auto-complete":"off",placeholder:"账号"},{prefix:t(()=>[e(a,{"icon-class":"user",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),e(u,{prop:"password"},{default:t(()=>[e(_,{modelValue:o(s).password,"onUpdate:modelValue":r[1]||(r[1]=n=>o(s).password=n),type:"password",size:"large","auto-complete":"off",placeholder:"密码",onKeyup:y(g,["enter"])},{prefix:t(()=>[e(a,{"icon-class":"password",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),e(u,{prop:"confirmPassword"},{default:t(()=>[e(_,{modelValue:o(s).confirmPassword,"onUpdate:modelValue":r[2]||(r[2]=n=>o(s).confirmPassword=n),type:"password",size:"large","auto-complete":"off",placeholder:"确认密码",onKeyup:y(g,["enter"])},{prefix:t(()=>[e(a,{"icon-class":"password",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"])]),_:1}),o(m)?(h(),z(u,{key:0,prop:"code"},{default:t(()=>[e(_,{size:"large",modelValue:o(s).code,"onUpdate:modelValue":r[3]||(r[3]=n=>o(s).code=n),"auto-complete":"off",placeholder:"验证码",style:{width:"63%"},onKeyup:y(g,["enter"])},{prefix:t(()=>[e(a,{"icon-class":"validCode",class:"el-input__icon input-icon"})]),_:1},8,["modelValue"]),d("div",H,[d("img",{src:o(V),onClick:v,class:"register-code-img"},null,8,L)])]),_:1})):B("",!0),e(u,{style:{width:"100%"}},{default:t(()=>[e(q,{loading:o(p),size:"large",type:"primary",style:{width:"100%"},onClick:K(g,["prevent"])},{default:t(()=>[o(p)?(h(),w("span",G,"注 册 中...")):(h(),w("span",D,"注 册"))]),_:1},8,["loading"]),d("div",J,[e(E,{class:"link-type",to:"/login"},{default:t(()=>[N("使用已有账户登录")]),_:1})])]),_:1})]),_:1},8,["model"]),O])}}},Y=R(Q,[["__scopeId","data-v-3ff66167"]]);export{Y as default};
