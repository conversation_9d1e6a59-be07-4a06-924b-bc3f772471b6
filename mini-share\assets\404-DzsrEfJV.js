import{_,y as i,e as l,c as d,f as s,z as n,h as r,A as p,j as h,i as u,v as m,p as v,q as b,o as f}from"./index-DDqcBwar.js";const g="/assets/404-N4aRkdWY.png",a="/assets/404_cloud-CPexjtDj.png",e=t=>(v("data-v-5945313b"),t=t(),b(),t),x={class:"wscn-http404-container"},k={class:"wscn-http404"},N=n('<div class="pic-404" data-v-5945313b><img class="pic-404__parent" src="'+g+'" alt="404" data-v-5945313b><img class="pic-404__child left" src="'+a+'" alt="404" data-v-5945313b><img class="pic-404__child mid" src="'+a+'" alt="404" data-v-5945313b><img class="pic-404__child right" src="'+a+'" alt="404" data-v-5945313b></div>',1),S={class:"bullshit"},w=e(()=>s("div",{class:"bullshit__oops"}," 404错误! ",-1)),I={class:"bullshit__headline"},V=e(()=>s("div",{class:"bullshit__info"}," 对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。 ",-1)),j={__name:"404",setup(t){let c=i(()=>"找不到网页！");return(B,C)=>{const o=l("router-link");return f(),d("div",x,[s("div",k,[N,s("div",S,[w,s("div",I,p(h(c)),1),V,r(o,{to:"/index",class:"bullshit__return-home"},{default:u(()=>[m(" 返回首页 ")]),_:1})])])])}}},D=_(j,[["__scopeId","data-v-5945313b"]]);export{D as default};
