import{T as C,r as N,d as M,e as n,c,o as a,h as i,i as d,f as l,t as _,j as s,A as t,v as u,M as v,J as T,K as B}from"./index-DDqcBwar.js";function J(){return C({url:"/monitor/server",method:"get"})}const V={class:"app-container"},A=l("span",{style:{"vertical-align":"middle"}},"CPU",-1),G={class:"el-table el-table--enable-row-hover el-table--medium"},I={cellspacing:"0",style:{width:"100%"}},L=l("thead",null,[l("tr",null,[l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"属性")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"值")])])],-1),D=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"核心数")],-1),P={class:"el-table__cell is-leaf"},S={key:0,class:"cell"},$=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"用户使用率")],-1),z={class:"el-table__cell is-leaf"},E={key:0,class:"cell"},K=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"系统使用率")],-1),U={class:"el-table__cell is-leaf"},q={key:0,class:"cell"},H=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"当前空闲率")],-1),O={class:"el-table__cell is-leaf"},Q={key:0,class:"cell"},R=l("span",{style:{"vertical-align":"middle"}},"内存",-1),W={class:"el-table el-table--enable-row-hover el-table--medium"},X={cellspacing:"0",style:{width:"100%"}},Y=l("thead",null,[l("tr",null,[l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"属性")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"内存")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"JVM")])])],-1),Z=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"总内存")],-1),F={class:"el-table__cell is-leaf"},ll={key:0,class:"cell"},el={class:"el-table__cell is-leaf"},sl={key:0,class:"cell"},tl=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"已用内存")],-1),cl={class:"el-table__cell is-leaf"},al={key:0,class:"cell"},_l={class:"el-table__cell is-leaf"},il={key:0,class:"cell"},dl=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"剩余内存")],-1),ol={class:"el-table__cell is-leaf"},nl={key:0,class:"cell"},hl={class:"el-table__cell is-leaf"},rl={key:0,class:"cell"},ul=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"使用率")],-1),vl={class:"el-table__cell is-leaf"},ml={class:"el-table__cell is-leaf"},bl=l("span",{style:{"vertical-align":"middle"}},"服务器信息",-1),fl={class:"el-table el-table--enable-row-hover el-table--medium"},pl={cellspacing:"0",style:{width:"100%"}},yl=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"服务器名称")],-1),gl={class:"el-table__cell is-leaf"},kl={key:0,class:"cell"},jl=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"操作系统")],-1),wl={class:"el-table__cell is-leaf"},xl={key:0,class:"cell"},Cl=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"服务器IP")],-1),Nl={class:"el-table__cell is-leaf"},Ml={key:0,class:"cell"},Tl=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"系统架构")],-1),Bl={class:"el-table__cell is-leaf"},Jl={key:0,class:"cell"},Vl=l("span",{style:{"vertical-align":"middle"}},"Java虚拟机信息",-1),Al={class:"el-table el-table--enable-row-hover el-table--medium"},Gl={cellspacing:"0",style:{width:"100%","table-layout":"fixed"}},Il=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"Java名称")],-1),Ll={class:"el-table__cell is-leaf"},Dl={key:0,class:"cell"},Pl=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"Java版本")],-1),Sl={class:"el-table__cell is-leaf"},$l={key:0,class:"cell"},zl=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"启动时间")],-1),El={class:"el-table__cell is-leaf"},Kl={key:0,class:"cell"},Ul=l("td",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"运行时长")],-1),ql={class:"el-table__cell is-leaf"},Hl={key:0,class:"cell"},Ol=l("td",{colspan:"1",class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"安装路径")],-1),Ql={colspan:"3",class:"el-table__cell is-leaf"},Rl={key:0,class:"cell"},Wl=l("td",{colspan:"1",class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"项目路径")],-1),Xl={colspan:"3",class:"el-table__cell is-leaf"},Yl={key:0,class:"cell"},Zl=l("td",{colspan:"1",class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"运行参数")],-1),Fl={colspan:"3",class:"el-table__cell is-leaf"},le={key:0,class:"cell"},ee=l("span",{style:{"vertical-align":"middle"}},"磁盘状态",-1),se={class:"el-table el-table--enable-row-hover el-table--medium"},te={cellspacing:"0",style:{width:"100%"}},ce=l("thead",null,[l("tr",null,[l("th",{class:"el-table__cell el-table__cell is-leaf"},[l("div",{class:"cell"},"盘符路径")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"文件系统")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"盘符类型")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"总大小")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"可用大小")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"已用大小")]),l("th",{class:"el-table__cell is-leaf"},[l("div",{class:"cell"},"已用百分比")])])],-1),ae={key:0},_e={class:"el-table__cell is-leaf"},ie={class:"cell"},de={class:"el-table__cell is-leaf"},oe={class:"cell"},ne={class:"el-table__cell is-leaf"},he={class:"cell"},re={class:"el-table__cell is-leaf"},ue={class:"cell"},ve={class:"el-table__cell is-leaf"},me={class:"cell"},be={class:"el-table__cell is-leaf"},fe={class:"cell"},pe={class:"el-table__cell is-leaf"},je={__name:"index",setup(ye){const e=N([]),{proxy:m}=M();function f(){m.$modal.loading("正在加载服务监控数据，请稍候！"),J().then(b=>{e.value=b.data,m.$modal.closeLoading()})}return f(),(b,ge)=>{const p=n("Cpu"),h=n("el-card"),r=n("el-col"),y=n("Tickets"),g=n("Monitor"),k=n("CoffeeCup"),j=n("MessageBox"),w=n("el-row");return a(),c("div",V,[i(w,null,{default:d(()=>[i(r,{span:12,class:"card-box"},{default:d(()=>[i(h,null,{header:d(()=>[i(p,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),u(),A]),default:d(()=>[l("div",G,[l("table",I,[L,l("tbody",null,[l("tr",null,[D,l("td",P,[s(e).cpu?(a(),c("div",S,t(s(e).cpu.cpuNum),1)):_("",!0)])]),l("tr",null,[$,l("td",z,[s(e).cpu?(a(),c("div",E,t(s(e).cpu.used)+"%",1)):_("",!0)])]),l("tr",null,[K,l("td",U,[s(e).cpu?(a(),c("div",q,t(s(e).cpu.sys)+"%",1)):_("",!0)])]),l("tr",null,[H,l("td",O,[s(e).cpu?(a(),c("div",Q,t(s(e).cpu.free)+"%",1)):_("",!0)])])])])])]),_:1})]),_:1}),i(r,{span:12,class:"card-box"},{default:d(()=>[i(h,null,{header:d(()=>[i(y,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),u(),R]),default:d(()=>[l("div",W,[l("table",X,[Y,l("tbody",null,[l("tr",null,[Z,l("td",F,[s(e).mem?(a(),c("div",ll,t(s(e).mem.total)+"G",1)):_("",!0)]),l("td",el,[s(e).jvm?(a(),c("div",sl,t(s(e).jvm.total)+"M",1)):_("",!0)])]),l("tr",null,[tl,l("td",cl,[s(e).mem?(a(),c("div",al,t(s(e).mem.used)+"G",1)):_("",!0)]),l("td",_l,[s(e).jvm?(a(),c("div",il,t(s(e).jvm.used)+"M",1)):_("",!0)])]),l("tr",null,[dl,l("td",ol,[s(e).mem?(a(),c("div",nl,t(s(e).mem.free)+"G",1)):_("",!0)]),l("td",hl,[s(e).jvm?(a(),c("div",rl,t(s(e).jvm.free)+"M",1)):_("",!0)])]),l("tr",null,[ul,l("td",vl,[s(e).mem?(a(),c("div",{key:0,class:v(["cell",{"text-danger":s(e).mem.usage>80}])},t(s(e).mem.usage)+"%",3)):_("",!0)]),l("td",ml,[s(e).jvm?(a(),c("div",{key:0,class:v(["cell",{"text-danger":s(e).jvm.usage>80}])},t(s(e).jvm.usage)+"%",3)):_("",!0)])])])])])]),_:1})]),_:1}),i(r,{span:24,class:"card-box"},{default:d(()=>[i(h,null,{header:d(()=>[i(g,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),u(),bl]),default:d(()=>[l("div",fl,[l("table",pl,[l("tbody",null,[l("tr",null,[yl,l("td",gl,[s(e).sys?(a(),c("div",kl,t(s(e).sys.computerName),1)):_("",!0)]),jl,l("td",wl,[s(e).sys?(a(),c("div",xl,t(s(e).sys.osName),1)):_("",!0)])]),l("tr",null,[Cl,l("td",Nl,[s(e).sys?(a(),c("div",Ml,t(s(e).sys.computerIp),1)):_("",!0)]),Tl,l("td",Bl,[s(e).sys?(a(),c("div",Jl,t(s(e).sys.osArch),1)):_("",!0)])])])])])]),_:1})]),_:1}),i(r,{span:24,class:"card-box"},{default:d(()=>[i(h,null,{header:d(()=>[i(k,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),u(),Vl]),default:d(()=>[l("div",Al,[l("table",Gl,[l("tbody",null,[l("tr",null,[Il,l("td",Ll,[s(e).jvm?(a(),c("div",Dl,t(s(e).jvm.name),1)):_("",!0)]),Pl,l("td",Sl,[s(e).jvm?(a(),c("div",$l,t(s(e).jvm.version),1)):_("",!0)])]),l("tr",null,[zl,l("td",El,[s(e).jvm?(a(),c("div",Kl,t(s(e).jvm.startTime),1)):_("",!0)]),Ul,l("td",ql,[s(e).jvm?(a(),c("div",Hl,t(s(e).jvm.runTime),1)):_("",!0)])]),l("tr",null,[Ol,l("td",Ql,[s(e).jvm?(a(),c("div",Rl,t(s(e).jvm.home),1)):_("",!0)])]),l("tr",null,[Wl,l("td",Xl,[s(e).sys?(a(),c("div",Yl,t(s(e).sys.userDir),1)):_("",!0)])]),l("tr",null,[Zl,l("td",Fl,[s(e).jvm?(a(),c("div",le,t(s(e).jvm.inputArgs),1)):_("",!0)])])])])])]),_:1})]),_:1}),i(r,{span:24,class:"card-box"},{default:d(()=>[i(h,null,{header:d(()=>[i(j,{style:{width:"1em",height:"1em","vertical-align":"middle"}}),u(),ee]),default:d(()=>[l("div",se,[l("table",te,[ce,s(e).sysFiles?(a(),c("tbody",ae,[(a(!0),c(T,null,B(s(e).sysFiles,(o,x)=>(a(),c("tr",{key:x},[l("td",_e,[l("div",ie,t(o.dirName),1)]),l("td",de,[l("div",oe,t(o.sysTypeName),1)]),l("td",ne,[l("div",he,t(o.typeName),1)]),l("td",re,[l("div",ue,t(o.total),1)]),l("td",ve,[l("div",me,t(o.free),1)]),l("td",be,[l("div",fe,t(o.used),1)]),l("td",pe,[l("div",{class:v(["cell",{"text-danger":o.usage>80}])},t(o.usage)+"%",3)])]))),128))])):_("",!0)])])]),_:1})]),_:1})]),_:1})])}}};export{je as default};
